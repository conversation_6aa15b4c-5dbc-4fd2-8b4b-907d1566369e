/**
 * React Hook for Professional ID Generation
 * Provides easy access to professional ID generation in React components
 */

import { useState, useCallback, useEffect } from 'react'
import { ClientProfessionalIdService } from '@/lib/services/client-professional-id-service'

interface UseIdGenerationResult {
  generateCustomerId: () => Promise<string>
  generateInventoryItemCode: (category: string, metalType: string, purity: string) => Promise<{ itemCode: string; barcode: string }>
  generateSaleIds: () => Promise<{ saleId: string; invoiceNumber: string }>
  generateInvoiceNumber: () => Promise<string>
  generateRepairJobNumber: () => Promise<string>
  generateSchemeNumber: () => Promise<string>
  generatePurchaseOrderNumber: () => Promise<string>
  generateSupplierCode: () => Promise<string>
  generateExchangeNumber: () => Promise<string>
  generateReceiptNumber: () => Promise<string>
  validateId: (type: string, id: string) => Promise<{ isValid: boolean; parsed?: any }>
  getCurrentSequences: () => Promise<Record<string, number>>
  getIdGenerationReport: () => Promise<any>
  validateSequenceIntegrity: () => Promise<any>
  isLoading: boolean
  error: string | null
}

export function useProfessionalIds(): UseIdGenerationResult {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const idService = new ClientProfessionalIdService()

  const handleAsyncOperation = useCallback(async <T>(operation: () => Promise<T>): Promise<T> => {
    setIsLoading(true)
    setError(null)
    try {
      const result = await operation()
      return result
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred'
      setError(errorMessage)
      throw err
    } finally {
      setIsLoading(false)
    }
  }, [])

  const generateCustomerId = useCallback(async (): Promise<string> => {
    return handleAsyncOperation(() => idService.generateCustomerId())
  }, [handleAsyncOperation, idService])

  const generateInventoryItemCode = useCallback(async (
    category: string, 
    metalType: string, 
    purity: string
  ): Promise<{ itemCode: string; barcode: string }> => {
    return handleAsyncOperation(() => idService.generateInventoryItemCode(category, metalType, purity))
  }, [handleAsyncOperation, idService])

  const generateSaleIds = useCallback(async (): Promise<{ saleId: string; invoiceNumber: string }> => {
    return handleAsyncOperation(() => idService.generateSaleIds())
  }, [handleAsyncOperation, idService])

  const generateInvoiceNumber = useCallback(async (): Promise<string> => {
    return handleAsyncOperation(() => idService.generateInvoiceNumber())
  }, [handleAsyncOperation, idService])

  const generateRepairJobNumber = useCallback(async (): Promise<string> => {
    return handleAsyncOperation(() => idService.generateRepairJobNumber())
  }, [handleAsyncOperation, idService])

  const generateSchemeNumber = useCallback(async (): Promise<string> => {
    return handleAsyncOperation(() => idService.generateSchemeNumber())
  }, [handleAsyncOperation, idService])

  const generatePurchaseOrderNumber = useCallback(async (): Promise<string> => {
    return handleAsyncOperation(() => idService.generatePurchaseOrderNumber())
  }, [handleAsyncOperation, idService])

  const generateSupplierCode = useCallback(async (): Promise<string> => {
    return handleAsyncOperation(() => idService.generateSupplierCode())
  }, [handleAsyncOperation, idService])

  const generateExchangeNumber = useCallback(async (): Promise<string> => {
    return handleAsyncOperation(() => idService.generateExchangeNumber())
  }, [handleAsyncOperation, idService])

  const generateReceiptNumber = useCallback(async (): Promise<string> => {
    return handleAsyncOperation(() => idService.generateReceiptNumber())
  }, [handleAsyncOperation, idService])

  const validateId = useCallback(async (type: string, id: string): Promise<{ isValid: boolean; parsed?: any }> => {
    return handleAsyncOperation(() => idService.validateId(type, id))
  }, [handleAsyncOperation, idService])

  const getCurrentSequences = useCallback(async (): Promise<Record<string, number>> => {
    return handleAsyncOperation(() => idService.getCurrentSequences())
  }, [handleAsyncOperation, idService])

  const getIdGenerationReport = useCallback(async (): Promise<any> => {
    return handleAsyncOperation(() => idService.getIdGenerationReport())
  }, [handleAsyncOperation, idService])

  const validateSequenceIntegrity = useCallback(async (): Promise<any> => {
    return handleAsyncOperation(() => idService.validateSequenceIntegrity())
  }, [handleAsyncOperation, idService])

  return {
    generateCustomerId,
    generateInventoryItemCode,
    generateSaleIds,
    generateInvoiceNumber,
    generateRepairJobNumber,
    generateSchemeNumber,
    generatePurchaseOrderNumber,
    generateSupplierCode,
    generateExchangeNumber,
    generateReceiptNumber,
    validateId,
    getCurrentSequences,
    getIdGenerationReport,
    validateSequenceIntegrity,
    isLoading,
    error
  }
}

/**
 * Hook for ID previews (client-side only, no API calls)
 */
export function useIdPreviews() {
  // Import client-side utilities dynamically to avoid server-side issues
  const [clientUtils, setClientUtils] = useState<any>(null)

  useEffect(() => {
    import('@/lib/utils/client-id-generator').then(setClientUtils)
  }, [])

  if (!clientUtils) {
    return {
      generateCustomerIdPreview: () => 'CUST-YYYY-XXXXXX',
      generateInventoryItemCodePreview: () => 'XXXX-YYYY-XXXX',
      generateBarcodePreview: () => 'SJXXXXXXXXXX',
      generateInvoiceNumberPreview: () => 'INV-FY-XXXXXX',
      generateSaleIdPreview: () => 'SALE-YYYY-XXXXXX',
      generateRepairJobNumberPreview: () => 'REP-FY-XXXXXX',
      generateSchemeNumberPreview: () => 'SCH-FY-XXXXXX',
      generatePurchaseOrderNumberPreview: () => 'PO-FY-XXXXXX',
      generateSupplierCodePreview: () => 'SUPP-YYYY-XXXX',
      generateExchangeNumberPreview: () => 'EXG-FY-XXXXXX',
      generateReceiptNumberPreview: () => 'RCP-FY-XXXXXX',
      getHSNCode: () => '71131900',
      formatIdForDisplay: (id: string) => id,
      validateCustomerId: () => false,
      validateInvoiceNumber: () => false,
      validateRepairJobNumber: () => false,
      validateInventoryItemCode: () => false,
      parseInventoryItemCode: () => ({ category: '', metal: '', purity: '', year: '', sequence: '', isValid: false })
    }
  }

  return {
    generateCustomerIdPreview: clientUtils.generateCustomerIdPreview,
    generateInventoryItemCodePreview: clientUtils.generateInventoryItemCodePreview,
    generateBarcodePreview: clientUtils.generateBarcodePreview,
    generateInvoiceNumberPreview: clientUtils.generateInvoiceNumberPreview,
    generateSaleIdPreview: clientUtils.generateSaleIdPreview,
    generateRepairJobNumberPreview: clientUtils.generateRepairJobNumberPreview,
    generateSchemeNumberPreview: clientUtils.generateSchemeNumberPreview,
    generatePurchaseOrderNumberPreview: clientUtils.generatePurchaseOrderNumberPreview,
    generateSupplierCodePreview: clientUtils.generateSupplierCodePreview,
    generateExchangeNumberPreview: clientUtils.generateExchangeNumberPreview,
    generateReceiptNumberPreview: clientUtils.generateReceiptNumberPreview,
    getHSNCode: clientUtils.getHSNCode,
    formatIdForDisplay: clientUtils.formatIdForDisplay,
    validateCustomerId: clientUtils.validateCustomerId,
    validateInvoiceNumber: clientUtils.validateInvoiceNumber,
    validateRepairJobNumber: clientUtils.validateRepairJobNumber,
    validateInventoryItemCode: clientUtils.validateInventoryItemCode,
    parseInventoryItemCode: clientUtils.parseInventoryItemCode
  }
}
