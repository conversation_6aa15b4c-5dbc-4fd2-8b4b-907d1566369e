-- MYSQL 8.0+ COMPATIBLE DATABASE RESET AND IMPORT
-- Version: 5.0.0 - Professional ID Standards
-- Date: January 31, 2025
-- Description: MySQL 8.0+ compatible database reset and professional data import

-- ============================================================================
-- SAFETY WARNING
-- ============================================================================
/*
⚠️  WARNING: THIS WILL COMPLETELY DELETE ALL EXISTING DATA! ⚠️

This script will:
1. Drop the entire jewellers_db database
2. Recreate it from scratch with MySQL 8.0+ compatibility
3. Create all tables with professional ID support
4. Load sample data with professional business-standard IDs

BACKUP YOUR DATA BEFORE RUNNING THIS SCRIPT!
*/

-- ============================================================================
-- STEP 1: DATABASE RESET
-- ============================================================================

-- Disable foreign key checks
SET FOREIGN_KEY_CHECKS = 0;

-- Drop existing database completely
DROP DATABASE IF EXISTS jewellers_db;

-- Create fresh database with proper charset
CREATE DATABASE jewellers_db 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- Use the new database
USE jewellers_db;

-- Set MySQL 8.0+ compatible SQL mode
SET sql_mode = 'STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION';

SELECT '✅ Database reset completed' as step_1_status;

-- ============================================================================
-- STEP 2: CREATE CORE TABLES
-- ============================================================================

-- 1. ID SEQUENCES - Professional ID sequence management
CREATE TABLE id_sequences (
  id VARCHAR(36) PRIMARY KEY,
  entity_type VARCHAR(50) NOT NULL,
  current_sequence INT NOT NULL DEFAULT 0,
  prefix VARCHAR(20),
  financial_year VARCHAR(10) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_entity_fy (entity_type, financial_year),
  INDEX idx_entity_type (entity_type),
  INDEX idx_financial_year (financial_year)
);

-- 2. BUSINESS SETTINGS
CREATE TABLE business_settings (
  id INT PRIMARY KEY AUTO_INCREMENT,
  business_name VARCHAR(255) NOT NULL DEFAULT 'Shree Jewellers',
  business_type ENUM('jewelry_store', 'manufacturer', 'wholesaler', 'retailer') DEFAULT 'jewelry_store',
  address TEXT,
  city VARCHAR(100) DEFAULT 'Mumbai',
  state VARCHAR(100) DEFAULT 'Maharashtra',
  pincode VARCHAR(20) DEFAULT '400002',
  country VARCHAR(100) DEFAULT 'India',
  phone VARCHAR(20) DEFAULT '+91 98765 43210',
  email VARCHAR(255) DEFAULT '<EMAIL>',
  website VARCHAR(255),
  gst_number VARCHAR(50) DEFAULT '27**********1Z5',
  pan_number VARCHAR(20) DEFAULT '**********',
  license_number VARCHAR(100),
  established_year YEAR DEFAULT 2020,
  cgst_rate DECIMAL(5,2) DEFAULT 1.50,
  sgst_rate DECIMAL(5,2) DEFAULT 1.50,
  igst_rate DECIMAL(5,2) DEFAULT 3.00,
  currency_symbol VARCHAR(10) DEFAULT '₹',
  currency_code VARCHAR(5) DEFAULT 'INR',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 3. USERS
CREATE TABLE users (
  id VARCHAR(36) PRIMARY KEY,
  user_code VARCHAR(50) UNIQUE,
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  phone VARCHAR(20),
  password_hash VARCHAR(255) NOT NULL,
  role ENUM('super_admin', 'admin', 'manager', 'sales_staff', 'accountant', 'technician') NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_email (email),
  INDEX idx_role (role)
);

-- 4. CUSTOMERS - Professional customer codes
CREATE TABLE customers (
  id VARCHAR(36) PRIMARY KEY,
  customer_code VARCHAR(50) UNIQUE NOT NULL,
  name VARCHAR(255) NOT NULL,
  phone VARCHAR(20) NOT NULL,
  alternate_phone VARCHAR(20),
  email VARCHAR(255),
  date_of_birth DATE,
  anniversary_date DATE,
  gender ENUM('male', 'female', 'other'),
  address TEXT,
  city VARCHAR(100),
  state VARCHAR(100),
  pincode VARCHAR(20),
  country VARCHAR(100) DEFAULT 'India',
  aadhar_number VARCHAR(20),
  pan_number VARCHAR(20),
  customer_type ENUM('regular', 'premium', 'vip') DEFAULT 'regular',
  is_active BOOLEAN DEFAULT true,
  total_purchases DECIMAL(15,2) DEFAULT 0.00,
  loyalty_points INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_customer_code (customer_code),
  INDEX idx_phone (phone)
);

-- 5. METAL RATES
CREATE TABLE metal_rates (
  id VARCHAR(36) PRIMARY KEY,
  metal_type ENUM('gold', 'silver', 'platinum', 'diamond') NOT NULL,
  purity VARCHAR(10) NOT NULL,
  rate_per_gram DECIMAL(10,2) NOT NULL,
  margin_percentage DECIMAL(5,2) DEFAULT 0.00,
  effective_date DATE NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_metal_type (metal_type),
  INDEX idx_effective_date (effective_date)
);

-- 6. INVENTORY CATEGORIES
CREATE TABLE inventory_categories (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE,
  description TEXT,
  hsn_code VARCHAR(20) DEFAULT '71131900',
  tax_rate DECIMAL(5,2) DEFAULT 3.00,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 7. INVENTORY - Professional item codes
CREATE TABLE inventory (
  id VARCHAR(50) PRIMARY KEY,
  barcode VARCHAR(100) UNIQUE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  category_id VARCHAR(36),
  metal_type ENUM('gold', 'silver', 'platinum', 'diamond', 'white_gold', 'rose_gold', 'mixed', 'other') NOT NULL,
  purity VARCHAR(10) NOT NULL,
  gross_weight DECIMAL(10,3) NOT NULL,
  stone_weight DECIMAL(10,3) DEFAULT 0.000,
  stone_details TEXT,
  stone_amount DECIMAL(12,2) DEFAULT 0.00,
  making_charges DECIMAL(12,2) NOT NULL,
  other_charges DECIMAL(12,2) DEFAULT 0.00,
  current_value DECIMAL(12,2) NOT NULL,
  selling_price DECIMAL(12,2) NOT NULL,
  mrp DECIMAL(12,2),
  stock INT DEFAULT 1,
  status ENUM('active', 'sold', 'reserved', 'repair', 'inactive') DEFAULT 'active',
  hsn_code VARCHAR(20) DEFAULT '71131900',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (category_id) REFERENCES inventory_categories(id) ON DELETE SET NULL,
  INDEX idx_barcode (barcode),
  INDEX idx_category (category_id),
  INDEX idx_metal_type (metal_type),
  INDEX idx_status (status)
);

-- 8. SALES - Professional sale and invoice numbers
CREATE TABLE sales (
  id VARCHAR(36) PRIMARY KEY,
  sale_number VARCHAR(50) UNIQUE NOT NULL,
  invoice_number VARCHAR(100) UNIQUE NOT NULL,
  customer_id VARCHAR(36),
  sale_date DATE NOT NULL,
  subtotal DECIMAL(12,2) NOT NULL,
  discount_amount DECIMAL(12,2) DEFAULT 0.00,
  cgst_amount DECIMAL(12,2) DEFAULT 0.00,
  sgst_amount DECIMAL(12,2) DEFAULT 0.00,
  total_amount DECIMAL(12,2) NOT NULL,
  paid_amount DECIMAL(12,2) NOT NULL,
  payment_method ENUM('cash', 'card', 'upi', 'bank_transfer', 'mixed') NOT NULL,
  status ENUM('pending', 'completed', 'cancelled') DEFAULT 'completed',
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
  INDEX idx_sale_number (sale_number),
  INDEX idx_invoice_number (invoice_number),
  INDEX idx_customer (customer_id),
  INDEX idx_sale_date (sale_date)
);

-- 9. SUPPLIERS - Professional supplier codes
CREATE TABLE suppliers (
  id VARCHAR(36) PRIMARY KEY,
  supplier_code VARCHAR(50) UNIQUE NOT NULL,
  name VARCHAR(255) NOT NULL,
  contact_person VARCHAR(255),
  phone VARCHAR(20),
  email VARCHAR(255),
  address TEXT,
  city VARCHAR(100),
  state VARCHAR(100),
  country VARCHAR(100) DEFAULT 'India',
  gst_number VARCHAR(50),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_supplier_code (supplier_code)
);

-- 10. SCHEMES - Professional scheme numbers
CREATE TABLE schemes (
  id VARCHAR(36) PRIMARY KEY,
  scheme_number VARCHAR(100) UNIQUE NOT NULL,
  customer_id VARCHAR(36),
  scheme_name VARCHAR(255) NOT NULL,
  scheme_type ENUM('monthly', 'advance', 'installment') NOT NULL,
  total_amount DECIMAL(12,2) NOT NULL,
  monthly_amount DECIMAL(12,2) DEFAULT 0.00,
  paid_amount DECIMAL(12,2) DEFAULT 0.00,
  duration_months INT NOT NULL,
  start_date DATE NOT NULL,
  maturity_date DATE NOT NULL,
  status ENUM('active', 'completed', 'cancelled') DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
  INDEX idx_scheme_number (scheme_number),
  INDEX idx_customer (customer_id),
  INDEX idx_status (status)
);

SELECT '✅ Tables created successfully' as step_2_status;

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

SELECT '🎉 MySQL 8.0+ compatible schema ready for data import!' as completion_status;
