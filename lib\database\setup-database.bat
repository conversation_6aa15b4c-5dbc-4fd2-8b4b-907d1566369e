@echo off
REM JEWELRY MANAGEMENT SYSTEM - DATABASE SETUP SCRIPT (Windows)
REM Version: 4.0.0
REM Date: January 31, 2025

echo ============================================================================
echo JEWELRY MANAGEMENT SYSTEM - DATABASE SETUP
echo Version: 4.0.0
echo ============================================================================
echo.

REM Database configuration
set DB_NAME=jewellers_db
set DB_USER=root
set DB_HOST=localhost
set DB_PORT=3306

echo Setting up comprehensive jewelry management database...
echo.

REM Check if MySQL is available
echo Checking MySQL installation...
mysql --version >nul 2>&1
if errorlevel 1 (
    echo Error: MySQL is not installed or not in PATH
    echo Please install MySQL and add it to your PATH
    pause
    exit /b 1
)
echo MySQL found successfully
echo.

REM Get MySQL password
echo Please enter MySQL root password:
set /p DB_PASSWORD=

REM Test MySQL connection
echo Testing MySQL connection...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% -e "SELECT 1;" >nul 2>&1
if errorlevel 1 (
    echo Error: Cannot connect to MySQL. Please check credentials.
    pause
    exit /b 1
)
echo MySQL connection successful
echo.

REM Warning about data loss
echo WARNING: This will completely reset the database!
echo All existing data in '%DB_NAME%' will be lost.
echo.
set /p CONFIRM=Do you want to continue? (yes/no): 

if not "%CONFIRM%"=="yes" (
    echo Database setup cancelled.
    pause
    exit /b 0
)

echo.
echo Starting database setup...
echo.

REM Step 1: Create database and schema
echo Step 1: Creating database and schema...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% < updated-comprehensive-schema.sql
if errorlevel 1 (
    echo Error creating database schema
    pause
    exit /b 1
)
echo Database schema created successfully
echo.

REM Step 2: Insert sample data
echo Step 2: Inserting sample data...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% < seed-sample-data.sql
if errorlevel 1 (
    echo Error inserting sample data
    pause
    exit /b 1
)
echo Sample data inserted successfully
echo.

REM Step 3: Verify setup
echo Step 3: Verifying database setup...
for /f %%i in ('mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% -D%DB_NAME% -se "SELECT COUNT(*) FROM information_schema.TABLES WHERE TABLE_SCHEMA = '%DB_NAME%';"') do set TABLE_COUNT=%%i
for /f %%i in ('mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% -D%DB_NAME% -se "SELECT COUNT(*) FROM users;"') do set USER_COUNT=%%i
for /f %%i in ('mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% -D%DB_NAME% -se "SELECT COUNT(*) FROM customers;"') do set CUSTOMER_COUNT=%%i
for /f %%i in ('mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% -D%DB_NAME% -se "SELECT COUNT(*) FROM inventory;"') do set INVENTORY_COUNT=%%i

echo Database verification completed
echo.

REM Display setup summary
echo ============================================================================
echo DATABASE SETUP COMPLETED SUCCESSFULLY!
echo ============================================================================
echo.
echo Setup Summary:
echo • Database Name: %DB_NAME%
echo • Total Tables: %TABLE_COUNT%
echo • Sample Users: %USER_COUNT%
echo • Sample Customers: %CUSTOMER_COUNT%
echo • Sample Inventory: %INVENTORY_COUNT%
echo.

echo Default Login Credentials:
echo • Email: <EMAIL>
echo • Password: admin123
echo.
echo WARNING: Change the default password immediately!
echo.

echo Database Connection Details:
echo • Host: %DB_HOST%
echo • Port: %DB_PORT%
echo • Database: %DB_NAME%
echo • User: %DB_USER%
echo.

echo Sample Data Included:
echo • 5 Users (different roles)
echo • 5 Customers (different types)
echo • 5 Inventory items (various categories)
echo • 3 Sales transactions
echo • 3 Gold schemes
echo • 3 Repair orders
echo • 3 Suppliers
echo • 3 Purchase orders
echo • 2 Exchange transactions
echo.

echo Next Steps:
echo 1. Update your application's database configuration
echo 2. Start your application server
echo 3. Access the system and change default credentials
echo 4. Configure business settings as needed
echo 5. Test all features with sample data
echo.

echo Database setup completed! You can now start using the jewelry management system.
echo ============================================================================
echo.
pause
