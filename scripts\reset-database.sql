-- RESET DATABASE - COMPLETE CLEANUP
-- Version: 5.0.0 - Professional ID Standards
-- Date: January 31, 2025
-- Description: Complete database reset for fresh professional ID import

-- ============================================================================
-- SAFETY WARNING
-- ============================================================================
/*
⚠️  WARNING: THIS WILL COMPLETELY DELETE ALL EXISTING DATA! ⚠️

This script will:
1. Drop the entire jewellers_db database
2. Recreate it from scratch
3. Prepare it for professional ID data import

BACKUP YOUR DATA BEFORE RUNNING THIS SCRIPT!

To proceed, uncomment the confirmation line below:
*/

-- SET @CONFIRM_RESET = 'YES_DELETE_ALL_DATA_AND_RESET';

-- ============================================================================
-- SAFETY CHECK
-- ============================================================================

SELECT CASE 
  WHEN @CONFIRM_RESET = 'YES_DELETE_ALL_DATA_AND_RESET' THEN 
    '🚀 Proceeding with complete database reset...'
  ELSE 
    '❌ Reset cancelled. Uncomment the confirmation line above to proceed.'
END as reset_status;

-- ============================================================================
-- DATABASE RESET
-- ============================================================================

-- Disable foreign key checks
SET FOREIGN_KEY_CHECKS = 0;

-- Drop existing database completely
DROP DATABASE IF EXISTS jewellers_db;

-- Create fresh database with proper charset
CREATE DATABASE jewellers_db 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- Use the new database
USE jewellers_db;

-- Set proper SQL mode for strict data handling (compatible with MySQL 8.0+)
SET sql_mode = 'STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION';

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- ============================================================================
-- COMPLETION MESSAGE
-- ============================================================================

SELECT 
  '✅ Database reset completed successfully!' as message,
  'jewellers_db' as database_name,
  'Ready for schema and data import' as status,
  NOW() as reset_time;

-- ============================================================================
-- NEXT STEPS
-- ============================================================================

/*
🚀 NEXT STEPS:

1. Import the updated schema:
   mysql -u root -p jewellers_db < lib/database/updated-comprehensive-schema.sql

2. Import professional sample data:
   mysql -u root -p jewellers_db < lib/database/professional-sample-data.sql

3. Verify the import:
   mysql -u root -p jewellers_db -e "SHOW TABLES; SELECT COUNT(*) FROM customers; SELECT COUNT(*) FROM inventory;"

4. Check professional ID formats:
   mysql -u root -p jewellers_db -e "SELECT customer_code, name FROM customers LIMIT 5;"
   mysql -u root -p jewellers_db -e "SELECT id, barcode, name FROM inventory LIMIT 5;"

Your database will then have:
✅ Professional customer IDs: CUST-2024-000001
✅ Professional inventory codes: RGGD22-2024-0001
✅ Professional barcodes: SJ01224000001
✅ Professional invoice numbers: INV-2024-25-000001
✅ Complete sequence management system
*/
