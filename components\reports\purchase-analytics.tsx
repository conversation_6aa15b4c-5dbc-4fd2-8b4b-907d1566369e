"use client"

import { useState, useMemo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { useStore } from "@/lib/store"
import { 
  Download, Filter, ShoppingCart, TrendingUp, TrendingDown, 
  Building, Calendar, Target, Award, BarChart3,
  PieChart, Activity, Clock, Truck, CreditCard
} from "lucide-react"

export function PurchaseAnalytics() {
  const { purchases } = useStore()
  const [activeTab, setActiveTab] = useState("overview")
  const [dateRange, setDateRange] = useState("month")
  const [supplierFilter, setSupplierFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [categoryFilter, setCategoryFilter] = useState("all")

  // Enhanced purchase analytics
  const analytics = useMemo(() => {
    // Filter purchases based on selected criteria
    const filteredPurchases = purchases.filter((purchase) => {
      // Date filtering
      const purchaseDate = new Date(purchase.date)
      const now = new Date()
      
      if (dateRange === "today") {
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
        if (purchaseDate < today) return false
      } else if (dateRange === "week") {
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        if (purchaseDate < weekAgo) return false
      } else if (dateRange === "month") {
        const monthAgo = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate())
        if (purchaseDate < monthAgo) return false
      } else if (dateRange === "year") {
        const yearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate())
        if (purchaseDate < yearAgo) return false
      }
      
      // Supplier filtering
      if (supplierFilter !== "all" && purchase.supplier !== supplierFilter) return false
      
      // Status filtering
      if (statusFilter !== "all" && purchase.status !== statusFilter) return false
      
      return true
    })

    // Basic metrics
    const totalPurchases = filteredPurchases.length
    const totalAmount = filteredPurchases.reduce((sum, purchase) => sum + (purchase.amount || 0), 0)
    const avgPurchaseValue = totalPurchases > 0 ? totalAmount / totalPurchases : 0
    
    // Status distribution
    const statusDistribution = filteredPurchases.reduce((acc, purchase) => {
      const status = purchase.status || "pending"
      acc[status] = (acc[status] || 0) + 1
      return acc
    }, {} as Record<string, number>)
    
    // Supplier performance
    const supplierPerformance = filteredPurchases.reduce((acc, purchase) => {
      const supplier = purchase.supplier || "Unknown"
      if (!acc[supplier]) {
        acc[supplier] = {
          totalOrders: 0,
          totalAmount: 0,
          avgOrderValue: 0,
          onTimeDeliveries: 0,
          qualityScore: 0,
          paymentTerms: "net_30"
        }
      }
      
      acc[supplier].totalOrders += 1
      acc[supplier].totalAmount += purchase.amount || 0
      acc[supplier].avgOrderValue = acc[supplier].totalAmount / acc[supplier].totalOrders
      
      // Mock quality and delivery metrics
      if (purchase.status === "received") {
        acc[supplier].onTimeDeliveries += Math.random() > 0.2 ? 1 : 0
        acc[supplier].qualityScore += Math.random() * 2 + 3 // 3-5 range
      }
      
      return acc
    }, {} as Record<string, any>)
    
    // Calculate averages for suppliers
    Object.keys(supplierPerformance).forEach(supplier => {
      const perf = supplierPerformance[supplier]
      const receivedOrders = filteredPurchases.filter(p => p.supplier === supplier && p.status === "received").length
      perf.onTimeDeliveryRate = receivedOrders > 0 ? (perf.onTimeDeliveries / receivedOrders) * 100 : 0
      perf.avgQualityScore = receivedOrders > 0 ? perf.qualityScore / receivedOrders : 0
    })
    
    const topSuppliers = Object.entries(supplierPerformance)
      .sort(([,a], [,b]) => b.totalAmount - a.totalAmount)
      .slice(0, 10)
    
    // Category analysis (mock data based on common jewelry categories)
    const categoryAnalysis = {
      "Gold": { orders: Math.floor(totalPurchases * 0.4), amount: totalAmount * 0.5 },
      "Silver": { orders: Math.floor(totalPurchases * 0.3), amount: totalAmount * 0.25 },
      "Diamond": { orders: Math.floor(totalPurchases * 0.2), amount: totalAmount * 0.2 },
      "Platinum": { orders: Math.floor(totalPurchases * 0.1), amount: totalAmount * 0.05 }
    }
    
    // Time-based metrics
    const avgDeliveryTime = 7.5 // Mock data - days
    const orderFulfillmentRate = 92.3 // Mock data - percentage
    
    // Cost analysis
    const totalGST = totalAmount * 0.03 // Assuming 3% average GST
    const totalWithGST = totalAmount + totalGST
    
    // Payment analysis
    const paymentMethods = {
      "bank_transfer": Math.floor(totalPurchases * 0.6),
      "cheque": Math.floor(totalPurchases * 0.25),
      "cash": Math.floor(totalPurchases * 0.1),
      "credit_card": Math.floor(totalPurchases * 0.05)
    }
    
    // Growth calculations
    const previousPeriodAmount = totalAmount * 0.88 // Mock 12% growth
    const purchaseGrowth = previousPeriodAmount > 0 ? ((totalAmount - previousPeriodAmount) / previousPeriodAmount) * 100 : 0
    
    // Pending orders analysis
    const pendingOrders = statusDistribution.pending || 0
    const overdueOrders = Math.floor(pendingOrders * 0.15) // Mock 15% overdue
    
    return {
      totalPurchases,
      totalAmount,
      avgPurchaseValue,
      statusDistribution,
      supplierPerformance,
      topSuppliers,
      categoryAnalysis,
      avgDeliveryTime,
      orderFulfillmentRate,
      totalGST,
      totalWithGST,
      paymentMethods,
      purchaseGrowth,
      pendingOrders,
      overdueOrders,
      receivedOrders: statusDistribution.received || 0,
      cancelledOrders: statusDistribution.cancelled || 0,
      costSavings: totalAmount * 0.08, // Mock 8% cost savings
      supplierCount: Object.keys(supplierPerformance).length,
    }
  }, [purchases, dateRange, supplierFilter, statusFilter, categoryFilter])

  const exportReport = () => {
    const reportData = {
      summary: analytics,
      dateRange,
      filters: { supplierFilter, statusFilter, categoryFilter },
      generatedAt: new Date().toISOString()
    }
    
    const dataStr = JSON.stringify(reportData, null, 2)
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)
    
    const exportFileDefaultName = `purchase-analytics-${new Date().toISOString().split('T')[0]}.json`
    
    const linkElement = document.createElement('a')
    linkElement.setAttribute('href', dataUri)
    linkElement.setAttribute('download', exportFileDefaultName)
    linkElement.click()
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-2xl font-bold tracking-tight">Purchase Analytics</h3>
          <p className="text-muted-foreground">Comprehensive procurement and supplier performance analytics</p>
        </div>
        <Button onClick={exportReport}>
          <Download className="h-4 w-4 mr-2" />
          Export Report
        </Button>
      </div>

      {/* Advanced Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Analytics Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="space-y-2">
              <Label>Date Range</Label>
              <Select value={dateRange} onValueChange={setDateRange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="week">This Week</SelectItem>
                  <SelectItem value="month">This Month</SelectItem>
                  <SelectItem value="year">This Year</SelectItem>
                  <SelectItem value="all">All Time</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label>Supplier</Label>
              <Select value={supplierFilter} onValueChange={setSupplierFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Suppliers" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Suppliers</SelectItem>
                  <SelectItem value="Rajesh Gold Suppliers">Rajesh Gold Suppliers</SelectItem>
                  <SelectItem value="Diamond House Pvt Ltd">Diamond House Pvt Ltd</SelectItem>
                  <SelectItem value="Silver Craft Industries">Silver Craft Industries</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Status</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="received">Received</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Category</Label>
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="gold">Gold</SelectItem>
                  <SelectItem value="silver">Silver</SelectItem>
                  <SelectItem value="diamond">Diamond</SelectItem>
                  <SelectItem value="platinum">Platinum</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>&nbsp;</Label>
              <Button 
                variant="outline" 
                onClick={() => {
                  setDateRange("month")
                  setSupplierFilter("all")
                  setStatusFilter("all")
                  setCategoryFilter("all")
                }}
                className="w-full"
              >
                Reset Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="suppliers" className="flex items-center gap-2">
            <Building className="h-4 w-4" />
            Suppliers
          </TabsTrigger>
          <TabsTrigger value="performance" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Performance
          </TabsTrigger>
          <TabsTrigger value="financial" className="flex items-center gap-2">
            <CreditCard className="h-4 w-4" />
            Financial
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Purchases</CardTitle>
                <ShoppingCart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.totalPurchases}</div>
                <p className="text-xs text-muted-foreground">
                  {analytics.purchaseGrowth > 0 ? "+" : ""}{analytics.purchaseGrowth.toFixed(1)}% from last period
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Amount</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">₹{analytics.totalAmount.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  Avg: ₹{analytics.avgPurchaseValue.toLocaleString()}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Suppliers</CardTitle>
                <Building className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.supplierCount}</div>
                <p className="text-xs text-muted-foreground">
                  {analytics.orderFulfillmentRate}% fulfillment rate
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Pending Orders</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">{analytics.pendingOrders}</div>
                <p className="text-xs text-muted-foreground">
                  {analytics.overdueOrders} overdue
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Status and Category Distribution */}
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Purchase Status Distribution</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {Object.entries(analytics.statusDistribution).map(([status, count]) => (
                  <div key={status}>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="capitalize">{status.replace('_', ' ')}</span>
                      <span>{count} orders</span>
                    </div>
                    <Progress value={(count / analytics.totalPurchases) * 100} className="h-2" />
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Category Analysis</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {Object.entries(analytics.categoryAnalysis).map(([category, data]: [string, any]) => (
                  <div key={category}>
                    <div className="flex justify-between text-sm mb-1">
                      <span>{category}</span>
                      <span>₹{data.amount.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between text-xs text-muted-foreground mb-1">
                      <span>{data.orders} orders</span>
                      <span>{((data.amount / analytics.totalAmount) * 100).toFixed(1)}%</span>
                    </div>
                    <Progress value={(data.amount / analytics.totalAmount) * 100} className="h-2" />
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Business Intelligence Cards */}
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Truck className="h-5 w-5" />
                  Delivery Performance
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600 mb-2">
                    {analytics.avgDeliveryTime}
                  </div>
                  <p className="text-sm text-muted-foreground mb-4">Average Days</p>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>On-Time Delivery</span>
                    <span className="font-medium">{analytics.orderFulfillmentRate}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Received Orders</span>
                    <span className="font-medium">{analytics.receivedOrders}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Cost Efficiency
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      ₹{analytics.costSavings.toLocaleString()}
                    </div>
                    <p className="text-sm text-muted-foreground">Cost Savings</p>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Savings Rate</span>
                      <span className="font-medium">8.0%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Avg Order Value</span>
                      <span className="font-medium">₹{analytics.avgPurchaseValue.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Payment Methods
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(analytics.paymentMethods).map(([method, count]) => (
                    <div key={method}>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="capitalize">{method.replace('_', ' ')}</span>
                        <span>{count} orders</span>
                      </div>
                      <Progress value={(count / analytics.totalPurchases) * 100} className="h-2" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Suppliers Tab */}
        <TabsContent value="suppliers" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Top Suppliers Performance</CardTitle>
              <CardDescription>Supplier performance metrics and analytics</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Supplier</TableHead>
                    <TableHead>Total Orders</TableHead>
                    <TableHead>Total Amount</TableHead>
                    <TableHead>Avg Order Value</TableHead>
                    <TableHead>On-Time Rate</TableHead>
                    <TableHead>Quality Score</TableHead>
                    <TableHead>Performance</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {analytics.topSuppliers.map(([supplier, perf]: [string, any]) => (
                    <TableRow key={supplier}>
                      <TableCell>
                        <div className="font-medium">{supplier}</div>
                      </TableCell>
                      <TableCell>{perf.totalOrders}</TableCell>
                      <TableCell className="font-medium">
                        ₹{perf.totalAmount.toLocaleString()}
                      </TableCell>
                      <TableCell>₹{perf.avgOrderValue.toLocaleString()}</TableCell>
                      <TableCell>
                        <Badge variant={perf.onTimeDeliveryRate > 80 ? "default" : perf.onTimeDeliveryRate > 60 ? "secondary" : "destructive"}>
                          {perf.onTimeDeliveryRate.toFixed(1)}%
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <span>{perf.avgQualityScore.toFixed(1)}/5</span>
                          <div className="flex">
                            {[...Array(5)].map((_, i) => (
                              <span key={i} className={`text-xs ${i < Math.floor(perf.avgQualityScore) ? 'text-yellow-400' : 'text-gray-300'}`}>
                                ★
                              </span>
                            ))}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Progress
                            value={Math.min(100, (perf.onTimeDeliveryRate + perf.avgQualityScore * 20) / 2)}
                            className="h-2 w-16"
                          />
                          <span className="text-sm">
                            {Math.min(100, Math.round((perf.onTimeDeliveryRate + perf.avgQualityScore * 20) / 2))}%
                          </span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Order Fulfillment</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Fulfillment Rate</span>
                    <span>{analytics.orderFulfillmentRate}%</span>
                  </div>
                  <Progress value={analytics.orderFulfillmentRate} className="h-2" />
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Total Orders</span>
                    <span className="font-medium">{analytics.totalPurchases}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Received Orders</span>
                    <span className="font-medium text-green-600">{analytics.receivedOrders}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Pending Orders</span>
                    <span className="font-medium text-orange-600">{analytics.pendingOrders}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Cancelled Orders</span>
                    <span className="font-medium text-red-600">{analytics.cancelledOrders}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Delivery Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600">
                      {analytics.avgDeliveryTime}
                    </div>
                    <p className="text-sm text-muted-foreground">Average Delivery Time (Days)</p>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>On-Time Deliveries</span>
                      <span className="font-medium">{analytics.orderFulfillmentRate}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Overdue Orders</span>
                      <span className="font-medium text-red-600">{analytics.overdueOrders}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Active Suppliers</span>
                      <span className="font-medium">{analytics.supplierCount}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Financial Tab */}
        <TabsContent value="financial" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Purchase Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span>Total Purchases</span>
                    <span className="font-medium">₹{analytics.totalAmount.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>GST Amount</span>
                    <span className="font-medium">₹{analytics.totalGST.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Total with GST</span>
                    <span className="font-medium">₹{analytics.totalWithGST.toLocaleString()}</span>
                  </div>
                  <div className="border-t pt-2">
                    <div className="flex justify-between font-bold">
                      <span>Cost Savings</span>
                      <span className="text-green-600">₹{analytics.costSavings.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Growth Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600 mb-2">
                    {analytics.purchaseGrowth > 0 ? '+' : ''}{analytics.purchaseGrowth.toFixed(1)}%
                  </div>
                  <p className="text-sm text-muted-foreground mb-4">Purchase Growth</p>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Current Period</span>
                      <span className="font-medium">₹{analytics.totalAmount.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Previous Period</span>
                      <span className="font-medium">₹{(analytics.totalAmount * 0.88).toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Payment Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      ₹{analytics.avgPurchaseValue.toLocaleString()}
                    </div>
                    <p className="text-sm text-muted-foreground">Average Order Value</p>
                  </div>
                  <div className="space-y-2 text-sm">
                    {Object.entries(analytics.paymentMethods).map(([method, count]) => (
                      <div key={method} className="flex justify-between">
                        <span className="capitalize">{method.replace('_', ' ')}</span>
                        <span>{count} orders</span>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
