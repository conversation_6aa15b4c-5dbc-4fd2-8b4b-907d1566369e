"use client"

import { useState, useMemo } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { useStore } from "@/lib/store"
import { 
  Download, Filter, IndianRupee, Package, TrendingDown, TrendingUp, 
  AlertTriangle, Scale, Gem, Award, BarChart3, PieChart, Activity,
  Calendar, Clock, Target, Zap
} from "lucide-react"

export function EnhancedInventoryReport() {
  const { inventory } = useStore()
  const [activeTab, setActiveTab] = useState("overview")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [metalFilter, setMetalFilter] = useState("all")
  const [stockFilter, setStockFilter] = useState("all")
  const [valueRangeFilter, setValueRangeFilter] = useState("all")
  const [searchTerm, setSearchTerm] = useState("")

  // Enhanced filtering
  const filteredInventory = useMemo(() => {
    return inventory.filter((item) => {
      if (categoryFilter !== "all" && item.category !== categoryFilter) return false
      if (metalFilter !== "all" && item.metalType !== metalFilter) return false
      if (stockFilter === "low" && (item.stock || 0) >= 5) return false
      if (stockFilter === "out" && (item.stock || 0) > 0) return false
      if (stockFilter === "high" && (item.stock || 0) < 10) return false
      
      if (valueRangeFilter !== "all") {
        const value = item.currentValue || 0
        if (valueRangeFilter === "under-50k" && value >= 50000) return false
        if (valueRangeFilter === "50k-1l" && (value < 50000 || value >= 100000)) return false
        if (valueRangeFilter === "1l-5l" && (value < 100000 || value >= 500000)) return false
        if (valueRangeFilter === "above-5l" && value < 500000) return false
      }
      
      if (searchTerm && !item.name?.toLowerCase().includes(searchTerm.toLowerCase()) && 
          !item.id?.toLowerCase().includes(searchTerm.toLowerCase())) return false
      
      return true
    })
  }, [inventory, categoryFilter, metalFilter, stockFilter, valueRangeFilter, searchTerm])

  // Enhanced analytics
  const analytics = useMemo(() => {
    const totalValue = filteredInventory.reduce((sum, item) => sum + (item.currentValue || 0) * (item.stock || 0), 0)
    const totalNetWeight = filteredInventory.reduce((sum, item) => sum + (item.netWeight || 0) * (item.stock || 0), 0)
    const totalGrossWeight = filteredInventory.reduce((sum, item) => sum + (item.grossWeight || 0) * (item.stock || 0), 0)
    const totalItems = filteredInventory.reduce((sum, item) => sum + (item.stock || 0), 0)
    const uniqueItems = filteredInventory.length
    
    // Stock analysis
    const lowStockItems = inventory.filter((item) => (item.stock || 0) < 5).length
    const outOfStockItems = inventory.filter((item) => (item.stock || 0) === 0).length
    const highValueItems = inventory.filter((item) => (item.currentValue || 0) > 100000).length
    
    // Metal analysis
    const goldItems = inventory.filter((item) => item.metalType === "gold")
    const silverItems = inventory.filter((item) => item.metalType === "silver")
    const platinumItems = inventory.filter((item) => item.metalType === "platinum")
    
    const goldValue = goldItems.reduce((sum, item) => sum + (item.currentValue || 0) * (item.stock || 0), 0)
    const silverValue = silverItems.reduce((sum, item) => sum + (item.currentValue || 0) * (item.stock || 0), 0)
    const platinumValue = platinumItems.reduce((sum, item) => sum + (item.currentValue || 0) * (item.stock || 0), 0)
    
    // Category analysis
    const categories = [...new Set(inventory.map(item => item.category).filter(Boolean))]
    const categoryAnalysis = categories.map(category => {
      const categoryItems = inventory.filter(item => item.category === category)
      const categoryValue = categoryItems.reduce((sum, item) => sum + (item.currentValue || 0) * (item.stock || 0), 0)
      const categoryWeight = categoryItems.reduce((sum, item) => sum + (item.netWeight || 0) * (item.stock || 0), 0)
      const categoryStock = categoryItems.reduce((sum, item) => sum + (item.stock || 0), 0)
      
      return {
        category,
        items: categoryItems.length,
        value: categoryValue,
        weight: categoryWeight,
        stock: categoryStock,
        avgValue: categoryValue / categoryItems.length || 0
      }
    }).sort((a, b) => b.value - a.value)

    // Age analysis (mock data - would need actual dates)
    const fastMovingItems = inventory.filter((item) => (item.stock || 0) < 3).length
    const slowMovingItems = inventory.filter((item) => (item.stock || 0) > 10).length
    
    return {
      totalValue,
      totalNetWeight,
      totalGrossWeight,
      totalItems,
      uniqueItems,
      lowStockItems,
      outOfStockItems,
      highValueItems,
      goldItems: goldItems.length,
      silverItems: silverItems.length,
      platinumItems: platinumItems.length,
      goldValue,
      silverValue,
      platinumValue,
      categoryAnalysis,
      fastMovingItems,
      slowMovingItems,
      avgItemValue: totalValue / uniqueItems || 0,
      stockTurnoverRate: 85, // Mock data
      inventoryAccuracy: 98.5 // Mock data
    }
  }, [filteredInventory, inventory])

  const getCategories = () => {
    return [...new Set(inventory.map((item) => item.category).filter(Boolean))]
  }

  const getMetalTypes = () => {
    return [...new Set(inventory.map((item) => item.metalType).filter(Boolean))]
  }

  const exportReport = () => {
    // Enhanced export functionality would go here
    const reportData = {
      summary: analytics,
      inventory: filteredInventory,
      generatedAt: new Date().toISOString()
    }
    
    const dataStr = JSON.stringify(reportData, null, 2)
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)
    
    const exportFileDefaultName = `inventory-report-${new Date().toISOString().split('T')[0]}.json`
    
    const linkElement = document.createElement('a')
    linkElement.setAttribute('href', dataUri)
    linkElement.setAttribute('download', exportFileDefaultName)
    linkElement.click()
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-2xl font-bold tracking-tight">Enhanced Inventory Analytics</h3>
          <p className="text-muted-foreground">Comprehensive jewelry inventory analysis and business intelligence</p>
        </div>
        <Button onClick={exportReport}>
          <Download className="h-4 w-4 mr-2" />
          Export Report
        </Button>
      </div>

      {/* Advanced Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Advanced Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <div className="space-y-2">
              <Label>Search</Label>
              <Input
                placeholder="Search items..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label>Category</Label>
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {getCategories().map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Metal Type</Label>
              <Select value={metalFilter} onValueChange={setMetalFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Metals" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Metals</SelectItem>
                  {getMetalTypes().map((metal) => (
                    <SelectItem key={metal} value={metal}>
                      {metal?.charAt(0).toUpperCase() + metal?.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Stock Status</Label>
              <Select value={stockFilter} onValueChange={setStockFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Stock" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Items</SelectItem>
                  <SelectItem value="high">High Stock (10+)</SelectItem>
                  <SelectItem value="low">Low Stock (&lt;5)</SelectItem>
                  <SelectItem value="out">Out of Stock</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Value Range</Label>
              <Select value={valueRangeFilter} onValueChange={setValueRangeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Values" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Values</SelectItem>
                  <SelectItem value="under-50k">Under ₹50K</SelectItem>
                  <SelectItem value="50k-1l">₹50K - ₹1L</SelectItem>
                  <SelectItem value="1l-5l">₹1L - ₹5L</SelectItem>
                  <SelectItem value="above-5l">Above ₹5L</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>&nbsp;</Label>
              <Button 
                variant="outline" 
                onClick={() => {
                  setCategoryFilter("all")
                  setMetalFilter("all")
                  setStockFilter("all")
                  setValueRangeFilter("all")
                  setSearchTerm("")
                }}
                className="w-full"
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="categories" className="flex items-center gap-2">
            <PieChart className="h-4 w-4" />
            Categories
          </TabsTrigger>
          <TabsTrigger value="metals" className="flex items-center gap-2">
            <Scale className="h-4 w-4" />
            Metals
          </TabsTrigger>
          <TabsTrigger value="performance" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Performance
          </TabsTrigger>
          <TabsTrigger value="detailed" className="flex items-center gap-2">
            <Package className="h-4 w-4" />
            Detailed
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Inventory Value</CardTitle>
                <IndianRupee className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">₹{analytics.totalValue.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  {analytics.uniqueItems} unique items, {analytics.totalItems} total pieces
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Net Weight</CardTitle>
                <Scale className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">{analytics.totalNetWeight.toFixed(1)}g</div>
                <p className="text-xs text-muted-foreground">
                  Gross: {analytics.totalGrossWeight.toFixed(1)}g
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Average Item Value</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">₹{analytics.avgItemValue.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">Per unique item</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Stock Alerts</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{analytics.lowStockItems}</div>
                <p className="text-xs text-muted-foreground">
                  {analytics.outOfStockItems} out of stock
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Business Intelligence Cards */}
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Inventory Health
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Stock Turnover Rate</span>
                    <span>{analytics.stockTurnoverRate}%</span>
                  </div>
                  <Progress value={analytics.stockTurnoverRate} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Inventory Accuracy</span>
                    <span>{analytics.inventoryAccuracy}%</span>
                  </div>
                  <Progress value={analytics.inventoryAccuracy} className="h-2" />
                </div>
                <div className="pt-2 border-t">
                  <p className="text-sm text-muted-foreground">
                    {analytics.fastMovingItems} fast-moving items
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {analytics.slowMovingItems} slow-moving items
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Gem className="h-5 w-5" />
                  High Value Items
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-purple-600 mb-2">
                  {analytics.highValueItems}
                </div>
                <p className="text-sm text-muted-foreground mb-4">
                  Items valued above ₹1 Lakh
                </p>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Premium Category</span>
                    <span className="font-medium">
                      {((analytics.highValueItems / analytics.uniqueItems) * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Quick Stats
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm">Categories</span>
                  <Badge variant="outline">{analytics.categoryAnalysis.length}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Metal Types</span>
                  <Badge variant="outline">
                    {[analytics.goldItems, analytics.silverItems, analytics.platinumItems].filter(x => x > 0).length}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Filtered Results</span>
                  <Badge variant="secondary">{filteredInventory.length}</Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Categories Tab */}
        <TabsContent value="categories" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Category Performance Analysis</CardTitle>
              <CardDescription>Detailed breakdown by jewelry categories</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Category</TableHead>
                    <TableHead>Items</TableHead>
                    <TableHead>Total Stock</TableHead>
                    <TableHead>Net Weight</TableHead>
                    <TableHead>Total Value</TableHead>
                    <TableHead>Avg. Value</TableHead>
                    <TableHead>% of Total</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {analytics.categoryAnalysis.map((category) => (
                    <TableRow key={category.category}>
                      <TableCell>
                        <Badge variant="outline">{category.category}</Badge>
                      </TableCell>
                      <TableCell>{category.items}</TableCell>
                      <TableCell>{category.stock}</TableCell>
                      <TableCell className="text-blue-600 font-medium">
                        {category.weight.toFixed(1)}g
                      </TableCell>
                      <TableCell className="font-medium">
                        ₹{category.value.toLocaleString()}
                      </TableCell>
                      <TableCell>₹{category.avgValue.toLocaleString()}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Progress
                            value={(category.value / analytics.totalValue) * 100}
                            className="h-2 w-16"
                          />
                          <span className="text-sm">
                            {((category.value / analytics.totalValue) * 100).toFixed(1)}%
                          </span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Metals Tab */}
        <TabsContent value="metals" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-yellow-500 rounded-full"></div>
                  Gold Inventory
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600 mb-2">
                  {analytics.goldItems} Items
                </div>
                <div className="text-lg font-semibold mb-4">
                  ₹{analytics.goldValue.toLocaleString()}
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>% of Total Value</span>
                    <span className="font-medium">
                      {((analytics.goldValue / analytics.totalValue) * 100).toFixed(1)}%
                    </span>
                  </div>
                  <Progress value={(analytics.goldValue / analytics.totalValue) * 100} className="h-2" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-gray-400 rounded-full"></div>
                  Silver Inventory
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-600 mb-2">
                  {analytics.silverItems} Items
                </div>
                <div className="text-lg font-semibold mb-4">
                  ₹{analytics.silverValue.toLocaleString()}
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>% of Total Value</span>
                    <span className="font-medium">
                      {((analytics.silverValue / analytics.totalValue) * 100).toFixed(1)}%
                    </span>
                  </div>
                  <Progress value={(analytics.silverValue / analytics.totalValue) * 100} className="h-2" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-gray-700 rounded-full"></div>
                  Platinum Inventory
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-700 mb-2">
                  {analytics.platinumItems} Items
                </div>
                <div className="text-lg font-semibold mb-4">
                  ₹{analytics.platinumValue.toLocaleString()}
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>% of Total Value</span>
                    <span className="font-medium">
                      {((analytics.platinumValue / analytics.totalValue) * 100).toFixed(1)}%
                    </span>
                  </div>
                  <Progress value={(analytics.platinumValue / analytics.totalValue) * 100} className="h-2" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Metal Analysis Table */}
          <Card>
            <CardHeader>
              <CardTitle>Metal Type Analysis</CardTitle>
              <CardDescription>Detailed breakdown by metal types and purities</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Metal Type</TableHead>
                    <TableHead>Purity</TableHead>
                    <TableHead>Items</TableHead>
                    <TableHead>Total Weight</TableHead>
                    <TableHead>Total Value</TableHead>
                    <TableHead>Avg. Value/g</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {getMetalTypes().map((metalType) => {
                    const metalItems = inventory.filter(item => item.metalType === metalType)
                    const purities = [...new Set(metalItems.map(item => item.purity).filter(Boolean))]

                    return purities.map((purity) => {
                      const purityItems = metalItems.filter(item => item.purity === purity)
                      const totalWeight = purityItems.reduce((sum, item) => sum + (item.netWeight || 0) * (item.stock || 0), 0)
                      const totalValue = purityItems.reduce((sum, item) => sum + (item.currentValue || 0) * (item.stock || 0), 0)
                      const avgValuePerGram = totalWeight > 0 ? totalValue / totalWeight : 0

                      return (
                        <TableRow key={`${metalType}-${purity}`}>
                          <TableCell>
                            <Badge variant="outline" className="capitalize">
                              {metalType}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant="secondary">{purity}</Badge>
                          </TableCell>
                          <TableCell>{purityItems.length}</TableCell>
                          <TableCell className="text-blue-600 font-medium">
                            {totalWeight.toFixed(1)}g
                          </TableCell>
                          <TableCell className="font-medium">
                            ₹{totalValue.toLocaleString()}
                          </TableCell>
                          <TableCell>₹{avgValuePerGram.toLocaleString()}/g</TableCell>
                        </TableRow>
                      )
                    })
                  })}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Stock Movement Analysis
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{analytics.fastMovingItems}</div>
                    <p className="text-sm text-green-700">Fast Moving</p>
                    <p className="text-xs text-muted-foreground">Stock &lt; 3</p>
                  </div>
                  <div className="text-center p-4 bg-red-50 rounded-lg">
                    <div className="text-2xl font-bold text-red-600">{analytics.slowMovingItems}</div>
                    <p className="text-sm text-red-700">Slow Moving</p>
                    <p className="text-xs text-muted-foreground">Stock &gt; 10</p>
                  </div>
                </div>

                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Inventory Turnover</span>
                      <span>{analytics.stockTurnoverRate}%</span>
                    </div>
                    <Progress value={analytics.stockTurnoverRate} className="h-2" />
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Stock Availability</span>
                      <span>{(((analytics.uniqueItems - analytics.outOfStockItems) / analytics.uniqueItems) * 100).toFixed(1)}%</span>
                    </div>
                    <Progress value={((analytics.uniqueItems - analytics.outOfStockItems) / analytics.uniqueItems) * 100} className="h-2" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="h-5 w-5" />
                  Top Performers
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">Highest Value Categories</h4>
                    <div className="space-y-2">
                      {analytics.categoryAnalysis.slice(0, 3).map((category, index) => (
                        <div key={category.category} className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="text-xs">
                              #{index + 1}
                            </Badge>
                            <span className="text-sm">{category.category}</span>
                          </div>
                          <span className="text-sm font-medium">
                            ₹{category.value.toLocaleString()}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium mb-2">Most Items</h4>
                    <div className="space-y-2">
                      {analytics.categoryAnalysis
                        .sort((a, b) => b.items - a.items)
                        .slice(0, 3)
                        .map((category, index) => (
                          <div key={category.category} className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="text-xs">
                                #{index + 1}
                              </Badge>
                              <span className="text-sm">{category.category}</span>
                            </div>
                            <span className="text-sm font-medium">
                              {category.items} items
                            </span>
                          </div>
                        ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Detailed Tab */}
        <TabsContent value="detailed" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Detailed Inventory Listing</CardTitle>
              <CardDescription>
                Complete item-wise inventory with all details ({filteredInventory.length} items shown)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Item Code</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Metal/Purity</TableHead>
                    <TableHead>Net Weight</TableHead>
                    <TableHead>Stock</TableHead>
                    <TableHead>Unit Value</TableHead>
                    <TableHead>Total Value</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Location</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredInventory.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-mono text-sm">
                        <div className="font-medium">{item.id}</div>
                        {item.hsnCode && (
                          <div className="text-xs text-muted-foreground">
                            HSN: {item.hsnCode}
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{item.name}</div>
                        {item.designNumber && (
                          <div className="text-xs text-muted-foreground">
                            Design: {item.designNumber}
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{item.category}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <Badge variant="secondary" className="capitalize">
                            {item.metalType}
                          </Badge>
                          {item.purity && (
                            <div className="text-xs text-muted-foreground mt-1">
                              {item.purity}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="text-blue-600 font-medium">
                        {(item.netWeight || 0).toFixed(3)}g
                        {item.grossWeight && (
                          <div className="text-xs text-muted-foreground">
                            Gross: {item.grossWeight.toFixed(3)}g
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            (item.stock || 0) === 0 ? "destructive" :
                            (item.stock || 0) < 5 ? "secondary" :
                            "default"
                          }
                        >
                          {item.stock || 0}
                        </Badge>
                        {item.minStockLevel && (
                          <div className="text-xs text-muted-foreground mt-1">
                            Min: {item.minStockLevel}
                          </div>
                        )}
                      </TableCell>
                      <TableCell className="font-medium">
                        ₹{(item.currentValue || 0).toLocaleString()}
                      </TableCell>
                      <TableCell className="font-bold">
                        ₹{((item.currentValue || 0) * (item.stock || 0)).toLocaleString()}
                      </TableCell>
                      <TableCell>
                        {(item.stock || 0) === 0 ? (
                          <Badge variant="destructive">Out of Stock</Badge>
                        ) : (item.stock || 0) < 5 ? (
                          <Badge variant="secondary">Low Stock</Badge>
                        ) : (item.stock || 0) > 10 ? (
                          <Badge variant="outline">High Stock</Badge>
                        ) : (
                          <Badge variant="default">In Stock</Badge>
                        )}
                        {item.bisHallmark && (
                          <div className="mt-1">
                            <Badge variant="outline" className="text-xs bg-green-50">
                              BIS
                            </Badge>
                          </div>
                        )}
                      </TableCell>
                      <TableCell className="text-sm">
                        {item.location || "Not specified"}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {filteredInventory.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  No items match the current filters
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
