/**
 * API Route for Professional ID Generation
 * Handles server-side ID generation with database sequence management
 */

import { NextRequest, NextResponse } from 'next/server'
import { ServerProfessionalIdService } from '@/lib/services/professional-id-service'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { type, ...params } = body

    const idService = new ServerProfessionalIdService()

    switch (type) {
      case 'customer':
        const customerId = await idService.generateCustomerId()
        return NextResponse.json({ success: true, id: customerId })

      case 'inventory':
        const { category, metalType, purity } = params
        if (!category || !metalType || !purity) {
          return NextResponse.json(
            { success: false, error: 'Missing required parameters: category, metalType, purity' },
            { status: 400 }
          )
        }
        const inventoryResult = await idService.generateInventoryItemCode(category, metalType, purity)
        return NextResponse.json({ success: true, ...inventoryResult })

      case 'sale':
        const saleResult = await idService.generateSaleIds()
        return NextResponse.json({ success: true, ...saleResult })

      case 'invoice':
        const invoiceNumber = await idService.generateInvoiceNumber()
        return NextResponse.json({ success: true, invoiceNumber })

      case 'repair':
        const repairJobNumber = await idService.generateRepairJobNumber()
        return NextResponse.json({ success: true, repairJobNumber })

      case 'scheme':
        const schemeNumber = await idService.generateSchemeNumber()
        return NextResponse.json({ success: true, schemeNumber })

      case 'purchase':
        const purchaseOrderNumber = await idService.generatePurchaseOrderNumber()
        return NextResponse.json({ success: true, purchaseOrderNumber })

      case 'supplier':
        const supplierCode = await idService.generateSupplierCode()
        return NextResponse.json({ success: true, supplierCode })

      case 'exchange':
        const exchangeNumber = await idService.generateExchangeNumber()
        return NextResponse.json({ success: true, exchangeNumber })

      case 'receipt':
        const receiptNumber = await idService.generateReceiptNumber()
        return NextResponse.json({ success: true, receiptNumber })

      case 'estimate':
        const estimateNumber = await idService.generateEstimateNumber()
        return NextResponse.json({ success: true, estimateNumber })

      default:
        return NextResponse.json(
          { success: false, error: `Unknown ID type: ${type}` },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Professional ID generation error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    const idService = new ServerProfessionalIdService()

    switch (action) {
      case 'sequences':
        const sequences = await idService.getCurrentSequences()
        return NextResponse.json({ success: true, sequences })

      case 'report':
        const report = await idService.getIdGenerationReport()
        return NextResponse.json({ success: true, report })

      case 'integrity':
        const integrity = await idService.validateSequenceIntegrity()
        return NextResponse.json({ success: true, integrity })

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action parameter' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Professional ID API error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
