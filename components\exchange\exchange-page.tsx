"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { DataTable } from "@/components/ui/data-table"
import { Plus, Search, Filter, Download, Eye, Edit, Trash2, FileText, RefreshCw } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ExchangeTransaction } from "@/lib/types"
import { ExchangeForm } from "./exchange-form"
import { ExchangeRateManager } from "./exchange-rate-manager"
import { ExchangeReports } from "./exchange-reports"
import { ExchangePurchaseBill } from "./exchange-purchase-bill"
import { formatCurrency, formatDate } from "@/lib/utils"
import { useStore } from "@/lib/store"
import { toast } from "sonner"

interface ExchangePageProps {
  initialTransactions?: ExchangeTransaction[]
}

export function ExchangePage({ initialTransactions = [] }: ExchangePageProps) {
  // Use store for data management
  const { isLoading } = useStore()

  const [transactions, setTransactions] = useState<ExchangeTransaction[]>(initialTransactions)
  const [isFormOpen, setIsFormOpen] = useState(false)
  const [selectedTransaction, setSelectedTransaction] = useState<ExchangeTransaction | null>(null)
  const [activeTab, setActiveTab] = useState("transactions")
  const [isBillDialogOpen, setIsBillDialogOpen] = useState(false)
  const [billTransaction, setBillTransaction] = useState<ExchangeTransaction | null>(null)

  // Data table columns
  const exchangeColumns = [
    {
      key: "transactionNumber",
      label: "Transaction #",
      render: (transaction: ExchangeTransaction) => (
        <div>
          <p className="font-medium">{transaction.transactionNumber}</p>
          <p className="text-sm text-muted-foreground">{formatDate(transaction.transactionDate)}</p>
        </div>
      ),
    },
    {
      key: "customer",
      label: "Customer",
      render: (transaction: ExchangeTransaction) => (
        <div>
          <p className="font-medium">{transaction.customer?.name || 'Walk-in Customer'}</p>
          <p className="text-sm text-muted-foreground">{transaction.customer?.phone || 'No phone'}</p>
        </div>
      ),
    },
    {
      key: "items",
      label: "Items",
      render: (transaction: ExchangeTransaction) => (
        <div>
          <p className="font-medium">{transaction.items.length} item(s)</p>
          <p className="text-sm text-muted-foreground">
            {transaction.items.map(item => item.metalType).join(", ")}
          </p>
        </div>
      ),
    },
    {
      key: "totalAmount",
      label: "Amount",
      render: (transaction: ExchangeTransaction) => (
        <div>
          <p className="font-medium">{formatCurrency(transaction.totalAmount)}</p>
          <p className="text-sm text-muted-foreground">{transaction.paymentMethod}</p>
        </div>
      ),
    },
    {
      key: "status",
      label: "Status",
      render: (transaction: ExchangeTransaction) => getStatusBadge(transaction.status),
    },
  ]

  // Calculate summary statistics
  const stats = {
    totalTransactions: transactions.length,
    totalAmount: transactions.reduce((sum, t) => sum + (t.totalAmount || 0), 0),
    pendingTransactions: transactions.filter(t => t.status === 'pending').length,
    completedTransactions: transactions.filter(t => t.status === 'completed').length,
  }

  const handleNewExchange = () => {
    setSelectedTransaction(null)
    setIsFormOpen(true)
  }

  const handleEditTransaction = (transaction: ExchangeTransaction) => {
    setSelectedTransaction(transaction)
    setIsFormOpen(true)
  }

  const handleDeleteTransaction = (transaction: ExchangeTransaction) => {
    setTransactions(prev => prev.filter(t => t.id !== transaction.id))
  }

  const handleFormClose = () => {
    setIsFormOpen(false)
    setSelectedTransaction(null)
  }

  const handleFormSuccess = (transaction: ExchangeTransaction) => {
    if (selectedTransaction) {
      // Update existing transaction
      setTransactions(prev =>
        prev.map(t => t.id === transaction.id ? transaction : t)
      )
    } else {
      // Add new transaction
      setTransactions(prev => [transaction, ...prev])
    }
    handleFormClose()
  }

  const handleGenerateBill = (transaction: ExchangeTransaction) => {
    setBillTransaction(transaction)
    setIsBillDialogOpen(true)
  }

  const handleBillClose = () => {
    setIsBillDialogOpen(false)
    setBillTransaction(null)
  }

  const handleBillGenerated = (bill: any) => {
    // Update transaction with bill information
    setTransactions(prev =>
      prev.map(t =>
        t.id === bill.exchangeTransactionId
          ? { ...t, purchaseBillGenerated: true, purchaseBillNumber: bill.billNumber }
          : t
      )
    )
  }

  // Helper function for status badges
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { variant: "secondary" as const, label: "Pending" },
      completed: { variant: "default" as const, label: "Completed" },
      cancelled: { variant: "destructive" as const, label: "Cancelled" },
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Old Gold/Silver Exchange</h1>
          <p className="text-muted-foreground">
            Manage old gold and silver exchange transactions
          </p>
        </div>
        <Button onClick={handleNewExchange} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          New Exchange
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Transactions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalTransactions}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Amount</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.totalAmount)}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pendingTransactions}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.completedTransactions}</div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
          <TabsTrigger value="rates">Exchange Rates</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="transactions" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Exchange Transactions</CardTitle>
                <CardDescription>
                  Manage old gold and silver exchange transactions
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
                <Button onClick={handleNewExchange}>
                  <Plus className="h-4 w-4 mr-2" />
                  New Exchange
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {transactions.length > 0 ? (
                <DataTable
                  data={transactions}
                  columns={exchangeColumns}
                  onEdit={handleEditTransaction}
                  onDelete={handleDeleteTransaction}
                  searchPlaceholder="Search transactions..."
                  searchKey="transactionNumber"
                  exportData={transactions}
                  exportType="Exchange Transactions"
                />
              ) : (
                <div className="text-center py-12">
                  <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
                    <FileText className="h-12 w-12 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">No Exchange Transactions</h3>
                  <p className="text-muted-foreground mb-4">
                    Start by creating your first exchange transaction
                  </p>
                  <Button onClick={handleNewExchange}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Exchange Transaction
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="rates">
          <ExchangeRateManager />
        </TabsContent>

        <TabsContent value="reports">
          <ExchangeReports transactions={transactions} />
        </TabsContent>
      </Tabs>

      {/* Exchange Form Dialog */}
      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {selectedTransaction ? "Edit Exchange Transaction" : "New Exchange Transaction"}
            </DialogTitle>
          </DialogHeader>
          <ExchangeForm
            transaction={selectedTransaction}
            onSuccess={handleFormSuccess}
            onClose={handleFormClose}
          />
        </DialogContent>
      </Dialog>

      {/* Purchase Bill Dialog */}
      <Dialog open={isBillDialogOpen} onOpenChange={setIsBillDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Generate Purchase Bill</DialogTitle>
          </DialogHeader>
          {billTransaction && (
            <ExchangePurchaseBill
              exchangeTransaction={billTransaction}
              onBillGenerated={handleBillGenerated}
              onClose={handleBillClose}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
