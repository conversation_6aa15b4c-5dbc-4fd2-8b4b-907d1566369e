-- DATABASE RESET SCRIPT
-- Version: 4.0.0
-- Date: January 31, 2025
-- Description: Complete database reset and recreation script

-- ============================================================================
-- SAFETY CHECKS AND WARNINGS
-- ============================================================================

-- WARNING: This script will completely drop and recreate the database
-- Make sure you have a backup before running this script
-- Uncomment the following line only if you're sure you want to proceed

-- SET @CONFIRM_RESET = 'YES_I_WANT_TO_RESET_DATABASE';

-- Safety check
-- SELECT CASE 
--   WHEN @CONFIRM_RESET = 'YES_I_WANT_TO_RESET_DATABASE' THEN 'Proceeding with database reset...'
--   ELSE 'Database reset cancelled. Set @CONFIRM_RESET variable to proceed.'
-- END as status;

-- ============================================================================
-- DROP EXISTING DATABASE
-- ============================================================================

-- Disable foreign key checks to avoid constraint errors during drop
SET FOREIGN_KEY_CHECKS = 0;

-- Drop database if exists
DROP DATABASE IF EXISTS jewellers_db;

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- ============================================================================
-- CREATE FRESH DATABASE
-- ============================================================================

-- Create database with proper character set and collation
CREATE DATABASE jewellers_db 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- Use the new database
USE jewellers_db;

-- Set proper SQL mode and foreign key checks
SET FOREIGN_KEY_CHECKS = 1;
SET sql_mode = 'STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION';

-- ============================================================================
-- EXECUTE COMPREHENSIVE SCHEMA
-- ============================================================================

-- Note: The actual table creation is in the comprehensive schema file
-- This script should be run after executing the updated-comprehensive-schema.sql

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================

-- Verify database creation
SELECT 
  SCHEMA_NAME as database_name,
  DEFAULT_CHARACTER_SET_NAME as charset,
  DEFAULT_COLLATION_NAME as collation
FROM information_schema.SCHEMATA 
WHERE SCHEMA_NAME = 'jewellers_db';

-- Count tables created
SELECT COUNT(*) as total_tables 
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'jewellers_db';

-- List all tables
SELECT 
  TABLE_NAME as table_name,
  TABLE_TYPE as table_type,
  ENGINE as engine,
  TABLE_ROWS as estimated_rows
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'jewellers_db'
ORDER BY TABLE_NAME;

-- Verify foreign key constraints
SELECT 
  CONSTRAINT_NAME as constraint_name,
  TABLE_NAME as table_name,
  COLUMN_NAME as column_name,
  REFERENCED_TABLE_NAME as referenced_table,
  REFERENCED_COLUMN_NAME as referenced_column
FROM information_schema.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = 'jewellers_db' 
  AND REFERENCED_TABLE_NAME IS NOT NULL
ORDER BY TABLE_NAME, CONSTRAINT_NAME;

-- Verify indexes
SELECT 
  TABLE_NAME as table_name,
  INDEX_NAME as index_name,
  COLUMN_NAME as column_name,
  NON_UNIQUE as non_unique
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = 'jewellers_db'
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- ============================================================================
-- RESET COMPLETION MESSAGE
-- ============================================================================

SELECT 
  'Database reset completed successfully!' as message,
  NOW() as completed_at,
  'jewellers_db' as database_name,
  '4.0.0' as schema_version;
