"use client"

import type React from "react"
import { useState, useEffect, useMemo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"
import { Textarea } from "@/components/ui/textarea"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useStore } from "@/lib/store"
import type { Purchase, InventoryItem } from "@/lib/types"
import { 
  Plus, Trash2, ShoppingCart, Truck, FileText, 
  Calculator, CheckCircle, AlertCircle, Calendar,
  User, Building, CreditCard, Package
} from "lucide-react"

interface ComprehensivePurchaseFormProps {
  purchase?: Purchase
  onSubmit: () => void
  onCancel: () => void
}

interface Supplier {
  id: string
  name: string
  contactPerson: string
  phone: string
  email: string
  address: string
  gstNumber: string
  paymentTerms: string
  creditLimit: number
  rating: number
  category: string
}

interface PurchaseItem {
  id: string
  itemName: string
  category: string
  metalType: string
  purity: string
  grossWeight: number
  stoneWeight: number
  netWeight: number
  rate: number
  makingCharges: number
  stoneAmount: number
  quantity: number
  unitCost: number
  totalCost: number
  hsnCode: string
  gstRate: number
  gstAmount: number
  finalAmount: number
  notes: string
}

interface QualityCheck {
  id: string
  parameter: string
  expectedValue: string
  actualValue: string
  status: "pending" | "passed" | "failed"
  notes: string
  checkedBy: string
  checkedDate: string
}

export function ComprehensivePurchaseForm({ purchase, onSubmit, onCancel }: ComprehensivePurchaseFormProps) {
  const { addPurchase, updatePurchase, inventory, getMetalRate } = useStore()
  
  // Basic form data
  const [formData, setFormData] = useState({
    poNumber: "",
    supplierId: "",
    purchaseDate: "",
    expectedDeliveryDate: "",
    actualDeliveryDate: "",
    status: "draft",
    priority: "normal",
    paymentTerms: "net_30",
    paymentMethod: "bank_transfer",
    totalAmount: 0,
    gstAmount: 0,
    finalAmount: 0,
    advancePaid: 0,
    balanceAmount: 0,
    notes: "",
    internalNotes: "",
    approvedBy: "",
    approvalDate: "",
  })

  // Purchase items
  const [purchaseItems, setPurchaseItems] = useState<PurchaseItem[]>([])
  
  // Supplier data
  const [suppliers, setSuppliers] = useState<Supplier[]>([])
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(null)
  
  // Quality checks
  const [qualityChecks, setQualityChecks] = useState<QualityCheck[]>([])
  
  // Delivery tracking
  const [deliveryDetails, setDeliveryDetails] = useState({
    trackingNumber: "",
    carrier: "",
    shippingMethod: "",
    shippingCost: 0,
    insuranceAmount: 0,
    deliveryInstructions: "",
  })

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [activeTab, setActiveTab] = useState("details")

  // Initialize form
  useEffect(() => {
    if (purchase) {
      // Load existing purchase data
      setFormData({
        poNumber: purchase.poNumber || "",
        supplierId: purchase.supplierId || "",
        purchaseDate: purchase.date,
        expectedDeliveryDate: purchase.expectedDeliveryDate || "",
        actualDeliveryDate: purchase.actualDeliveryDate || "",
        status: purchase.status,
        priority: purchase.priority || "normal",
        paymentTerms: purchase.paymentTerms || "net_30",
        paymentMethod: purchase.paymentMethod || "bank_transfer",
        totalAmount: purchase.amount,
        gstAmount: purchase.gstAmount || 0,
        finalAmount: purchase.finalAmount || purchase.amount,
        advancePaid: purchase.advancePaid || 0,
        balanceAmount: purchase.balanceAmount || 0,
        notes: purchase.notes || "",
        internalNotes: purchase.internalNotes || "",
        approvedBy: purchase.approvedBy || "",
        approvalDate: purchase.approvalDate || "",
      })
      
      setPurchaseItems(purchase.purchaseItems || [])
      setQualityChecks(purchase.qualityChecks || [])
    } else {
      // Set defaults for new purchase
      const today = new Date().toISOString().split("T")[0]
      const nextWeek = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split("T")[0]
      const poNumber = `PO-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`
      
      setFormData(prev => ({
        ...prev,
        purchaseDate: today,
        expectedDeliveryDate: nextWeek,
        poNumber
      }))
      
      // Initialize default quality checks
      const defaultQualityChecks: QualityCheck[] = [
        {
          id: "qc1",
          parameter: "Weight Accuracy",
          expectedValue: "As per specification",
          actualValue: "",
          status: "pending",
          notes: "",
          checkedBy: "",
          checkedDate: ""
        },
        {
          id: "qc2",
          parameter: "Purity Verification",
          expectedValue: "As per order",
          actualValue: "",
          status: "pending",
          notes: "",
          checkedBy: "",
          checkedDate: ""
        },
        {
          id: "qc3",
          parameter: "Stone Quality",
          expectedValue: "Grade A",
          actualValue: "",
          status: "pending",
          notes: "",
          checkedBy: "",
          checkedDate: ""
        },
        {
          id: "qc4",
          parameter: "Finish Quality",
          expectedValue: "Excellent",
          actualValue: "",
          status: "pending",
          notes: "",
          checkedBy: "",
          checkedDate: ""
        }
      ]
      
      setQualityChecks(defaultQualityChecks)
    }
    
    // Load suppliers (mock data for now)
    loadSuppliers()
  }, [purchase])

  const loadSuppliers = () => {
    // Load suppliers from API or store
    // In production, this should fetch from the suppliers API endpoint
    setSuppliers([])
  }

  // Helper functions
  const handleInputChange = (field: string, value: any) => {
    setFormData({ ...formData, [field]: value })
    if (errors[field]) {
      setErrors({ ...errors, [field]: "" })
    }
  }

  const handleSupplierChange = (supplierId: string) => {
    const supplier = suppliers.find(s => s.id === supplierId)
    setSelectedSupplier(supplier || null)
    handleInputChange("supplierId", supplierId)
    
    if (supplier) {
      handleInputChange("paymentTerms", supplier.paymentTerms)
    }
  }

  const addPurchaseItem = () => {
    const newItem: PurchaseItem = {
      id: `item_${Date.now()}`,
      itemName: "",
      category: "rings",
      metalType: "gold",
      purity: "22K",
      grossWeight: 0,
      stoneWeight: 0,
      netWeight: 0,
      rate: getMetalRate("gold", "22K"),
      makingCharges: 0,
      stoneAmount: 0,
      quantity: 1,
      unitCost: 0,
      totalCost: 0,
      hsnCode: "71131900",
      gstRate: 3,
      gstAmount: 0,
      finalAmount: 0,
      notes: ""
    }
    setPurchaseItems([...purchaseItems, newItem])
  }

  const removePurchaseItem = (index: number) => {
    setPurchaseItems(purchaseItems.filter((_, i) => i !== index))
  }

  const updatePurchaseItem = (index: number, field: string, value: any) => {
    const updatedItems = [...purchaseItems]
    const item = updatedItems[index]
    
    // Handle numeric fields
    if (['grossWeight', 'stoneWeight', 'netWeight', 'rate', 'makingCharges', 'stoneAmount', 'quantity', 'unitCost', 'gstRate'].includes(field)) {
      (item as any)[field] = Number(value) || 0
    } else {
      (item as any)[field] = value
    }
    
    // Recalculate weights
    if (field === "grossWeight" || field === "stoneWeight") {
      item.netWeight = Math.max(0, item.grossWeight - item.stoneWeight)
    }
    
    // Recalculate costs
    const metalValue = item.netWeight * item.rate
    const itemCost = metalValue + item.makingCharges + item.stoneAmount
    item.unitCost = itemCost
    item.totalCost = itemCost * item.quantity
    
    // Calculate GST
    item.gstAmount = (item.totalCost * item.gstRate) / 100
    item.finalAmount = item.totalCost + item.gstAmount
    
    updatedItems[index] = item
    setPurchaseItems(updatedItems)
  }

  const updateQualityCheck = (index: number, field: string, value: any) => {
    const updatedChecks = [...qualityChecks]
    const check = updatedChecks[index]
    
    if (field === "status" && (value === "passed" || value === "failed")) {
      check.checkedDate = new Date().toISOString().split("T")[0]
      check.checkedBy = "Quality Inspector"
    }
    
    (check as any)[field] = value
    updatedChecks[index] = check
    setQualityChecks(updatedChecks)
  }

  // Get categories for jewelry
  const getCategories = () => {
    return [
      "Rings", "Necklaces", "Earrings", "Bangles", "Bracelets",
      "Pendants", "Chains", "Anklets", "Nose Pins", "Toe Rings",
      "Temple Jewelry", "Kundan", "Meenakari", "Antique"
    ]
  }

  const getPaymentTerms = () => {
    return [
      { value: "net_15", label: "Net 15 Days" },
      { value: "net_30", label: "Net 30 Days" },
      { value: "net_45", label: "Net 45 Days" },
      { value: "net_60", label: "Net 60 Days" },
      { value: "advance", label: "Advance Payment" },
      { value: "cod", label: "Cash on Delivery" }
    ]
  }

  // Calculate totals
  const calculations = useMemo(() => {
    const totalAmount = purchaseItems.reduce((sum, item) => sum + item.totalCost, 0)
    const totalGST = purchaseItems.reduce((sum, item) => sum + item.gstAmount, 0)
    const finalAmount = totalAmount + totalGST + deliveryDetails.shippingCost
    const balanceAmount = finalAmount - formData.advancePaid
    
    return {
      totalAmount,
      totalGST,
      finalAmount,
      balanceAmount,
      totalItems: purchaseItems.length,
      totalQuantity: purchaseItems.reduce((sum, item) => sum + item.quantity, 0),
      totalWeight: purchaseItems.reduce((sum, item) => sum + (item.netWeight * item.quantity), 0)
    }
  }, [purchaseItems, deliveryDetails.shippingCost, formData.advancePaid])

  // Update form totals when calculations change
  useEffect(() => {
    setFormData(prev => ({
      ...prev,
      totalAmount: calculations.totalAmount,
      gstAmount: calculations.totalGST,
      finalAmount: calculations.finalAmount,
      balanceAmount: calculations.balanceAmount
    }))
  }, [calculations])

  return (
    <form className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="details" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Details
          </TabsTrigger>
          <TabsTrigger value="items" className="flex items-center gap-2">
            <Package className="h-4 w-4" />
            Items
          </TabsTrigger>
          <TabsTrigger value="delivery" className="flex items-center gap-2">
            <Truck className="h-4 w-4" />
            Delivery
          </TabsTrigger>
          <TabsTrigger value="quality" className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4" />
            Quality
          </TabsTrigger>
          <TabsTrigger value="summary" className="flex items-center gap-2">
            <Calculator className="h-4 w-4" />
            Summary
          </TabsTrigger>
        </TabsList>

        {/* Purchase Order Information Header */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ShoppingCart className="h-5 w-5" />
              Purchase Order Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label htmlFor="supplierId">Supplier *</Label>
                <Select value={formData.supplierId || undefined} onValueChange={handleSupplierChange}>
                  <SelectTrigger className={errors.supplierId ? "border-red-500" : ""}>
                    <SelectValue placeholder="Select supplier" />
                  </SelectTrigger>
                  <SelectContent>
                    {suppliers.map((supplier) => (
                      <SelectItem key={supplier.id} value={supplier.id}>
                        <div className="flex flex-col">
                          <span>{supplier.name}</span>
                          <span className="text-xs text-muted-foreground">{supplier.category}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.supplierId && <p className="text-sm text-red-500">{errors.supplierId}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="poNumber">PO Number</Label>
                <Input
                  id="poNumber"
                  value={formData.poNumber}
                  onChange={(e) => handleInputChange("poNumber", e.target.value)}
                  placeholder="Auto-generated"
                  className="bg-gray-50"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="priority">Priority</Label>
                <Select value={formData.priority} onValueChange={(value) => handleInputChange("priority", value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="normal">Normal</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="urgent">Urgent</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select value={formData.status} onValueChange={(value) => handleInputChange("status", value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="pending_approval">Pending Approval</SelectItem>
                    <SelectItem value="approved">Approved</SelectItem>
                    <SelectItem value="sent_to_supplier">Sent to Supplier</SelectItem>
                    <SelectItem value="confirmed">Confirmed</SelectItem>
                    <SelectItem value="partially_received">Partially Received</SelectItem>
                    <SelectItem value="received">Received</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
              <div className="space-y-2">
                <Label htmlFor="purchaseDate">Purchase Date *</Label>
                <Input
                  id="purchaseDate"
                  type="date"
                  value={formData.purchaseDate}
                  onChange={(e) => handleInputChange("purchaseDate", e.target.value)}
                  required
                  className={errors.purchaseDate ? "border-red-500" : ""}
                />
                {errors.purchaseDate && <p className="text-sm text-red-500">{errors.purchaseDate}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="expectedDeliveryDate">Expected Delivery *</Label>
                <Input
                  id="expectedDeliveryDate"
                  type="date"
                  value={formData.expectedDeliveryDate}
                  onChange={(e) => handleInputChange("expectedDeliveryDate", e.target.value)}
                  required
                  className={errors.expectedDeliveryDate ? "border-red-500" : ""}
                />
                {errors.expectedDeliveryDate && <p className="text-sm text-red-500">{errors.expectedDeliveryDate}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="paymentTerms">Payment Terms</Label>
                <Select value={formData.paymentTerms} onValueChange={(value) => handleInputChange("paymentTerms", value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {getPaymentTerms().map((term) => (
                      <SelectItem key={term.value} value={term.value}>
                        {term.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Supplier Information Display */}
            {selectedSupplier && (
              <Card className="mt-4 bg-blue-50 border-blue-200">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm text-blue-900">Supplier Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <p className="text-blue-700 font-medium">Contact Person</p>
                      <p className="text-blue-900">{selectedSupplier.contactPerson}</p>
                    </div>
                    <div>
                      <p className="text-blue-700 font-medium">Phone</p>
                      <p className="text-blue-900">{selectedSupplier.phone}</p>
                    </div>
                    <div>
                      <p className="text-blue-700 font-medium">GST Number</p>
                      <p className="text-blue-900">{selectedSupplier.gstNumber}</p>
                    </div>
                    <div>
                      <p className="text-blue-700 font-medium">Credit Limit</p>
                      <p className="text-blue-900">₹{selectedSupplier.creditLimit.toLocaleString()}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </CardContent>
        </Card>

        {/* Details Tab */}
        <TabsContent value="details" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Purchase Notes</CardTitle>
                <CardDescription>General notes and requirements for this purchase</CardDescription>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={formData.notes}
                  onChange={(e) => handleInputChange("notes", e.target.value)}
                  placeholder="Special requirements, delivery instructions, etc..."
                  rows={4}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Internal Notes</CardTitle>
                <CardDescription>Internal notes for staff reference</CardDescription>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={formData.internalNotes}
                  onChange={(e) => handleInputChange("internalNotes", e.target.value)}
                  placeholder="Internal notes, approval comments, etc..."
                  rows={4}
                />
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Payment Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="paymentMethod">Payment Method</Label>
                  <Select value={formData.paymentMethod} onValueChange={(value) => handleInputChange("paymentMethod", value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                      <SelectItem value="cheque">Cheque</SelectItem>
                      <SelectItem value="cash">Cash</SelectItem>
                      <SelectItem value="credit_card">Credit Card</SelectItem>
                      <SelectItem value="letter_of_credit">Letter of Credit</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="advancePaid">Advance Paid (₹)</Label>
                  <Input
                    id="advancePaid"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.advancePaid}
                    onChange={(e) => handleInputChange("advancePaid", Number(e.target.value))}
                    placeholder="0"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Balance Amount</Label>
                  <div className="p-2 bg-red-50 border border-red-200 rounded-md">
                    <p className="font-semibold text-red-700">
                      ₹{calculations.balanceAmount.toLocaleString()}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Cost Summary */}
          <Card className="bg-green-50 border-green-200">
            <CardHeader>
              <CardTitle className="text-green-900">Cost Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <p className="text-green-700 font-medium">Total Items</p>
                  <p className="text-2xl font-bold text-green-900">{calculations.totalItems}</p>
                </div>
                <div>
                  <p className="text-green-700 font-medium">Total Quantity</p>
                  <p className="text-2xl font-bold text-green-900">{calculations.totalQuantity}</p>
                </div>
                <div>
                  <p className="text-green-700 font-medium">Total Weight</p>
                  <p className="text-2xl font-bold text-blue-600">{calculations.totalWeight.toFixed(1)}g</p>
                </div>
                <div>
                  <p className="text-green-700 font-medium">Final Amount</p>
                  <p className="text-2xl font-bold text-green-900">₹{calculations.finalAmount.toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Items Tab */}
        <TabsContent value="items" className="space-y-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Purchase Items
              </CardTitle>
              <Button type="button" onClick={addPurchaseItem} size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Item
              </Button>
            </CardHeader>
            <CardContent className="space-y-4">
              {purchaseItems.map((item, index) => (
                <Card key={item.id} className="p-4 border-l-4 border-l-green-500">
                  <div className="flex justify-between items-start mb-4">
                    <h4 className="font-medium">Item {index + 1}</h4>
                    <Button type="button" variant="ghost" size="sm" onClick={() => removePurchaseItem(index)}>
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div className="space-y-2">
                      <Label>Item Name *</Label>
                      <Input
                        value={item.itemName}
                        onChange={(e) => updatePurchaseItem(index, "itemName", e.target.value)}
                        placeholder="Gold Ring, Silver Necklace, etc."
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Category</Label>
                      <Select
                        value={item.category}
                        onValueChange={(value) => updatePurchaseItem(index, "category", value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {getCategories().map((category) => (
                            <SelectItem key={category} value={category.toLowerCase()}>
                              {category}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>HSN Code</Label>
                      <Input
                        value={item.hsnCode}
                        onChange={(e) => updatePurchaseItem(index, "hsnCode", e.target.value)}
                        placeholder="71131900"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                    <div className="space-y-2">
                      <Label>Metal Type</Label>
                      <Select
                        value={item.metalType}
                        onValueChange={(value) => updatePurchaseItem(index, "metalType", value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="gold">Gold</SelectItem>
                          <SelectItem value="silver">Silver</SelectItem>
                          <SelectItem value="platinum">Platinum</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Purity</Label>
                      <Select
                        value={item.purity}
                        onValueChange={(value) => updatePurchaseItem(index, "purity", value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="24K">24K</SelectItem>
                          <SelectItem value="22K">22K</SelectItem>
                          <SelectItem value="18K">18K</SelectItem>
                          <SelectItem value="925">925 Silver</SelectItem>
                          <SelectItem value="950">950 Platinum</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Quantity</Label>
                      <Input
                        type="number"
                        min="1"
                        value={item.quantity}
                        onChange={(e) => updatePurchaseItem(index, "quantity", e.target.value)}
                        placeholder="1"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>GST Rate (%)</Label>
                      <Select
                        value={item.gstRate.toString()}
                        onValueChange={(value) => updatePurchaseItem(index, "gstRate", value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="0">0% (Exempt)</SelectItem>
                          <SelectItem value="3">3% (Standard)</SelectItem>
                          <SelectItem value="18">18% (Other)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                    <div className="space-y-2">
                      <Label>Gross Weight (g)</Label>
                      <Input
                        type="number"
                        step="0.001"
                        min="0"
                        value={item.grossWeight}
                        onChange={(e) => updatePurchaseItem(index, "grossWeight", e.target.value)}
                        placeholder="25.500"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Stone Weight (g)</Label>
                      <Input
                        type="number"
                        step="0.001"
                        min="0"
                        value={item.stoneWeight}
                        onChange={(e) => updatePurchaseItem(index, "stoneWeight", e.target.value)}
                        placeholder="2.500"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Net Weight (g)</Label>
                      <Input
                        type="number"
                        step="0.001"
                        value={item.netWeight}
                        readOnly
                        className="bg-gray-50"
                        placeholder="Auto-calculated"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Rate (₹/g)</Label>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        value={item.rate}
                        onChange={(e) => updatePurchaseItem(index, "rate", e.target.value)}
                        placeholder="6500"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                    <div className="space-y-2">
                      <Label>Making Charges (₹)</Label>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        value={item.makingCharges}
                        onChange={(e) => updatePurchaseItem(index, "makingCharges", e.target.value)}
                        placeholder="8500"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Stone Amount (₹)</Label>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        value={item.stoneAmount}
                        onChange={(e) => updatePurchaseItem(index, "stoneAmount", e.target.value)}
                        placeholder="15000"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Unit Cost (₹)</Label>
                      <div className="p-2 bg-blue-50 border border-blue-200 rounded-md">
                        <p className="font-semibold text-blue-700">
                          ₹{item.unitCost.toLocaleString()}
                        </p>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Final Amount (₹)</Label>
                      <div className="p-2 bg-green-50 border border-green-200 rounded-md">
                        <p className="font-semibold text-green-700">
                          ₹{item.finalAmount.toLocaleString()}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Item Notes</Label>
                    <Textarea
                      value={item.notes}
                      onChange={(e) => updatePurchaseItem(index, "notes", e.target.value)}
                      placeholder="Specific notes about this item..."
                      rows={2}
                    />
                  </div>
                </Card>
              ))}

              {purchaseItems.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No items added yet. Click "Add Item" to start.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
