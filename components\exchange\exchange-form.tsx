"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Plus, Trash2, Calculator, X } from "lucide-react"
import { ExchangeTransaction, ExchangeItem, ExchangeRate, Customer } from "@/lib/types"
import { formatCurrency } from "@/lib/utils"
import {
  generateExchangeTransactionNumber,
  calculateNetWeight,
  calculateAmount,
  getCommonPurities,
  validateExchangeItem
} from "@/lib/utils/exchange-utils"

interface ExchangeFormProps {
  transaction?: ExchangeTransaction | null
  onClose: () => void
  onSuccess: (transaction: ExchangeTransaction) => void
}

interface ExchangeItemForm {
  id?: string
  itemDescription: string
  metalType: 'gold' | 'silver'
  purity: string
  grossWeight: string
  stoneWeight: string
  netWeight: number
  ratePerGram: string
  amount: number
  itemCondition: 'good' | 'fair' | 'poor'
  notes: string
}

interface ExchangeFormData {
  customerId: string
  transactionDate: string
  paymentMethod: 'cash' | 'bank_transfer' | 'adjustment'
  notes: string
  items: ExchangeItemForm[]
}

export function ExchangeForm({ transaction, onClose, onSuccess }: ExchangeFormProps) {
  const [formData, setFormData] = useState<ExchangeFormData>({
    customerId: '',
    transactionDate: new Date().toISOString().split('T')[0],
    paymentMethod: 'cash',
    notes: '',
    items: []
  })

  const [customers, setCustomers] = useState<Customer[]>([])
  const [exchangeRates, setExchangeRates] = useState<ExchangeRate[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Load customers from API
  useEffect(() => {
    // Load customers from the store or API
    // This will be populated by the actual customer data

    // Mock exchange rates
    setExchangeRates([
      { id: '1', metalType: 'gold', purity: '24K', ratePerGram: 6500, effectiveDate: '2024-01-31', isActive: true, createdAt: '2024-01-31', updatedAt: '2024-01-31' },
      { id: '2', metalType: 'gold', purity: '22K', ratePerGram: 5950, effectiveDate: '2024-01-31', isActive: true, createdAt: '2024-01-31', updatedAt: '2024-01-31' },
      { id: '3', metalType: 'gold', purity: '18K', ratePerGram: 4875, effectiveDate: '2024-01-31', isActive: true, createdAt: '2024-01-31', updatedAt: '2024-01-31' },
      { id: '4', metalType: 'gold', purity: '916', ratePerGram: 5950, effectiveDate: '2024-01-31', isActive: true, createdAt: '2024-01-31', updatedAt: '2024-01-31' },
      { id: '5', metalType: 'silver', purity: '999', ratePerGram: 85, effectiveDate: '2024-01-31', isActive: true, createdAt: '2024-01-31', updatedAt: '2024-01-31' },
      { id: '6', metalType: 'silver', purity: '925', ratePerGram: 78, effectiveDate: '2024-01-31', isActive: true, createdAt: '2024-01-31', updatedAt: '2024-01-31' }
    ])

    // Load existing transaction data if editing
    if (transaction) {
      setFormData({
        customerId: transaction.customerId || '',
        transactionDate: transaction.transactionDate,
        paymentMethod: transaction.paymentMethod,
        notes: transaction.notes || '',
        items: transaction.items.map(item => ({
          id: item.id,
          itemDescription: item.itemDescription,
          metalType: item.metalType,
          purity: item.purity,
          grossWeight: item.grossWeight.toString(),
          stoneWeight: item.stoneWeight.toString(),
          netWeight: item.netWeight,
          ratePerGram: item.ratePerGram.toString(),
          amount: item.amount,
          itemCondition: item.itemCondition,
          notes: item.notes || ''
        }))
      })
    } else {
      // Add one empty item for new transactions
      addNewItem()
    }
  }, [transaction])

  const addNewItem = () => {
    const newItem: ExchangeItemForm = {
      itemDescription: '',
      metalType: 'gold',
      purity: '22K',
      grossWeight: '',
      stoneWeight: '0',
      netWeight: 0,
      ratePerGram: '',
      amount: 0,
      itemCondition: 'good',
      notes: ''
    }
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, newItem]
    }))
  }

  const removeItem = (index: number) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index)
    }))
  }

  const updateItem = (index: number, field: keyof ExchangeItemForm, value: any) => {
    setFormData(prev => {
      const newItems = [...prev.items]
      newItems[index] = { ...newItems[index], [field]: value }
      
      // Auto-calculate net weight and amount when relevant fields change
      if (field === 'grossWeight' || field === 'stoneWeight') {
        const grossWeight = parseFloat(newItems[index].grossWeight) || 0
        const stoneWeight = parseFloat(newItems[index].stoneWeight) || 0
        newItems[index].netWeight = calculateNetWeight(grossWeight, stoneWeight)

        // Recalculate amount
        const rate = parseFloat(newItems[index].ratePerGram) || 0
        newItems[index].amount = calculateAmount(newItems[index].netWeight, rate)
      }

      if (field === 'ratePerGram') {
        const rate = parseFloat(value) || 0
        newItems[index].amount = calculateAmount(newItems[index].netWeight, rate)
      }

      // Auto-fill rate when metal type or purity changes
      if (field === 'metalType' || field === 'purity') {
        const rate = exchangeRates.find(r => 
          r.metalType === newItems[index].metalType && 
          r.purity === newItems[index].purity
        )
        if (rate) {
          newItems[index].ratePerGram = rate.ratePerGram.toString()
          newItems[index].amount = calculateAmount(newItems[index].netWeight, rate.ratePerGram)
        }
      }
      
      return { ...prev, items: newItems }
    })
  }

  const getPuritiesForMetal = (metalType: 'gold' | 'silver') => {
    // Use utility function as fallback if no rates loaded
    const ratesFromData = exchangeRates
      .filter(rate => rate.metalType === metalType)
      .map(rate => rate.purity)

    return ratesFromData.length > 0 ? ratesFromData : getCommonPurities(metalType)
  }

  const getTotalAmount = () => {
    return formData.items.reduce((sum, item) => sum + item.amount, 0)
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}
    
    if (formData.items.length === 0) {
      newErrors.items = 'At least one item is required'
    }
    
    formData.items.forEach((item, index) => {
      if (!item.itemDescription.trim()) {
        newErrors[`item_${index}_description`] = 'Item description is required'
      }
      if (!item.grossWeight || parseFloat(item.grossWeight) <= 0) {
        newErrors[`item_${index}_grossWeight`] = 'Valid gross weight is required'
      }
      if (!item.ratePerGram || parseFloat(item.ratePerGram) <= 0) {
        newErrors[`item_${index}_rate`] = 'Valid rate is required'
      }
    })
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }
    
    setIsLoading(true)
    
    try {
      // Mock API call - replace with actual implementation
      const mockTransaction: ExchangeTransaction = {
        id: transaction?.id || `exg_${Date.now()}`,
        transactionNumber: transaction?.transactionNumber || generateExchangeTransactionNumber(),
        customerId: formData.customerId || undefined,
        customer: customers.find(c => c.id === formData.customerId),
        transactionDate: formData.transactionDate,
        totalAmount: getTotalAmount(),
        paymentMethod: formData.paymentMethod,
        notes: formData.notes,
        status: 'pending',
        items: formData.items.map(item => ({
          id: item.id || `item_${Date.now()}_${Math.random()}`,
          transactionId: transaction?.id || `exg_${Date.now()}`,
          itemDescription: item.itemDescription,
          metalType: item.metalType,
          purity: item.purity,
          grossWeight: parseFloat(item.grossWeight),
          stoneWeight: parseFloat(item.stoneWeight),
          netWeight: item.netWeight,
          ratePerGram: parseFloat(item.ratePerGram),
          amount: item.amount,
          itemCondition: item.itemCondition,
          notes: item.notes,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        })),
        createdAt: transaction?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      onSuccess(mockTransaction)
    } catch (error) {
      console.error('Error saving exchange transaction:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-6xl max-h-[95vh] overflow-y-auto">
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>
              {transaction ? 'Edit Exchange Transaction' : 'New Exchange Transaction'}
            </CardTitle>
            <CardDescription>
              {transaction 
                ? `Edit transaction ${transaction.transactionNumber}`
                : 'Create a new old gold/silver exchange transaction'
              }
            </CardDescription>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>
        
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-6">
            {/* Transaction Details */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="customer">Customer (Optional)</Label>
                <Select
                  value={formData.customerId || "walk-in"}
                  onValueChange={(value) => setFormData(prev => ({
                    ...prev,
                    customerId: value === "walk-in" ? "" : value
                  }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select customer or leave blank" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="walk-in">Walk-in Customer</SelectItem>
                    {customers.map(customer => (
                      <SelectItem key={customer.id} value={customer.id}>
                        {customer.name} - {customer.phone}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="transactionDate">Transaction Date *</Label>
                <Input
                  id="transactionDate"
                  type="date"
                  value={formData.transactionDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, transactionDate: e.target.value }))}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="paymentMethod">Payment Method *</Label>
                <Select
                  value={formData.paymentMethod}
                  onValueChange={(value: 'cash' | 'bank_transfer' | 'adjustment') => 
                    setFormData(prev => ({ ...prev, paymentMethod: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="cash">Cash</SelectItem>
                    <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                    <SelectItem value="adjustment">Adjustment</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Items Section */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Exchange Items</h3>
                <Button type="button" variant="outline" onClick={addNewItem}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Item
                </Button>
              </div>
              
              {errors.items && (
                <p className="text-sm text-red-500">{errors.items}</p>
              )}
              
              <div className="space-y-4">
                {formData.items.map((item, index) => (
                  <Card key={index} className="p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-medium">Item {index + 1}</h4>
                      {formData.items.length > 1 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeItem(index)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      <div className="space-y-2 md:col-span-2">
                        <Label>Item Description *</Label>
                        <Input
                          placeholder="e.g., GOLD OLD BAR, OLD GOLD RING"
                          value={item.itemDescription}
                          onChange={(e) => updateItem(index, 'itemDescription', e.target.value)}
                          className={errors[`item_${index}_description`] ? "border-red-500" : ""}
                        />
                        {errors[`item_${index}_description`] && (
                          <p className="text-sm text-red-500">{errors[`item_${index}_description`]}</p>
                        )}
                      </div>
                      
                      <div className="space-y-2">
                        <Label>Metal Type *</Label>
                        <Select
                          value={item.metalType}
                          onValueChange={(value: 'gold' | 'silver') => updateItem(index, 'metalType', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="gold">Gold</SelectItem>
                            <SelectItem value="silver">Silver</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="space-y-2">
                        <Label>Purity *</Label>
                        <Select
                          value={item.purity}
                          onValueChange={(value) => updateItem(index, 'purity', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {getPuritiesForMetal(item.metalType).map(purity => (
                              <SelectItem key={purity} value={purity}>
                                {purity}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mt-4">
                      <div className="space-y-2">
                        <Label>Gross Weight (g) *</Label>
                        <Input
                          type="number"
                          step="0.001"
                          placeholder="0.000"
                          value={item.grossWeight}
                          onChange={(e) => updateItem(index, 'grossWeight', e.target.value)}
                          className={errors[`item_${index}_grossWeight`] ? "border-red-500" : ""}
                        />
                        {errors[`item_${index}_grossWeight`] && (
                          <p className="text-sm text-red-500">{errors[`item_${index}_grossWeight`]}</p>
                        )}
                      </div>
                      
                      <div className="space-y-2">
                        <Label>Stone Weight (g)</Label>
                        <Input
                          type="number"
                          step="0.001"
                          placeholder="0.000"
                          value={item.stoneWeight}
                          onChange={(e) => updateItem(index, 'stoneWeight', e.target.value)}
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <Label>Net Weight (g)</Label>
                        <Input
                          type="number"
                          step="0.001"
                          value={item.netWeight.toFixed(3)}
                          readOnly
                          className="bg-muted"
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <Label>Rate/gram (₹) *</Label>
                        <Input
                          type="number"
                          step="0.01"
                          placeholder="0.00"
                          value={item.ratePerGram}
                          onChange={(e) => updateItem(index, 'ratePerGram', e.target.value)}
                          className={errors[`item_${index}_rate`] ? "border-red-500" : ""}
                        />
                        {errors[`item_${index}_rate`] && (
                          <p className="text-sm text-red-500">{errors[`item_${index}_rate`]}</p>
                        )}
                      </div>
                      
                      <div className="space-y-2">
                        <Label>Amount (₹)</Label>
                        <Input
                          type="text"
                          value={formatCurrency(item.amount)}
                          readOnly
                          className="bg-muted font-medium"
                        />
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                      <div className="space-y-2">
                        <Label>Item Condition</Label>
                        <Select
                          value={item.itemCondition}
                          onValueChange={(value: 'good' | 'fair' | 'poor') => updateItem(index, 'itemCondition', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="good">Good</SelectItem>
                            <SelectItem value="fair">Fair</SelectItem>
                            <SelectItem value="poor">Poor</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="space-y-2">
                        <Label>Item Notes</Label>
                        <Input
                          placeholder="Additional notes about this item"
                          value={item.notes}
                          onChange={(e) => updateItem(index, 'notes', e.target.value)}
                        />
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>

            {/* Total Amount */}
            <Card className="p-4 bg-muted/50">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Calculator className="h-5 w-5" />
                  <span className="font-semibold">Total Exchange Amount:</span>
                </div>
                <div className="text-2xl font-bold text-primary">
                  {formatCurrency(getTotalAmount())}
                </div>
              </div>
            </Card>

            {/* Transaction Notes */}
            <div className="space-y-2">
              <Label htmlFor="notes">Transaction Notes</Label>
              <Textarea
                id="notes"
                placeholder="Additional notes about this exchange transaction"
                value={formData.notes}
                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                rows={3}
              />
            </div>

            {/* Form Actions */}
            <div className="flex justify-end gap-2 pt-4 border-t">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Saving...' : (transaction ? 'Update Transaction' : 'Create Transaction')}
              </Button>
            </div>
          </CardContent>
        </form>
      </Card>
    </div>
  )
}
