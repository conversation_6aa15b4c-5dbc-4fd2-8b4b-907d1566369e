#!/bin/bash

# PROFESSIONAL JEWELRY MANAGEMENT SYSTEM - COMPLETE IMPORT
# Version: 5.0.0 - Professional ID Standards
# Date: January 31, 2025

echo ""
echo "========================================================================"
echo "  PROFESSIONAL JEWELRY MANAGEMENT SYSTEM - DATABASE IMPORT"
echo "========================================================================"
echo ""
echo "This script will:"
echo "  1. Reset the database completely (DELETE ALL DATA)"
echo "  2. Import the updated schema with professional ID support"
echo "  3. Load sample data with professional business-standard IDs"
echo "  4. Verify the import"
echo ""
echo "WARNING: This will DELETE ALL EXISTING DATA!"
echo ""
read -p "Are you sure you want to proceed? (type YES to continue): " confirm

if [ "$confirm" != "YES" ]; then
    echo ""
    echo "Import cancelled."
    exit 1
fi

echo ""
echo "========================================================================"
echo "  STEP 1: DATABASE RESET"
echo "========================================================================"
echo ""

# Check if MySQL is accessible
if ! command -v mysql &> /dev/null; then
    echo "ERROR: MySQL is not accessible. Please ensure MySQL is installed and in PATH."
    exit 1
fi

echo "Resetting database..."
mysql -u root -p -e "SET @CONFIRM_RESET = 'YES_DELETE_ALL_DATA_AND_RESET'; source scripts/reset-database.sql;"

if [ $? -ne 0 ]; then
    echo "ERROR: Database reset failed!"
    exit 1
fi

echo "✅ Database reset completed"

echo ""
echo "========================================================================"
echo "  STEP 2: SCHEMA IMPORT"
echo "========================================================================"
echo ""

echo "Importing updated schema with professional ID support..."
mysql -u root -p jewellers_db < lib/database/updated-comprehensive-schema.sql

if [ $? -ne 0 ]; then
    echo "ERROR: Schema import failed!"
    exit 1
fi

echo "✅ Schema imported successfully"

echo ""
echo "========================================================================"
echo "  STEP 3: PROFESSIONAL DATA IMPORT"
echo "========================================================================"
echo ""

echo "Importing professional sample data..."
mysql -u root -p jewellers_db < lib/database/professional-sample-data.sql

if [ $? -ne 0 ]; then
    echo "ERROR: Data import failed!"
    exit 1
fi

echo "✅ Professional data imported successfully"

echo ""
echo "========================================================================"
echo "  STEP 4: VERIFICATION"
echo "========================================================================"
echo ""

echo "Verifying import..."

echo ""
echo "Table counts:"
mysql -u root -p jewellers_db -e "SELECT 'users' as table_name, COUNT(*) as count FROM users UNION ALL SELECT 'customers', COUNT(*) FROM customers UNION ALL SELECT 'inventory', COUNT(*) FROM inventory UNION ALL SELECT 'sales', COUNT(*) FROM sales UNION ALL SELECT 'schemes', COUNT(*) FROM schemes UNION ALL SELECT 'suppliers', COUNT(*) FROM suppliers UNION ALL SELECT 'id_sequences', COUNT(*) FROM id_sequences;"

echo ""
echo "Professional Customer IDs:"
mysql -u root -p jewellers_db -e "SELECT customer_code, name FROM customers ORDER BY customer_code LIMIT 5;"

echo ""
echo "Professional Inventory Codes:"
mysql -u root -p jewellers_db -e "SELECT id as item_code, barcode, name FROM inventory ORDER BY id LIMIT 5;"

echo ""
echo "Professional Invoice Numbers:"
mysql -u root -p jewellers_db -e "SELECT sale_number, invoice_number, total_amount FROM sales ORDER BY sale_number LIMIT 3;"

echo ""
echo "Sequence Status:"
mysql -u root -p jewellers_db -e "SELECT entity_type, current_sequence, financial_year FROM id_sequences ORDER BY entity_type;"

echo ""
echo "========================================================================"
echo "  IMPORT COMPLETED SUCCESSFULLY!"
echo "========================================================================"
echo ""
echo "Your jewelry management system now has:"
echo "  ✅ Professional customer IDs: CUST-2024-000001"
echo "  ✅ Professional inventory codes: RGGD22-2024-0001"
echo "  ✅ Professional barcodes: SJ01224000001"
echo "  ✅ Professional invoice numbers: INV-2024-25-000001"
echo "  ✅ Complete sequence management system"
echo "  ✅ Sample data for testing"
echo ""
echo "Next steps:"
echo "  1. Start your application: npm run dev"
echo "  2. Login with: <EMAIL> / admin123"
echo "  3. Test the professional ID generation"
echo "  4. Update business settings as needed"
echo ""
echo "Professional ID System is ready to use!"
echo ""
