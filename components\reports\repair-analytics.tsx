"use client"

import { useState, useMemo } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { useStore } from "@/lib/store"
import { 
  Download, Filter, Wrench, Clock, TrendingUp, TrendingDown, 
  CheckCircle, AlertCircle, Users, Calendar, Target,
  BarChart3, Activity, Award, Settings, Star
} from "lucide-react"

export function RepairAnalytics() {
  const { repairs, customers } = useStore()
  const [activeTab, setActiveTab] = useState("overview")
  const [dateRange, setDateRange] = useState("month")
  const [statusFilter, setStatusFilter] = useState("all")
  const [technicianFilter, setTechnicianFilter] = useState("all")
  const [priorityFilter, setPriorityFilter] = useState("all")

  // Enhanced repair analytics
  const analytics = useMemo(() => {
    // Filter repairs based on selected criteria
    const filteredRepairs = repairs.filter((repair) => {
      // Date filtering
      const repairDate = new Date(repair.receivedDate)
      const now = new Date()
      
      if (dateRange === "today") {
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
        if (repairDate < today) return false
      } else if (dateRange === "week") {
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        if (repairDate < weekAgo) return false
      } else if (dateRange === "month") {
        const monthAgo = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate())
        if (repairDate < monthAgo) return false
      } else if (dateRange === "year") {
        const yearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate())
        if (repairDate < yearAgo) return false
      }
      
      // Status filtering
      if (statusFilter !== "all" && repair.status !== statusFilter) return false
      
      // Technician filtering
      if (technicianFilter !== "all" && repair.assignedTechnician !== technicianFilter) return false
      
      // Priority filtering
      if (priorityFilter !== "all" && repair.priority !== priorityFilter) return false
      
      return true
    })

    // Basic metrics
    const totalRepairs = filteredRepairs.length
    const totalRevenue = filteredRepairs.reduce((sum, repair) => sum + (repair.totalActual || repair.charges || 0), 0)
    const avgRepairValue = totalRepairs > 0 ? totalRevenue / totalRepairs : 0
    
    // Status distribution
    const statusDistribution = filteredRepairs.reduce((acc, repair) => {
      const status = repair.status || "unknown"
      acc[status] = (acc[status] || 0) + 1
      return acc
    }, {} as Record<string, number>)
    
    // Priority distribution
    const priorityDistribution = filteredRepairs.reduce((acc, repair) => {
      const priority = repair.priority || "normal"
      acc[priority] = (acc[priority] || 0) + 1
      return acc
    }, {} as Record<string, number>)
    
    // Technician performance
    const technicianPerformance = filteredRepairs.reduce((acc, repair) => {
      const technician = repair.assignedTechnician || "Unassigned"
      if (!acc[technician]) {
        acc[technician] = {
          totalJobs: 0,
          completedJobs: 0,
          totalRevenue: 0,
          avgCompletionTime: 0,
          onTimeDelivery: 0
        }
      }
      
      acc[technician].totalJobs += 1
      acc[technician].totalRevenue += repair.totalActual || repair.charges || 0
      
      if (repair.status === "completed" || repair.status === "delivered") {
        acc[technician].completedJobs += 1
        
        // Calculate completion time
        if (repair.receivedDate && repair.completedDate) {
          const received = new Date(repair.receivedDate)
          const completed = new Date(repair.completedDate)
          const days = Math.ceil((completed.getTime() - received.getTime()) / (1000 * 60 * 60 * 24))
          acc[technician].avgCompletionTime += days
        }
        
        // Check on-time delivery
        if (repair.promisedDate && repair.completedDate) {
          const promised = new Date(repair.promisedDate)
          const completed = new Date(repair.completedDate)
          if (completed <= promised) {
            acc[technician].onTimeDelivery += 1
          }
        }
      }
      
      return acc
    }, {} as Record<string, any>)
    
    // Calculate averages for technicians
    Object.keys(technicianPerformance).forEach(tech => {
      const perf = technicianPerformance[tech]
      perf.avgCompletionTime = perf.completedJobs > 0 ? perf.avgCompletionTime / perf.completedJobs : 0
      perf.completionRate = perf.totalJobs > 0 ? (perf.completedJobs / perf.totalJobs) * 100 : 0
      perf.onTimeRate = perf.completedJobs > 0 ? (perf.onTimeDelivery / perf.completedJobs) * 100 : 0
      perf.avgJobValue = perf.totalJobs > 0 ? perf.totalRevenue / perf.totalJobs : 0
    })
    
    // Repair type analysis
    const repairTypeAnalysis = filteredRepairs.reduce((acc, repair) => {
      const type = repair.orderType || "repair"
      if (!acc[type]) {
        acc[type] = { count: 0, revenue: 0, avgValue: 0 }
      }
      acc[type].count += 1
      acc[type].revenue += repair.totalActual || repair.charges || 0
      acc[type].avgValue = acc[type].revenue / acc[type].count
      return acc
    }, {} as Record<string, any>)
    
    // Time-based metrics
    const avgTurnaroundTime = filteredRepairs
      .filter(repair => repair.receivedDate && repair.completedDate)
      .reduce((sum, repair) => {
        const received = new Date(repair.receivedDate)
        const completed = new Date(repair.completedDate!)
        return sum + Math.ceil((completed.getTime() - received.getTime()) / (1000 * 60 * 60 * 24))
      }, 0) / filteredRepairs.filter(repair => repair.receivedDate && repair.completedDate).length || 0
    
    // On-time delivery rate
    const onTimeDeliveries = filteredRepairs.filter(repair => {
      if (!repair.promisedDate || !repair.completedDate) return false
      return new Date(repair.completedDate) <= new Date(repair.promisedDate)
    }).length
    
    const totalDelivered = filteredRepairs.filter(repair => repair.completedDate).length
    const onTimeDeliveryRate = totalDelivered > 0 ? (onTimeDeliveries / totalDelivered) * 100 : 0
    
    // Customer satisfaction metrics (mock data)
    const customerSatisfactionScore = 4.3
    const repeatCustomerRate = 65.2
    
    // Overdue repairs
    const overdueRepairs = filteredRepairs.filter(repair => {
      if (repair.status === "completed" || repair.status === "delivered") return false
      if (!repair.promisedDate) return false
      return new Date(repair.promisedDate) < new Date()
    }).length
    
    // Revenue growth (mock calculation)
    const previousPeriodRevenue = totalRevenue * 0.88 // Mock 12% growth
    const revenueGrowth = previousPeriodRevenue > 0 ? ((totalRevenue - previousPeriodRevenue) / previousPeriodRevenue) * 100 : 0
    
    return {
      totalRepairs,
      totalRevenue,
      avgRepairValue,
      statusDistribution,
      priorityDistribution,
      technicianPerformance,
      repairTypeAnalysis,
      avgTurnaroundTime,
      onTimeDeliveryRate,
      customerSatisfactionScore,
      repeatCustomerRate,
      overdueRepairs,
      revenueGrowth,
      completionRate: totalRepairs > 0 ? ((statusDistribution.completed || 0) + (statusDistribution.delivered || 0)) / totalRepairs * 100 : 0,
      pendingRepairs: statusDistribution.pending || 0,
      inProgressRepairs: (statusDistribution.in_progress || 0) + (statusDistribution.assessed || 0) + (statusDistribution.approved || 0),
    }
  }, [repairs, dateRange, statusFilter, technicianFilter, priorityFilter])

  const exportReport = () => {
    const reportData = {
      summary: analytics,
      dateRange,
      filters: { statusFilter, technicianFilter, priorityFilter },
      generatedAt: new Date().toISOString()
    }
    
    const dataStr = JSON.stringify(reportData, null, 2)
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)
    
    const exportFileDefaultName = `repair-analytics-${new Date().toISOString().split('T')[0]}.json`
    
    const linkElement = document.createElement('a')
    linkElement.setAttribute('href', dataUri)
    linkElement.setAttribute('download', exportFileDefaultName)
    linkElement.click()
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-2xl font-bold tracking-tight">Repair Analytics</h3>
          <p className="text-muted-foreground">Comprehensive repair shop performance and business intelligence</p>
        </div>
        <Button onClick={exportReport}>
          <Download className="h-4 w-4 mr-2" />
          Export Report
        </Button>
      </div>

      {/* Advanced Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Analytics Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="space-y-2">
              <Label>Date Range</Label>
              <Select value={dateRange} onValueChange={setDateRange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="week">This Week</SelectItem>
                  <SelectItem value="month">This Month</SelectItem>
                  <SelectItem value="year">This Year</SelectItem>
                  <SelectItem value="all">All Time</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label>Status</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="received">Received</SelectItem>
                  <SelectItem value="in_progress">In Progress</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="delivered">Delivered</SelectItem>
                  <SelectItem value="on_hold">On Hold</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Technician</Label>
              <Select value={technicianFilter} onValueChange={setTechnicianFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Technicians" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Technicians</SelectItem>
                  <SelectItem value="Rajesh Kumar">Rajesh Kumar</SelectItem>
                  <SelectItem value="Suresh Patel">Suresh Patel</SelectItem>
                  <SelectItem value="Amit Sharma">Amit Sharma</SelectItem>
                  <SelectItem value="Deepak Singh">Deepak Singh</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Priority</Label>
              <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Priorities" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priorities</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="normal">Normal</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="urgent">Urgent</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>&nbsp;</Label>
              <Button 
                variant="outline" 
                onClick={() => {
                  setDateRange("month")
                  setStatusFilter("all")
                  setTechnicianFilter("all")
                  setPriorityFilter("all")
                }}
                className="w-full"
              >
                Reset Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="performance" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Performance
          </TabsTrigger>
          <TabsTrigger value="technicians" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Technicians
          </TabsTrigger>
          <TabsTrigger value="operations" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Operations
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Repairs</CardTitle>
                <Wrench className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.totalRepairs}</div>
                <p className="text-xs text-muted-foreground">
                  {analytics.pendingRepairs} pending, {analytics.inProgressRepairs} in progress
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">₹{analytics.totalRevenue.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  {analytics.revenueGrowth > 0 ? "+" : ""}{analytics.revenueGrowth.toFixed(1)}% from last period
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg Repair Value</CardTitle>
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">₹{analytics.avgRepairValue.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">Per repair job</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Overdue Jobs</CardTitle>
                <AlertCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{analytics.overdueRepairs}</div>
                <p className="text-xs text-muted-foreground">
                  {analytics.onTimeDeliveryRate.toFixed(1)}% on-time delivery
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Status and Priority Distribution */}
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Repair Status Distribution</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {Object.entries(analytics.statusDistribution).map(([status, count]) => (
                  <div key={status}>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="capitalize">{status.replace('_', ' ')}</span>
                      <span>{count} jobs</span>
                    </div>
                    <Progress value={(count / analytics.totalRepairs) * 100} className="h-2" />
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Priority Distribution</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {Object.entries(analytics.priorityDistribution).map(([priority, count]) => (
                  <div key={priority}>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="capitalize">{priority}</span>
                      <span>{count} jobs</span>
                    </div>
                    <Progress value={(count / analytics.totalRepairs) * 100} className="h-2" />
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Business Intelligence Cards */}
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Turnaround Time
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600 mb-2">
                    {analytics.avgTurnaroundTime.toFixed(1)}
                  </div>
                  <p className="text-sm text-muted-foreground mb-4">Average Days</p>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Completion Rate</span>
                      <span className="font-medium">{analytics.completionRate.toFixed(1)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>On-Time Delivery</span>
                      <span className="font-medium">{analytics.onTimeDeliveryRate.toFixed(1)}%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Star className="h-5 w-5" />
                  Customer Satisfaction
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="text-3xl font-bold text-yellow-600 mb-2">
                    {analytics.customerSatisfactionScore}/5
                  </div>
                  <p className="text-sm text-muted-foreground mb-4">Average Rating</p>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Repeat Customers</span>
                      <span className="font-medium">{analytics.repeatCustomerRate}%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Repair Types
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(analytics.repairTypeAnalysis).map(([type, data]: [string, any]) => (
                    <div key={type}>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="capitalize">{type.replace('_', ' ')}</span>
                        <span>₹{data.avgValue.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between text-xs text-muted-foreground mb-1">
                        <span>{data.count} jobs</span>
                        <span>₹{data.revenue.toLocaleString()} total</span>
                      </div>
                      <Progress value={(data.revenue / analytics.totalRevenue) * 100} className="h-2" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Job Completion Rate</span>
                    <span>{analytics.completionRate.toFixed(1)}%</span>
                  </div>
                  <Progress value={analytics.completionRate} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>On-Time Delivery Rate</span>
                    <span>{analytics.onTimeDeliveryRate.toFixed(1)}%</span>
                  </div>
                  <Progress value={analytics.onTimeDeliveryRate} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Customer Satisfaction</span>
                    <span>{((analytics.customerSatisfactionScore / 5) * 100).toFixed(1)}%</span>
                  </div>
                  <Progress value={(analytics.customerSatisfactionScore / 5) * 100} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Repeat Customer Rate</span>
                    <span>{analytics.repeatCustomerRate}%</span>
                  </div>
                  <Progress value={analytics.repeatCustomerRate} className="h-2" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Revenue Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      ₹{analytics.totalRevenue.toLocaleString()}
                    </div>
                    <p className="text-sm text-muted-foreground">Total Revenue</p>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Average Job Value</span>
                      <span className="font-medium">₹{analytics.avgRepairValue.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Revenue Growth</span>
                      <span className={`font-medium ${analytics.revenueGrowth > 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {analytics.revenueGrowth > 0 ? '+' : ''}{analytics.revenueGrowth.toFixed(1)}%
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Technicians Tab */}
        <TabsContent value="technicians" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Technician Performance</CardTitle>
              <CardDescription>Individual technician metrics and productivity analysis</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Technician</TableHead>
                    <TableHead>Total Jobs</TableHead>
                    <TableHead>Completed</TableHead>
                    <TableHead>Completion Rate</TableHead>
                    <TableHead>On-Time Rate</TableHead>
                    <TableHead>Avg Days</TableHead>
                    <TableHead>Revenue</TableHead>
                    <TableHead>Avg Job Value</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {Object.entries(analytics.technicianPerformance).map(([technician, perf]: [string, any]) => (
                    <TableRow key={technician}>
                      <TableCell>
                        <div className="font-medium">{technician}</div>
                      </TableCell>
                      <TableCell>{perf.totalJobs}</TableCell>
                      <TableCell>{perf.completedJobs}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Progress value={perf.completionRate} className="h-2 w-16" />
                          <span className="text-sm">{perf.completionRate.toFixed(1)}%</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={perf.onTimeRate > 80 ? "default" : perf.onTimeRate > 60 ? "secondary" : "destructive"}>
                          {perf.onTimeRate.toFixed(1)}%
                        </Badge>
                      </TableCell>
                      <TableCell>{perf.avgCompletionTime.toFixed(1)}</TableCell>
                      <TableCell className="font-medium">₹{perf.totalRevenue.toLocaleString()}</TableCell>
                      <TableCell>₹{perf.avgJobValue.toLocaleString()}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Operations Tab */}
        <TabsContent value="operations" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Operational Efficiency</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Average Turnaround Time</span>
                    <Badge variant="outline">{analytics.avgTurnaroundTime.toFixed(1)} days</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Jobs Completed On Time</span>
                    <Badge variant={analytics.onTimeDeliveryRate > 80 ? "default" : "secondary"}>
                      {analytics.onTimeDeliveryRate.toFixed(1)}%
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Overdue Jobs</span>
                    <Badge variant={analytics.overdueRepairs > 5 ? "destructive" : "outline"}>
                      {analytics.overdueRepairs}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Customer Satisfaction</span>
                    <Badge variant="default">{analytics.customerSatisfactionScore}/5</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Quality Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600 mb-2">
                      {analytics.customerSatisfactionScore}
                    </div>
                    <p className="text-sm text-muted-foreground mb-4">Customer Rating (out of 5)</p>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Quality Pass Rate</span>
                      <span className="font-medium">96.5%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Rework Rate</span>
                      <span className="font-medium">3.5%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Customer Complaints</span>
                      <span className="font-medium">1.2%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
