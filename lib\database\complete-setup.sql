-- COMPLETE DATABASE SETUP SCRIPT
-- Version: 4.0.0
-- Date: January 31, 2025
-- Description: Complete database setup with schema and sample data

-- ============================================================================
-- SETUP INSTRUCTIONS
-- ============================================================================

/*
COMPLETE DATABASE SETUP FOR JEWELRY MANAGEMENT SYSTEM

This script will:
1. Drop and recreate the database
2. Create all tables with proper relationships
3. Insert comprehensive sample data for testing

PREREQUISITES:
- MySQL 8.0 or higher
- Sufficient privileges to create databases
- Backup any existing data before running

USAGE:
1. Connect to MySQL as root or admin user
2. Execute this script: mysql -u root -p < complete-setup.sql
3. Or run each section manually in your MySQL client

WARNING: This will completely reset the database!
*/

-- ============================================================================
-- SAFETY CONFIRMATION
-- ============================================================================

-- Uncomment the following line to confirm you want to reset the database
-- SET @CONFIRM_RESET = 'YES_I_WANT_TO_RESET_DATABASE';

-- Safety check (comment out to skip)
SELECT CASE 
  WHEN @CONFIRM_RESET = 'YES_I_WANT_TO_RESET_DATABASE' THEN 'Proceeding with database setup...'
  ELSE 'Setup cancelled. Uncomment the confirmation line to proceed.'
END as setup_status;

-- ============================================================================
-- STEP 1: DATABASE RESET
-- ============================================================================

-- Disable foreign key checks
SET FOREIGN_KEY_CHECKS = 0;

-- Drop existing database
DROP DATABASE IF EXISTS jewellers_db;

-- Create fresh database
CREATE DATABASE jewellers_db 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE jewellers_db;

-- Set proper SQL mode
SET sql_mode = 'STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION';

-- ============================================================================
-- STEP 2: EXECUTE SCHEMA CREATION
-- ============================================================================

-- Note: The complete schema is in updated-comprehensive-schema.sql
-- Execute that file first, then this setup script

-- Verify schema is loaded
SELECT COUNT(*) as tables_created 
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'jewellers_db';

-- ============================================================================
-- STEP 3: EXECUTE SAMPLE DATA
-- ============================================================================

-- Note: The sample data is in seed-sample-data.sql
-- Execute that file after the schema is created

-- ============================================================================
-- STEP 4: VERIFICATION AND TESTING
-- ============================================================================

-- Verify all tables exist
SELECT 
  TABLE_NAME as table_name,
  TABLE_ROWS as estimated_rows,
  CREATE_TIME as created_at
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'jewellers_db'
ORDER BY TABLE_NAME;

-- Verify foreign key constraints
SELECT 
  CONSTRAINT_NAME as constraint_name,
  TABLE_NAME as table_name,
  REFERENCED_TABLE_NAME as references_table
FROM information_schema.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = 'jewellers_db' 
  AND REFERENCED_TABLE_NAME IS NOT NULL
ORDER BY TABLE_NAME;

-- Verify sample data
SELECT 
  'users' as table_name, COUNT(*) as records FROM users
UNION ALL
SELECT 'customers', COUNT(*) FROM customers
UNION ALL
SELECT 'inventory', COUNT(*) FROM inventory
UNION ALL
SELECT 'sales', COUNT(*) FROM sales
UNION ALL
SELECT 'schemes', COUNT(*) FROM schemes
UNION ALL
SELECT 'repairs', COUNT(*) FROM repairs
UNION ALL
SELECT 'suppliers', COUNT(*) FROM suppliers
UNION ALL
SELECT 'purchases', COUNT(*) FROM purchases
UNION ALL
SELECT 'exchange_transactions', COUNT(*) FROM exchange_transactions;

-- ============================================================================
-- STEP 5: CREATE DEFAULT ADMIN USER (if not exists)
-- ============================================================================

INSERT IGNORE INTO users (
  id, name, email, phone, password_hash, role, permissions, is_active
) VALUES (
  'admin_default', 
  'System Administrator', 
  '<EMAIL>', 
  '+91 98765 43210',
  '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: admin123
  'super_admin',
  JSON_ARRAY('system_admin'),
  true
);

-- ============================================================================
-- STEP 6: PERFORMANCE OPTIMIZATION
-- ============================================================================

-- Analyze tables for better performance
ANALYZE TABLE users, customers, inventory, sales, schemes, repairs, suppliers, purchases, exchange_transactions;

-- Update table statistics
OPTIMIZE TABLE users, customers, inventory, sales, schemes, repairs, suppliers, purchases, exchange_transactions;

-- ============================================================================
-- SETUP COMPLETION
-- ============================================================================

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Final verification
SELECT 
  'Database setup completed successfully!' as message,
  NOW() as completed_at,
  'jewellers_db' as database_name,
  '4.0.0' as version,
  (SELECT COUNT(*) FROM information_schema.TABLES WHERE TABLE_SCHEMA = 'jewellers_db') as total_tables,
  (SELECT COUNT(*) FROM users) as total_users,
  (SELECT COUNT(*) FROM customers) as total_customers,
  (SELECT COUNT(*) FROM inventory) as total_inventory_items;

-- Show login credentials
SELECT 
  'DEFAULT LOGIN CREDENTIALS' as info,
  'Email: <EMAIL>' as email,
  'Password: admin123' as password,
  'CHANGE THESE CREDENTIALS IMMEDIATELY!' as warning;

-- ============================================================================
-- NEXT STEPS
-- ============================================================================

/*
NEXT STEPS AFTER SETUP:

1. SECURITY:
   - Change default admin password immediately
   - Create additional users with appropriate roles
   - Review and update business settings

2. CONFIGURATION:
   - Update business information in business_settings table
   - Configure metal rates for current market prices
   - Set up inventory categories as needed

3. TESTING:
   - Test all modules with sample data
   - Verify all features work correctly
   - Run application and check connectivity

4. PRODUCTION PREPARATION:
   - Remove or modify sample data as needed
   - Set up regular backup schedules
   - Configure monitoring and logging

5. APPLICATION STARTUP:
   - Update database connection settings in application
   - Start the application server
   - Access the system at http://localhost:3000

SAMPLE DATA INCLUDED:
- 5 Users (different roles)
- 5 Customers (different types)
- 5 Inventory items (various categories)
- 3 Sales transactions
- 3 Gold schemes
- 3 Repair orders
- 3 Suppliers
- 3 Purchase orders
- 2 Exchange transactions

All sample data is realistic and can be used for testing all system features.
*/
