"use client"

import { useState, useMemo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useStore } from "@/lib/store"
import { 
  Download, FileText, TrendingUp, TrendingDown, 
  IndianRupee, Calculator, BarChart3, PieChart,
  Calendar, Building, CreditCard, Receipt
} from "lucide-react"

export function FinancialReports() {
  const { sales, purchases, repairs, schemes, inventory } = useStore()
  const [activeTab, setActiveTab] = useState("pnl")
  const [reportPeriod, setReportPeriod] = useState("month")
  const [comparisonPeriod, setComparisonPeriod] = useState("previous_month")

  // Financial calculations
  const financialData = useMemo(() => {
    const now = new Date()
    const currentPeriodStart = new Date()
    const previousPeriodStart = new Date()
    const previousPeriodEnd = new Date()

    // Set current period
    if (reportPeriod === "month") {
      currentPeriodStart.setMonth(now.getMonth() - 1)
      previousPeriodStart.setMonth(now.getMonth() - 2)
      previousPeriodEnd.setMonth(now.getMonth() - 1)
    } else if (reportPeriod === "quarter") {
      currentPeriodStart.setMonth(now.getMonth() - 3)
      previousPeriodStart.setMonth(now.getMonth() - 6)
      previousPeriodEnd.setMonth(now.getMonth() - 3)
    } else if (reportPeriod === "year") {
      currentPeriodStart.setFullYear(now.getFullYear() - 1)
      previousPeriodStart.setFullYear(now.getFullYear() - 2)
      previousPeriodEnd.setFullYear(now.getFullYear() - 1)
    }

    // Filter data for current period
    const currentSales = sales.filter(sale => new Date(sale.date) >= currentPeriodStart)
    const currentPurchases = purchases.filter(purchase => new Date(purchase.date) >= currentPeriodStart)
    const currentRepairs = repairs.filter(repair => new Date(repair.receivedDate) >= currentPeriodStart)
    const currentSchemes = schemes.filter(scheme => new Date(scheme.startDate) >= currentPeriodStart)

    // Filter data for previous period
    const previousSales = sales.filter(sale => {
      const saleDate = new Date(sale.date)
      return saleDate >= previousPeriodStart && saleDate < previousPeriodEnd
    })
    const previousPurchases = purchases.filter(purchase => {
      const purchaseDate = new Date(purchase.date)
      return purchaseDate >= previousPeriodStart && purchaseDate < previousPeriodEnd
    })

    // Revenue Calculations
    const salesRevenue = currentSales.reduce((sum, sale) => sum + (sale.total || 0), 0)
    const repairRevenue = currentRepairs.reduce((sum, repair) => sum + (repair.charges || 0), 0)
    const schemeRevenue = currentSchemes.reduce((sum, scheme) => sum + (scheme.paidAmount || 0), 0)
    const totalRevenue = salesRevenue + repairRevenue + schemeRevenue

    // Previous period revenue for comparison
    const previousSalesRevenue = previousSales.reduce((sum, sale) => sum + (sale.total || 0), 0)
    const previousRepairRevenue = previousSales.length * 1500 // Mock repair revenue
    const previousTotalRevenue = previousSalesRevenue + previousRepairRevenue

    // Cost of Goods Sold (COGS)
    const purchaseCosts = currentPurchases.reduce((sum, purchase) => sum + (purchase.amount || 0), 0)
    const directMaterialCosts = salesRevenue * 0.60 // Assuming 60% material cost
    const totalCOGS = purchaseCosts + directMaterialCosts

    // Gross Profit
    const grossProfit = totalRevenue - totalCOGS
    const grossProfitMargin = totalRevenue > 0 ? (grossProfit / totalRevenue) * 100 : 0

    // Operating Expenses
    const salaryExpenses = 150000 // Mock monthly salary
    const rentExpenses = 50000 // Mock monthly rent
    const utilitiesExpenses = 15000 // Mock utilities
    const marketingExpenses = 25000 // Mock marketing
    const insuranceExpenses = 10000 // Mock insurance
    const otherExpenses = 20000 // Mock other expenses
    const totalOperatingExpenses = salaryExpenses + rentExpenses + utilitiesExpenses + marketingExpenses + insuranceExpenses + otherExpenses

    // EBITDA and Net Profit
    const ebitda = grossProfit - totalOperatingExpenses
    const depreciation = 8000 // Mock depreciation
    const interestExpense = 5000 // Mock interest
    const taxRate = 0.30 // 30% tax rate
    const profitBeforeTax = ebitda - depreciation - interestExpense
    const taxExpense = Math.max(0, profitBeforeTax * taxRate)
    const netProfit = profitBeforeTax - taxExpense

    // Balance Sheet Items
    const currentAssets = {
      cash: 500000,
      inventory: inventory.reduce((sum, item) => sum + ((item.currentValue || 0) * (item.stock || 0)), 0),
      accountsReceivable: totalRevenue * 0.15, // 15% of revenue as receivables
      prepaidExpenses: 25000
    }
    const totalCurrentAssets = Object.values(currentAssets).reduce((sum, value) => sum + value, 0)

    const fixedAssets = {
      equipment: 800000,
      furniture: 200000,
      accumulatedDepreciation: -150000
    }
    const totalFixedAssets = Object.values(fixedAssets).reduce((sum, value) => sum + value, 0)
    const totalAssets = totalCurrentAssets + totalFixedAssets

    const currentLiabilities = {
      accountsPayable: purchaseCosts * 0.20, // 20% of purchases as payables
      accrualExpenses: 35000,
      shortTermLoans: 100000
    }
    const totalCurrentLiabilities = Object.values(currentLiabilities).reduce((sum, value) => sum + value, 0)

    const longTermLiabilities = {
      longTermLoans: 300000,
      otherLiabilities: 50000
    }
    const totalLongTermLiabilities = Object.values(longTermLiabilities).reduce((sum, value) => sum + value, 0)
    const totalLiabilities = totalCurrentLiabilities + totalLongTermLiabilities

    const equity = {
      paidUpCapital: 1000000,
      retainedEarnings: totalAssets - totalLiabilities - 1000000
    }
    const totalEquity = Object.values(equity).reduce((sum, value) => sum + value, 0)

    // Cash Flow Statement
    const operatingCashFlow = netProfit + depreciation
    const investingCashFlow = -50000 // Mock equipment purchase
    const financingCashFlow = -25000 // Mock loan repayment
    const netCashFlow = operatingCashFlow + investingCashFlow + financingCashFlow

    // Financial Ratios
    const currentRatio = totalCurrentLiabilities > 0 ? totalCurrentAssets / totalCurrentLiabilities : 0
    const quickRatio = totalCurrentLiabilities > 0 ? (totalCurrentAssets - currentAssets.inventory) / totalCurrentLiabilities : 0
    const debtToEquityRatio = totalEquity > 0 ? totalLiabilities / totalEquity : 0
    const returnOnAssets = totalAssets > 0 ? (netProfit / totalAssets) * 100 : 0
    const returnOnEquity = totalEquity > 0 ? (netProfit / totalEquity) * 100 : 0

    // Growth calculations
    const revenueGrowth = previousTotalRevenue > 0 ? ((totalRevenue - previousTotalRevenue) / previousTotalRevenue) * 100 : 0

    return {
      // P&L Statement
      salesRevenue,
      repairRevenue,
      schemeRevenue,
      totalRevenue,
      totalCOGS,
      grossProfit,
      grossProfitMargin,
      totalOperatingExpenses,
      ebitda,
      depreciation,
      interestExpense,
      profitBeforeTax,
      taxExpense,
      netProfit,
      
      // Balance Sheet
      currentAssets,
      totalCurrentAssets,
      fixedAssets,
      totalFixedAssets,
      totalAssets,
      currentLiabilities,
      totalCurrentLiabilities,
      longTermLiabilities,
      totalLongTermLiabilities,
      totalLiabilities,
      equity,
      totalEquity,
      
      // Cash Flow
      operatingCashFlow,
      investingCashFlow,
      financingCashFlow,
      netCashFlow,
      
      // Ratios
      currentRatio,
      quickRatio,
      debtToEquityRatio,
      returnOnAssets,
      returnOnEquity,
      
      // Growth
      revenueGrowth,
      
      // Operating Expenses Breakdown
      operatingExpenses: {
        salaryExpenses,
        rentExpenses,
        utilitiesExpenses,
        marketingExpenses,
        insuranceExpenses,
        otherExpenses
      }
    }
  }, [sales, purchases, repairs, schemes, inventory, reportPeriod])

  const exportReport = () => {
    const reportData = {
      reportType: activeTab,
      reportPeriod,
      financialData,
      generatedAt: new Date().toISOString()
    }
    
    const dataStr = JSON.stringify(reportData, null, 2)
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)
    
    const exportFileDefaultName = `financial-report-${activeTab}-${new Date().toISOString().split('T')[0]}.json`
    
    const linkElement = document.createElement('a')
    linkElement.setAttribute('href', dataUri)
    linkElement.setAttribute('download', exportFileDefaultName)
    linkElement.click()
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Financial Reports</h2>
          <p className="text-muted-foreground">Comprehensive financial statements and analysis</p>
        </div>
        <div className="flex items-center gap-4">
          <Select value={reportPeriod} onValueChange={setReportPeriod}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="month">This Month</SelectItem>
              <SelectItem value="quarter">This Quarter</SelectItem>
              <SelectItem value="year">This Year</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={exportReport}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="pnl" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            P&L Statement
          </TabsTrigger>
          <TabsTrigger value="balance" className="flex items-center gap-2">
            <Calculator className="h-4 w-4" />
            Balance Sheet
          </TabsTrigger>
          <TabsTrigger value="cashflow" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Cash Flow
          </TabsTrigger>
          <TabsTrigger value="ratios" className="flex items-center gap-2">
            <PieChart className="h-4 w-4" />
            Financial Ratios
          </TabsTrigger>
        </TabsList>

        {/* P&L Statement Tab */}
        <TabsContent value="pnl" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Profit & Loss Statement
              </CardTitle>
              <CardDescription>
                Revenue, expenses, and profitability analysis for {reportPeriod}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableBody>
                  {/* Revenue Section */}
                  <TableRow className="bg-blue-50">
                    <TableCell className="font-bold">REVENUE</TableCell>
                    <TableCell></TableCell>
                    <TableCell></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-6">Sales Revenue</TableCell>
                    <TableCell className="text-right">₹{financialData.salesRevenue.toLocaleString()}</TableCell>
                    <TableCell className="text-right text-muted-foreground">
                      {((financialData.salesRevenue / financialData.totalRevenue) * 100).toFixed(1)}%
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-6">Repair Services Revenue</TableCell>
                    <TableCell className="text-right">₹{financialData.repairRevenue.toLocaleString()}</TableCell>
                    <TableCell className="text-right text-muted-foreground">
                      {((financialData.repairRevenue / financialData.totalRevenue) * 100).toFixed(1)}%
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-6">Scheme Collections</TableCell>
                    <TableCell className="text-right">₹{financialData.schemeRevenue.toLocaleString()}</TableCell>
                    <TableCell className="text-right text-muted-foreground">
                      {((financialData.schemeRevenue / financialData.totalRevenue) * 100).toFixed(1)}%
                    </TableCell>
                  </TableRow>
                  <TableRow className="border-t-2">
                    <TableCell className="font-bold">Total Revenue</TableCell>
                    <TableCell className="text-right font-bold">₹{financialData.totalRevenue.toLocaleString()}</TableCell>
                    <TableCell className="text-right">
                      <Badge variant={financialData.revenueGrowth > 0 ? "default" : "secondary"}>
                        {financialData.revenueGrowth > 0 ? "+" : ""}{financialData.revenueGrowth.toFixed(1)}%
                      </Badge>
                    </TableCell>
                  </TableRow>

                  {/* COGS Section */}
                  <TableRow className="bg-red-50">
                    <TableCell className="font-bold">COST OF GOODS SOLD</TableCell>
                    <TableCell></TableCell>
                    <TableCell></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-6">Direct Material Costs</TableCell>
                    <TableCell className="text-right">₹{(financialData.totalRevenue * 0.60).toLocaleString()}</TableCell>
                    <TableCell className="text-right text-muted-foreground">60.0%</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-6">Purchase Costs</TableCell>
                    <TableCell className="text-right">₹{(financialData.totalCOGS - financialData.totalRevenue * 0.60).toLocaleString()}</TableCell>
                    <TableCell className="text-right text-muted-foreground">
                      {(((financialData.totalCOGS - financialData.totalRevenue * 0.60) / financialData.totalRevenue) * 100).toFixed(1)}%
                    </TableCell>
                  </TableRow>
                  <TableRow className="border-t-2">
                    <TableCell className="font-bold">Total COGS</TableCell>
                    <TableCell className="text-right font-bold">₹{financialData.totalCOGS.toLocaleString()}</TableCell>
                    <TableCell className="text-right text-muted-foreground">
                      {((financialData.totalCOGS / financialData.totalRevenue) * 100).toFixed(1)}%
                    </TableCell>
                  </TableRow>

                  {/* Gross Profit */}
                  <TableRow className="bg-green-50 border-t-2">
                    <TableCell className="font-bold text-green-800">GROSS PROFIT</TableCell>
                    <TableCell className="text-right font-bold text-green-800">₹{financialData.grossProfit.toLocaleString()}</TableCell>
                    <TableCell className="text-right font-bold text-green-800">{financialData.grossProfitMargin.toFixed(1)}%</TableCell>
                  </TableRow>

                  {/* Operating Expenses */}
                  <TableRow className="bg-orange-50">
                    <TableCell className="font-bold">OPERATING EXPENSES</TableCell>
                    <TableCell></TableCell>
                    <TableCell></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-6">Salary & Benefits</TableCell>
                    <TableCell className="text-right">₹{financialData.operatingExpenses.salaryExpenses.toLocaleString()}</TableCell>
                    <TableCell className="text-right text-muted-foreground">
                      {((financialData.operatingExpenses.salaryExpenses / financialData.totalRevenue) * 100).toFixed(1)}%
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-6">Rent & Utilities</TableCell>
                    <TableCell className="text-right">₹{(financialData.operatingExpenses.rentExpenses + financialData.operatingExpenses.utilitiesExpenses).toLocaleString()}</TableCell>
                    <TableCell className="text-right text-muted-foreground">
                      {(((financialData.operatingExpenses.rentExpenses + financialData.operatingExpenses.utilitiesExpenses) / financialData.totalRevenue) * 100).toFixed(1)}%
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-6">Marketing & Advertising</TableCell>
                    <TableCell className="text-right">₹{financialData.operatingExpenses.marketingExpenses.toLocaleString()}</TableCell>
                    <TableCell className="text-right text-muted-foreground">
                      {((financialData.operatingExpenses.marketingExpenses / financialData.totalRevenue) * 100).toFixed(1)}%
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-6">Insurance & Other</TableCell>
                    <TableCell className="text-right">₹{(financialData.operatingExpenses.insuranceExpenses + financialData.operatingExpenses.otherExpenses).toLocaleString()}</TableCell>
                    <TableCell className="text-right text-muted-foreground">
                      {(((financialData.operatingExpenses.insuranceExpenses + financialData.operatingExpenses.otherExpenses) / financialData.totalRevenue) * 100).toFixed(1)}%
                    </TableCell>
                  </TableRow>
                  <TableRow className="border-t-2">
                    <TableCell className="font-bold">Total Operating Expenses</TableCell>
                    <TableCell className="text-right font-bold">₹{financialData.totalOperatingExpenses.toLocaleString()}</TableCell>
                    <TableCell className="text-right text-muted-foreground">
                      {((financialData.totalOperatingExpenses / financialData.totalRevenue) * 100).toFixed(1)}%
                    </TableCell>
                  </TableRow>

                  {/* EBITDA */}
                  <TableRow className="bg-blue-50 border-t-2">
                    <TableCell className="font-bold text-blue-800">EBITDA</TableCell>
                    <TableCell className="text-right font-bold text-blue-800">₹{financialData.ebitda.toLocaleString()}</TableCell>
                    <TableCell className="text-right font-bold text-blue-800">
                      {((financialData.ebitda / financialData.totalRevenue) * 100).toFixed(1)}%
                    </TableCell>
                  </TableRow>

                  {/* Other Expenses */}
                  <TableRow>
                    <TableCell className="pl-6">Depreciation</TableCell>
                    <TableCell className="text-right">₹{financialData.depreciation.toLocaleString()}</TableCell>
                    <TableCell className="text-right text-muted-foreground">
                      {((financialData.depreciation / financialData.totalRevenue) * 100).toFixed(1)}%
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-6">Interest Expense</TableCell>
                    <TableCell className="text-right">₹{financialData.interestExpense.toLocaleString()}</TableCell>
                    <TableCell className="text-right text-muted-foreground">
                      {((financialData.interestExpense / financialData.totalRevenue) * 100).toFixed(1)}%
                    </TableCell>
                  </TableRow>

                  {/* Profit Before Tax */}
                  <TableRow className="border-t-2">
                    <TableCell className="font-bold">Profit Before Tax</TableCell>
                    <TableCell className="text-right font-bold">₹{financialData.profitBeforeTax.toLocaleString()}</TableCell>
                    <TableCell className="text-right font-bold">
                      {((financialData.profitBeforeTax / financialData.totalRevenue) * 100).toFixed(1)}%
                    </TableCell>
                  </TableRow>

                  {/* Tax */}
                  <TableRow>
                    <TableCell className="pl-6">Tax Expense (30%)</TableCell>
                    <TableCell className="text-right">₹{financialData.taxExpense.toLocaleString()}</TableCell>
                    <TableCell className="text-right text-muted-foreground">
                      {((financialData.taxExpense / financialData.totalRevenue) * 100).toFixed(1)}%
                    </TableCell>
                  </TableRow>

                  {/* Net Profit */}
                  <TableRow className="bg-green-100 border-t-4 border-green-500">
                    <TableCell className="font-bold text-green-800 text-lg">NET PROFIT</TableCell>
                    <TableCell className="text-right font-bold text-green-800 text-lg">₹{financialData.netProfit.toLocaleString()}</TableCell>
                    <TableCell className="text-right font-bold text-green-800 text-lg">
                      {((financialData.netProfit / financialData.totalRevenue) * 100).toFixed(1)}%
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Balance Sheet Tab */}
        <TabsContent value="balance" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Assets */}
            <Card>
              <CardHeader>
                <CardTitle className="text-green-800">ASSETS</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableBody>
                    <TableRow className="bg-green-50">
                      <TableCell className="font-bold">CURRENT ASSETS</TableCell>
                      <TableCell></TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="pl-6">Cash & Cash Equivalents</TableCell>
                      <TableCell className="text-right">₹{financialData.currentAssets.cash.toLocaleString()}</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="pl-6">Inventory</TableCell>
                      <TableCell className="text-right">₹{financialData.currentAssets.inventory.toLocaleString()}</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="pl-6">Accounts Receivable</TableCell>
                      <TableCell className="text-right">₹{financialData.currentAssets.accountsReceivable.toLocaleString()}</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="pl-6">Prepaid Expenses</TableCell>
                      <TableCell className="text-right">₹{financialData.currentAssets.prepaidExpenses.toLocaleString()}</TableCell>
                    </TableRow>
                    <TableRow className="border-t-2">
                      <TableCell className="font-bold">Total Current Assets</TableCell>
                      <TableCell className="text-right font-bold">₹{financialData.totalCurrentAssets.toLocaleString()}</TableCell>
                    </TableRow>

                    <TableRow className="bg-green-50">
                      <TableCell className="font-bold">FIXED ASSETS</TableCell>
                      <TableCell></TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="pl-6">Equipment</TableCell>
                      <TableCell className="text-right">₹{financialData.fixedAssets.equipment.toLocaleString()}</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="pl-6">Furniture & Fixtures</TableCell>
                      <TableCell className="text-right">₹{financialData.fixedAssets.furniture.toLocaleString()}</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="pl-6">Less: Accumulated Depreciation</TableCell>
                      <TableCell className="text-right text-red-600">₹{financialData.fixedAssets.accumulatedDepreciation.toLocaleString()}</TableCell>
                    </TableRow>
                    <TableRow className="border-t-2">
                      <TableCell className="font-bold">Total Fixed Assets</TableCell>
                      <TableCell className="text-right font-bold">₹{financialData.totalFixedAssets.toLocaleString()}</TableCell>
                    </TableRow>

                    <TableRow className="bg-green-100 border-t-4 border-green-500">
                      <TableCell className="font-bold text-green-800 text-lg">TOTAL ASSETS</TableCell>
                      <TableCell className="text-right font-bold text-green-800 text-lg">₹{financialData.totalAssets.toLocaleString()}</TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </CardContent>
            </Card>

            {/* Liabilities & Equity */}
            <Card>
              <CardHeader>
                <CardTitle className="text-red-800">LIABILITIES & EQUITY</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableBody>
                    <TableRow className="bg-red-50">
                      <TableCell className="font-bold">CURRENT LIABILITIES</TableCell>
                      <TableCell></TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="pl-6">Accounts Payable</TableCell>
                      <TableCell className="text-right">₹{financialData.currentLiabilities.accountsPayable.toLocaleString()}</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="pl-6">Accrued Expenses</TableCell>
                      <TableCell className="text-right">₹{financialData.currentLiabilities.accrualExpenses.toLocaleString()}</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="pl-6">Short-term Loans</TableCell>
                      <TableCell className="text-right">₹{financialData.currentLiabilities.shortTermLoans.toLocaleString()}</TableCell>
                    </TableRow>
                    <TableRow className="border-t-2">
                      <TableCell className="font-bold">Total Current Liabilities</TableCell>
                      <TableCell className="text-right font-bold">₹{financialData.totalCurrentLiabilities.toLocaleString()}</TableCell>
                    </TableRow>

                    <TableRow className="bg-red-50">
                      <TableCell className="font-bold">LONG-TERM LIABILITIES</TableCell>
                      <TableCell></TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="pl-6">Long-term Loans</TableCell>
                      <TableCell className="text-right">₹{financialData.longTermLiabilities.longTermLoans.toLocaleString()}</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="pl-6">Other Liabilities</TableCell>
                      <TableCell className="text-right">₹{financialData.longTermLiabilities.otherLiabilities.toLocaleString()}</TableCell>
                    </TableRow>
                    <TableRow className="border-t-2">
                      <TableCell className="font-bold">Total Long-term Liabilities</TableCell>
                      <TableCell className="text-right font-bold">₹{financialData.totalLongTermLiabilities.toLocaleString()}</TableCell>
                    </TableRow>

                    <TableRow className="border-t-2">
                      <TableCell className="font-bold">Total Liabilities</TableCell>
                      <TableCell className="text-right font-bold">₹{financialData.totalLiabilities.toLocaleString()}</TableCell>
                    </TableRow>

                    <TableRow className="bg-blue-50">
                      <TableCell className="font-bold">EQUITY</TableCell>
                      <TableCell></TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="pl-6">Paid-up Capital</TableCell>
                      <TableCell className="text-right">₹{financialData.equity.paidUpCapital.toLocaleString()}</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="pl-6">Retained Earnings</TableCell>
                      <TableCell className="text-right">₹{financialData.equity.retainedEarnings.toLocaleString()}</TableCell>
                    </TableRow>
                    <TableRow className="border-t-2">
                      <TableCell className="font-bold">Total Equity</TableCell>
                      <TableCell className="text-right font-bold">₹{financialData.totalEquity.toLocaleString()}</TableCell>
                    </TableRow>

                    <TableRow className="bg-blue-100 border-t-4 border-blue-500">
                      <TableCell className="font-bold text-blue-800 text-lg">TOTAL LIABILITIES & EQUITY</TableCell>
                      <TableCell className="text-right font-bold text-blue-800 text-lg">₹{(financialData.totalLiabilities + financialData.totalEquity).toLocaleString()}</TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Cash Flow Tab */}
        <TabsContent value="cashflow" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Cash Flow Statement
              </CardTitle>
              <CardDescription>
                Cash inflows and outflows for {reportPeriod}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableBody>
                  <TableRow className="bg-green-50">
                    <TableCell className="font-bold">OPERATING ACTIVITIES</TableCell>
                    <TableCell></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-6">Net Profit</TableCell>
                    <TableCell className="text-right">₹{financialData.netProfit.toLocaleString()}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-6">Add: Depreciation</TableCell>
                    <TableCell className="text-right">₹{financialData.depreciation.toLocaleString()}</TableCell>
                  </TableRow>
                  <TableRow className="border-t-2">
                    <TableCell className="font-bold text-green-800">Net Cash from Operating Activities</TableCell>
                    <TableCell className="text-right font-bold text-green-800">₹{financialData.operatingCashFlow.toLocaleString()}</TableCell>
                  </TableRow>

                  <TableRow className="bg-blue-50">
                    <TableCell className="font-bold">INVESTING ACTIVITIES</TableCell>
                    <TableCell></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-6">Equipment Purchase</TableCell>
                    <TableCell className="text-right text-red-600">₹{Math.abs(financialData.investingCashFlow).toLocaleString()}</TableCell>
                  </TableRow>
                  <TableRow className="border-t-2">
                    <TableCell className="font-bold text-blue-800">Net Cash from Investing Activities</TableCell>
                    <TableCell className="text-right font-bold text-blue-800">₹{financialData.investingCashFlow.toLocaleString()}</TableCell>
                  </TableRow>

                  <TableRow className="bg-orange-50">
                    <TableCell className="font-bold">FINANCING ACTIVITIES</TableCell>
                    <TableCell></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-6">Loan Repayment</TableCell>
                    <TableCell className="text-right text-red-600">₹{Math.abs(financialData.financingCashFlow).toLocaleString()}</TableCell>
                  </TableRow>
                  <TableRow className="border-t-2">
                    <TableCell className="font-bold text-orange-800">Net Cash from Financing Activities</TableCell>
                    <TableCell className="text-right font-bold text-orange-800">₹{financialData.financingCashFlow.toLocaleString()}</TableCell>
                  </TableRow>

                  <TableRow className="bg-purple-100 border-t-4 border-purple-500">
                    <TableCell className="font-bold text-purple-800 text-lg">NET CHANGE IN CASH</TableCell>
                    <TableCell className="text-right font-bold text-purple-800 text-lg">₹{financialData.netCashFlow.toLocaleString()}</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Financial Ratios Tab */}
        <TabsContent value="ratios" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Liquidity Ratios</CardTitle>
                <CardDescription>Ability to meet short-term obligations</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Current Ratio</span>
                  <div className="text-right">
                    <span className="font-semibold">{financialData.currentRatio.toFixed(2)}</span>
                    <Badge variant={financialData.currentRatio >= 2 ? "default" : financialData.currentRatio >= 1 ? "secondary" : "destructive"} className="ml-2">
                      {financialData.currentRatio >= 2 ? "Excellent" : financialData.currentRatio >= 1 ? "Good" : "Poor"}
                    </Badge>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Quick Ratio</span>
                  <div className="text-right">
                    <span className="font-semibold">{financialData.quickRatio.toFixed(2)}</span>
                    <Badge variant={financialData.quickRatio >= 1 ? "default" : "secondary"} className="ml-2">
                      {financialData.quickRatio >= 1 ? "Good" : "Needs Attention"}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Leverage Ratios</CardTitle>
                <CardDescription>Financial leverage and debt management</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Debt-to-Equity Ratio</span>
                  <div className="text-right">
                    <span className="font-semibold">{financialData.debtToEquityRatio.toFixed(2)}</span>
                    <Badge variant={financialData.debtToEquityRatio <= 1 ? "default" : "secondary"} className="ml-2">
                      {financialData.debtToEquityRatio <= 1 ? "Conservative" : "Leveraged"}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Profitability Ratios</CardTitle>
                <CardDescription>Return on investment and efficiency</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Return on Assets (ROA)</span>
                  <div className="text-right">
                    <span className="font-semibold">{financialData.returnOnAssets.toFixed(1)}%</span>
                    <Badge variant={financialData.returnOnAssets >= 5 ? "default" : "secondary"} className="ml-2">
                      {financialData.returnOnAssets >= 5 ? "Good" : "Average"}
                    </Badge>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Return on Equity (ROE)</span>
                  <div className="text-right">
                    <span className="font-semibold">{financialData.returnOnEquity.toFixed(1)}%</span>
                    <Badge variant={financialData.returnOnEquity >= 15 ? "default" : "secondary"} className="ml-2">
                      {financialData.returnOnEquity >= 15 ? "Excellent" : "Good"}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Key Performance Indicators</CardTitle>
                <CardDescription>Overall business performance metrics</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Gross Profit Margin</span>
                  <div className="text-right">
                    <span className="font-semibold">{financialData.grossProfitMargin.toFixed(1)}%</span>
                    <Badge variant={financialData.grossProfitMargin >= 25 ? "default" : "secondary"} className="ml-2">
                      {financialData.grossProfitMargin >= 25 ? "Healthy" : "Needs Improvement"}
                    </Badge>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Net Profit Margin</span>
                  <div className="text-right">
                    <span className="font-semibold">{((financialData.netProfit / financialData.totalRevenue) * 100).toFixed(1)}%</span>
                    <Badge variant={((financialData.netProfit / financialData.totalRevenue) * 100) >= 10 ? "default" : "secondary"} className="ml-2">
                      {((financialData.netProfit / financialData.totalRevenue) * 100) >= 10 ? "Excellent" : "Good"}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
