"use client"

import { useState, useMemo } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { useStore } from "@/lib/store"
import { 
  Download, Filter, IndianRupee, TrendingUp, TrendingDown, 
  ShoppingCart, Users, Calendar, Target, Award, BarChart3,
  PieChart, Activity, Clock, Gem, Scale, CreditCard
} from "lucide-react"

export function EnhancedSalesAnalytics() {
  const { sales, customers, inventory } = useStore()
  const [activeTab, setActiveTab] = useState("overview")
  const [dateRange, setDateRange] = useState("month")
  const [customerFilter, setCustomerFilter] = useState("all")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [paymentFilter, setPaymentFilter] = useState("all")

  // Enhanced sales analytics
  const analytics = useMemo(() => {
    // Filter sales based on selected criteria
    const filteredSales = sales.filter((sale) => {
      // Date filtering
      const saleDate = new Date(sale.date)
      const now = new Date()
      
      if (dateRange === "today") {
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
        if (saleDate < today) return false
      } else if (dateRange === "week") {
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        if (saleDate < weekAgo) return false
      } else if (dateRange === "month") {
        const monthAgo = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate())
        if (saleDate < monthAgo) return false
      } else if (dateRange === "year") {
        const yearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate())
        if (saleDate < yearAgo) return false
      }
      
      // Customer filtering
      if (customerFilter !== "all" && sale.customer.id !== customerFilter) return false
      
      // Payment status filtering
      if (paymentFilter !== "all" && sale.status !== paymentFilter) return false
      
      return true
    })

    // Basic metrics
    const totalSales = filteredSales.reduce((sum, sale) => sum + (sale.total || 0), 0)
    const totalTransactions = filteredSales.length
    const avgTransactionValue = totalTransactions > 0 ? totalSales / totalTransactions : 0
    
    // Weight analytics
    const totalNetWeight = filteredSales.reduce(
      (sum, sale) => sum + sale.items.reduce((itemSum, item) => itemSum + (item.netWeight || 0), 0),
      0
    )
    const totalGrossWeight = filteredSales.reduce(
      (sum, sale) => sum + sale.items.reduce((itemSum, item) => itemSum + (item.grossWeight || 0), 0),
      0
    )
    
    // Customer analytics
    const uniqueCustomers = new Set(filteredSales.map((sale) => sale.customer.id)).size
    const repeatCustomers = filteredSales.filter(sale => {
      const customerSales = filteredSales.filter(s => s.customer.id === sale.customer.id)
      return customerSales.length > 1
    }).length
    
    // Payment method analytics
    const paymentMethods = filteredSales.reduce((acc, sale) => {
      const method = sale.paymentMethod || "cash"
      acc[method] = (acc[method] || 0) + 1
      return acc
    }, {} as Record<string, number>)
    
    // Category performance
    const categoryPerformance = filteredSales.reduce((acc, sale) => {
      sale.items.forEach(item => {
        const category = item.item.category || "Unknown"
        if (!acc[category]) {
          acc[category] = { sales: 0, revenue: 0, weight: 0, items: 0 }
        }
        acc[category].sales += 1
        acc[category].revenue += item.amount || 0
        acc[category].weight += item.netWeight || 0
        acc[category].items += 1
      })
      return acc
    }, {} as Record<string, { sales: number, revenue: number, weight: number, items: number }>)
    
    const topCategories = Object.entries(categoryPerformance)
      .sort(([,a], [,b]) => b.revenue - a.revenue)
      .slice(0, 5)
    
    // Metal type performance
    const metalPerformance = filteredSales.reduce((acc, sale) => {
      sale.items.forEach(item => {
        const metal = item.item.metalType || "Unknown"
        if (!acc[metal]) {
          acc[metal] = { sales: 0, revenue: 0, weight: 0 }
        }
        acc[metal].sales += 1
        acc[metal].revenue += item.amount || 0
        acc[metal].weight += item.netWeight || 0
      })
      return acc
    }, {} as Record<string, { sales: number, revenue: number, weight: number }>)
    
    // Top customers
    const customerPerformance = filteredSales.reduce((acc, sale) => {
      const customerId = sale.customer.id
      if (!acc[customerId]) {
        acc[customerId] = {
          customer: sale.customer,
          totalPurchases: 0,
          totalAmount: 0,
          transactionCount: 0,
          avgTransactionValue: 0
        }
      }
      acc[customerId].totalAmount += sale.total || 0
      acc[customerId].transactionCount += 1
      acc[customerId].totalPurchases += sale.items.length
      acc[customerId].avgTransactionValue = acc[customerId].totalAmount / acc[customerId].transactionCount
      return acc
    }, {} as Record<string, any>)
    
    const topCustomers = Object.values(customerPerformance)
      .sort((a: any, b: any) => b.totalAmount - a.totalAmount)
      .slice(0, 10)
    
    // Time-based analytics
    const dailySales = filteredSales.reduce((acc, sale) => {
      const date = sale.date
      acc[date] = (acc[date] || 0) + (sale.total || 0)
      return acc
    }, {} as Record<string, number>)
    
    // GST analytics
    const totalGST = filteredSales.reduce((sum, sale) => {
      return sum + (sale.cgst || 0) + (sale.sgst || 0) + (sale.igst || 0)
    }, 0)
    
    // Exchange analytics
    const exchangeTransactions = filteredSales.filter(sale => sale.hasExchange || false).length
    const totalExchangeValue = filteredSales.reduce((sum, sale) => sum + (sale.exchangeValue || 0), 0)
    
    // Status distribution
    const statusDistribution = filteredSales.reduce((acc, sale) => {
      const status = sale.status || "unknown"
      acc[status] = (acc[status] || 0) + 1
      return acc
    }, {} as Record<string, number>)
    
    // Growth calculations (mock data for demonstration)
    const previousPeriodSales = totalSales * 0.85 // Mock 15% growth
    const salesGrowth = ((totalSales - previousPeriodSales) / previousPeriodSales) * 100
    
    return {
      totalSales,
      totalTransactions,
      avgTransactionValue,
      totalNetWeight,
      totalGrossWeight,
      uniqueCustomers,
      repeatCustomers,
      paymentMethods,
      topCategories,
      metalPerformance,
      topCustomers,
      dailySales,
      totalGST,
      exchangeTransactions,
      totalExchangeValue,
      statusDistribution,
      salesGrowth,
      customerRetentionRate: repeatCustomers > 0 ? (repeatCustomers / uniqueCustomers) * 100 : 0,
      avgOrderValue: avgTransactionValue,
      conversionRate: 85.5, // Mock data
      profitMargin: 22.5, // Mock data
    }
  }, [sales, dateRange, customerFilter, categoryFilter, paymentFilter])

  const exportReport = () => {
    const reportData = {
      summary: analytics,
      dateRange,
      filters: { customerFilter, categoryFilter, paymentFilter },
      generatedAt: new Date().toISOString()
    }
    
    const dataStr = JSON.stringify(reportData, null, 2)
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)
    
    const exportFileDefaultName = `sales-analytics-${new Date().toISOString().split('T')[0]}.json`
    
    const linkElement = document.createElement('a')
    linkElement.setAttribute('href', dataUri)
    linkElement.setAttribute('download', exportFileDefaultName)
    linkElement.click()
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-2xl font-bold tracking-tight">Enhanced Sales Analytics</h3>
          <p className="text-muted-foreground">Comprehensive sales performance and business intelligence</p>
        </div>
        <Button onClick={exportReport}>
          <Download className="h-4 w-4 mr-2" />
          Export Report
        </Button>
      </div>

      {/* Advanced Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Analytics Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="space-y-2">
              <Label>Date Range</Label>
              <Select value={dateRange} onValueChange={setDateRange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="week">This Week</SelectItem>
                  <SelectItem value="month">This Month</SelectItem>
                  <SelectItem value="year">This Year</SelectItem>
                  <SelectItem value="all">All Time</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label>Customer</Label>
              <Select value={customerFilter} onValueChange={setCustomerFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Customers" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Customers</SelectItem>
                  {customers.slice(0, 10).map((customer) => (
                    <SelectItem key={customer.id} value={customer.id}>
                      {customer.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Category</Label>
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="rings">Rings</SelectItem>
                  <SelectItem value="necklaces">Necklaces</SelectItem>
                  <SelectItem value="earrings">Earrings</SelectItem>
                  <SelectItem value="bangles">Bangles</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Payment Status</Label>
              <Select value={paymentFilter} onValueChange={setPaymentFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="paid">Paid</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="partial">Partial</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>&nbsp;</Label>
              <Button 
                variant="outline" 
                onClick={() => {
                  setDateRange("month")
                  setCustomerFilter("all")
                  setCategoryFilter("all")
                  setPaymentFilter("all")
                }}
                className="w-full"
              >
                Reset Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="performance" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Performance
          </TabsTrigger>
          <TabsTrigger value="customers" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Customers
          </TabsTrigger>
          <TabsTrigger value="products" className="flex items-center gap-2">
            <Gem className="h-4 w-4" />
            Products
          </TabsTrigger>
          <TabsTrigger value="financial" className="flex items-center gap-2">
            <IndianRupee className="h-4 w-4" />
            Financial
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Sales</CardTitle>
                <IndianRupee className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">₹{analytics.totalSales.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  {analytics.salesGrowth > 0 ? "+" : ""}{analytics.salesGrowth.toFixed(1)}% from last period
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Transactions</CardTitle>
                <ShoppingCart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.totalTransactions}</div>
                <p className="text-xs text-muted-foreground">
                  Avg: ₹{analytics.avgTransactionValue.toLocaleString()}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Customers</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.uniqueCustomers}</div>
                <p className="text-xs text-muted-foreground">
                  {analytics.customerRetentionRate.toFixed(1)}% retention rate
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Net Weight Sold</CardTitle>
                <Scale className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">{analytics.totalNetWeight.toFixed(1)}g</div>
                <p className="text-xs text-muted-foreground">
                  Gross: {analytics.totalGrossWeight.toFixed(1)}g
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Business Intelligence Cards */}
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Business Health
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Conversion Rate</span>
                    <span>{analytics.conversionRate}%</span>
                  </div>
                  <Progress value={analytics.conversionRate} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Profit Margin</span>
                    <span>{analytics.profitMargin}%</span>
                  </div>
                  <Progress value={analytics.profitMargin} className="h-2" />
                </div>
                <div className="pt-2 border-t">
                  <p className="text-sm text-muted-foreground">
                    {analytics.exchangeTransactions} exchange transactions
                  </p>
                  <p className="text-sm text-muted-foreground">
                    ₹{analytics.totalExchangeValue.toLocaleString()} exchange value
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Payment Methods
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(analytics.paymentMethods).map(([method, count]) => (
                    <div key={method}>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="capitalize">{method.replace('_', ' ')}</span>
                        <span>{count} transactions</span>
                      </div>
                      <Progress value={(count / analytics.totalTransactions) * 100} className="h-2" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Transaction Status
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(analytics.statusDistribution).map(([status, count]) => (
                    <div key={status}>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="capitalize">{status}</span>
                        <span>{count}</span>
                      </div>
                      <Progress value={(count / analytics.totalTransactions) * 100} className="h-2" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Top Performing Categories</CardTitle>
              <CardDescription>Revenue and sales performance by jewelry categories</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Category</TableHead>
                    <TableHead>Revenue</TableHead>
                    <TableHead>Items Sold</TableHead>
                    <TableHead>Avg. Value</TableHead>
                    <TableHead>Weight Sold</TableHead>
                    <TableHead>% of Total</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {analytics.topCategories.map(([category, data]) => (
                    <TableRow key={category}>
                      <TableCell>
                        <Badge variant="outline">{category}</Badge>
                      </TableCell>
                      <TableCell className="font-medium">
                        ₹{data.revenue.toLocaleString()}
                      </TableCell>
                      <TableCell>{data.items}</TableCell>
                      <TableCell>₹{(data.revenue / data.items).toLocaleString()}</TableCell>
                      <TableCell className="text-blue-600 font-medium">
                        {data.weight.toFixed(1)}g
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Progress
                            value={(data.revenue / analytics.totalSales) * 100}
                            className="h-2 w-16"
                          />
                          <span className="text-sm">
                            {((data.revenue / analytics.totalSales) * 100).toFixed(1)}%
                          </span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Metal Performance</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {Object.entries(analytics.metalPerformance).map(([metal, data]) => (
                  <div key={metal}>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="capitalize font-medium">{metal}</span>
                      <span>₹{data.revenue.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between text-xs text-muted-foreground mb-1">
                      <span>{data.weight.toFixed(1)}g sold</span>
                      <span>{data.sales} transactions</span>
                    </div>
                    <Progress value={(data.revenue / analytics.totalSales) * 100} className="h-2" />
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>GST Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">
                      ₹{analytics.totalGST.toLocaleString()}
                    </div>
                    <p className="text-sm text-muted-foreground">Total GST Collected</p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>GST Rate</span>
                      <span>3% (Standard Jewelry)</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Taxable Value</span>
                      <span>₹{(analytics.totalSales - analytics.totalGST).toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between text-sm font-medium">
                      <span>Total with GST</span>
                      <span>₹{analytics.totalSales.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Customers Tab */}
        <TabsContent value="customers" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Top Customers</CardTitle>
              <CardDescription>Highest value customers and their purchase patterns</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Customer</TableHead>
                    <TableHead>Total Purchases</TableHead>
                    <TableHead>Transaction Count</TableHead>
                    <TableHead>Avg. Transaction</TableHead>
                    <TableHead>Customer Tier</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {analytics.topCustomers.map((customer: any) => (
                    <TableRow key={customer.customer.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{customer.customer.name}</div>
                          <div className="text-sm text-muted-foreground">{customer.customer.phone}</div>
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">
                        ₹{customer.totalAmount.toLocaleString()}
                      </TableCell>
                      <TableCell>{customer.transactionCount}</TableCell>
                      <TableCell>₹{customer.avgTransactionValue.toLocaleString()}</TableCell>
                      <TableCell>
                        <Badge variant="secondary" className="capitalize">
                          {customer.customer.loyalty_tier || "bronze"}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Products Tab */}
        <TabsContent value="products" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Best Selling Items</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-sm text-muted-foreground">
                    Analysis based on quantity sold and revenue generated
                  </p>
                  {/* This would be populated with actual best-selling items */}
                  <div className="text-center py-8 text-muted-foreground">
                    <Gem className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Product performance data will be displayed here</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Inventory Turnover</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-sm text-muted-foreground">
                    How quickly inventory items are selling
                  </p>
                  <div className="text-center py-8 text-muted-foreground">
                    <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Turnover analysis will be displayed here</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Financial Tab */}
        <TabsContent value="financial" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Revenue Breakdown</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span>Gross Sales</span>
                    <span className="font-medium">₹{analytics.totalSales.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Exchange Deductions</span>
                    <span className="font-medium text-red-600">-₹{analytics.totalExchangeValue.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>GST Collected</span>
                    <span className="font-medium">₹{analytics.totalGST.toLocaleString()}</span>
                  </div>
                  <div className="border-t pt-2">
                    <div className="flex justify-between font-bold">
                      <span>Net Revenue</span>
                      <span>₹{(analytics.totalSales - analytics.totalExchangeValue).toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Profit Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600 mb-2">
                    {analytics.profitMargin}%
                  </div>
                  <p className="text-sm text-muted-foreground mb-4">Average Profit Margin</p>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Estimated Profit</span>
                      <span className="font-medium">
                        ₹{((analytics.totalSales * analytics.profitMargin) / 100).toLocaleString()}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Payment Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      ₹{analytics.totalSales.toLocaleString()}
                    </div>
                    <p className="text-sm text-muted-foreground">Total Collected</p>
                  </div>
                  <div className="space-y-2 text-sm">
                    {Object.entries(analytics.paymentMethods).map(([method, count]) => (
                      <div key={method} className="flex justify-between">
                        <span className="capitalize">{method.replace('_', ' ')}</span>
                        <span>{count} transactions</span>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
