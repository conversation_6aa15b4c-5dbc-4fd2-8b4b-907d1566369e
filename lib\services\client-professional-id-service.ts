/**
 * Client-Side Professional ID Service
 * Uses API routes to generate professional IDs from client components
 */

export class ClientProfessionalIdService {
  private baseUrl = '/api/professional-ids'

  /**
   * Generate customer ID
   */
  async generateCustomerId(): Promise<string> {
    const response = await fetch(`${this.baseUrl}/generate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ type: 'customer' })
    })

    const data = await response.json()
    if (!data.success) {
      throw new Error(data.error || 'Failed to generate customer ID')
    }

    return data.id
  }

  /**
   * Generate inventory item code and barcode
   */
  async generateInventoryItemCode(
    category: string,
    metalType: string,
    purity: string
  ): Promise<{ itemCode: string; barcode: string }> {
    const response = await fetch(`${this.baseUrl}/generate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        type: 'inventory',
        category,
        metalType,
        purity
      })
    })

    const data = await response.json()
    if (!data.success) {
      throw new Error(data.error || 'Failed to generate inventory item code')
    }

    return {
      itemCode: data.itemCode,
      barcode: data.barcode
    }
  }

  /**
   * Generate sale ID and invoice number
   */
  async generateSaleIds(): Promise<{ saleId: string; invoiceNumber: string }> {
    const response = await fetch(`${this.baseUrl}/generate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ type: 'sale' })
    })

    const data = await response.json()
    if (!data.success) {
      throw new Error(data.error || 'Failed to generate sale IDs')
    }

    return {
      saleId: data.saleId,
      invoiceNumber: data.invoiceNumber
    }
  }

  /**
   * Generate invoice number
   */
  async generateInvoiceNumber(): Promise<string> {
    const response = await fetch(`${this.baseUrl}/generate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ type: 'invoice' })
    })

    const data = await response.json()
    if (!data.success) {
      throw new Error(data.error || 'Failed to generate invoice number')
    }

    return data.invoiceNumber
  }

  /**
   * Generate repair job number
   */
  async generateRepairJobNumber(): Promise<string> {
    const response = await fetch(`${this.baseUrl}/generate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ type: 'repair' })
    })

    const data = await response.json()
    if (!data.success) {
      throw new Error(data.error || 'Failed to generate repair job number')
    }

    return data.repairJobNumber
  }

  /**
   * Generate scheme number
   */
  async generateSchemeNumber(): Promise<string> {
    const response = await fetch(`${this.baseUrl}/generate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ type: 'scheme' })
    })

    const data = await response.json()
    if (!data.success) {
      throw new Error(data.error || 'Failed to generate scheme number')
    }

    return data.schemeNumber
  }

  /**
   * Generate purchase order number
   */
  async generatePurchaseOrderNumber(): Promise<string> {
    const response = await fetch(`${this.baseUrl}/generate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ type: 'purchase' })
    })

    const data = await response.json()
    if (!data.success) {
      throw new Error(data.error || 'Failed to generate purchase order number')
    }

    return data.purchaseOrderNumber
  }

  /**
   * Generate supplier code
   */
  async generateSupplierCode(): Promise<string> {
    const response = await fetch(`${this.baseUrl}/generate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ type: 'supplier' })
    })

    const data = await response.json()
    if (!data.success) {
      throw new Error(data.error || 'Failed to generate supplier code')
    }

    return data.supplierCode
  }

  /**
   * Generate exchange number
   */
  async generateExchangeNumber(): Promise<string> {
    const response = await fetch(`${this.baseUrl}/generate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ type: 'exchange' })
    })

    const data = await response.json()
    if (!data.success) {
      throw new Error(data.error || 'Failed to generate exchange number')
    }

    return data.exchangeNumber
  }

  /**
   * Generate receipt number
   */
  async generateReceiptNumber(): Promise<string> {
    const response = await fetch(`${this.baseUrl}/generate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ type: 'receipt' })
    })

    const data = await response.json()
    if (!data.success) {
      throw new Error(data.error || 'Failed to generate receipt number')
    }

    return data.receiptNumber
  }

  /**
   * Validate ID format
   */
  async validateId(type: string, id: string): Promise<{
    isValid: boolean
    parsed?: any
  }> {
    const response = await fetch(`${this.baseUrl}/validate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ type, id })
    })

    const data = await response.json()
    if (!data.success) {
      throw new Error(data.error || 'Failed to validate ID')
    }

    return {
      isValid: data.isValid,
      parsed: data.parsed
    }
  }

  /**
   * Get current sequences
   */
  async getCurrentSequences(): Promise<Record<string, number>> {
    const response = await fetch(`${this.baseUrl}/generate?action=sequences`)
    const data = await response.json()
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to get sequences')
    }

    return data.sequences
  }

  /**
   * Get ID generation report
   */
  async getIdGenerationReport(): Promise<{
    sequences: Record<string, number>
    totalGenerated: number
    financialYear: string
    lastUpdated: string
  }> {
    const response = await fetch(`${this.baseUrl}/generate?action=report`)
    const data = await response.json()
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to get report')
    }

    return data.report
  }

  /**
   * Validate sequence integrity
   */
  async validateSequenceIntegrity(): Promise<{
    isValid: boolean
    issues: Array<{
      entityType: string
      issue: string
      recommendation: string
    }>
  }> {
    const response = await fetch(`${this.baseUrl}/generate?action=integrity`)
    const data = await response.json()
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to validate integrity')
    }

    return data.integrity
  }
}
