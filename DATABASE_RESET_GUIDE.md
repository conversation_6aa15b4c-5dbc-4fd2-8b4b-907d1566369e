# 🗄️ Database Reset and Professional ID Import Guide

## ⚠️ **IMPORTANT WARNING**
**This process will COMPLETELY DELETE all existing data in your database!**  
Make sure to backup any important data before proceeding.

## 🚀 **Quick Import (Automated)**

### **Windows:**
```bash
# Run the automated import script
scripts\import-professional-system.bat
```

### **Linux/Mac:**
```bash
# Make script executable
chmod +x scripts/import-professional-system.sh

# Run the automated import script
./scripts/import-professional-system.sh
```

## 📋 **Manual Import (Step by Step)**

### **Step 1: Reset Database**
```sql
-- Connect to MySQL
mysql -u root -p

-- Reset the database (WARNING: Deletes all data!)
SET @CONFIRM_RESET = 'YES_DELETE_ALL_DATA_AND_RESET';
source scripts/reset-database.sql;
```

### **Step 2: Import Updated Schema**
```bash
# Import the comprehensive schema with professional ID support
mysql -u root -p jewellers_db < lib/database/updated-comprehensive-schema.sql
```

### **Step 3: Import Professional Sample Data**
```bash
# Import sample data with professional business IDs
mysql -u root -p jewellers_db < lib/database/professional-sample-data.sql
```

### **Step 4: Verify Import**
```sql
-- Connect to the database
mysql -u root -p jewellers_db

-- Check table counts
SELECT 'users' as table_name, COUNT(*) as count FROM users
UNION ALL SELECT 'customers', COUNT(*) FROM customers
UNION ALL SELECT 'inventory', COUNT(*) FROM inventory
UNION ALL SELECT 'sales', COUNT(*) FROM sales
UNION ALL SELECT 'schemes', COUNT(*) FROM schemes
UNION ALL SELECT 'suppliers', COUNT(*) FROM suppliers
UNION ALL SELECT 'id_sequences', COUNT(*) FROM id_sequences;

-- Check professional customer IDs
SELECT customer_code, name FROM customers ORDER BY customer_code LIMIT 5;

-- Check professional inventory codes
SELECT id as item_code, barcode, name FROM inventory ORDER BY id LIMIT 5;

-- Check professional invoice numbers
SELECT sale_number, invoice_number, total_amount FROM sales ORDER BY sale_number LIMIT 3;

-- Check sequence status
SELECT entity_type, current_sequence, financial_year FROM id_sequences ORDER BY entity_type;
```

## ✅ **Expected Results After Import**

### **Professional ID Formats:**
- **Customer IDs:** `CUST-2024-000001`, `CUST-2024-000002`, etc.
- **Inventory Codes:** `RGGD22-2024-0001`, `CHGD22-2024-0002`, etc.
- **Barcodes:** `SJ01224000001`, `SJ06224000002`, etc.
- **Invoice Numbers:** `INV-2024-25-000001`, `INV-2024-25-000002`, etc.
- **Sale Numbers:** `SALE-2024-000001`, `SALE-2024-000002`, etc.
- **Scheme Numbers:** `SCH-2024-25-000001`, `SCH-2024-25-000002`, etc.
- **Supplier Codes:** `SUPP-2024-0001`, `SUPP-2024-0002`, etc.

### **Sample Data Included:**
- **5 Customers** with professional customer codes
- **5 Inventory Items** with professional item codes and barcodes
- **3 Sales Transactions** with professional invoice numbers
- **3 Schemes** with professional scheme numbers
- **3 Suppliers** with professional supplier codes
- **Complete Sequence Management** for all entity types

### **Business Configuration:**
- **Business Name:** Shree Jewellers
- **GST Number:** 27ABCDE1234F1Z5
- **Current Metal Rates** for Gold, Silver, Platinum
- **Jewelry Categories** including Indian traditional items
- **User Accounts** with different roles

## 🔐 **Default Login Credentials**
After import, you can login with:
- **Email:** `<EMAIL>`
- **Password:** `admin123`

**⚠️ IMPORTANT: Change these credentials immediately after first login!**

## 🧪 **Testing the Professional ID System**

### **1. Test Customer Creation:**
- Go to Customers → Add New Customer
- Notice the preview customer code: `CUST-2024-XXXXXX`
- Save the customer and verify it gets a real professional ID

### **2. Test Inventory Creation:**
- Go to Inventory → Add New Item
- Select category (e.g., "Rings"), metal ("gold"), purity ("22K")
- Notice the preview: `RGGD22-2024-XXXX` and barcode: `SJ01224XXXXXX`
- Save the item and verify it gets real professional codes

### **3. Test Sales Transaction:**
- Create a new sale
- Notice professional invoice number generation
- Verify the sale gets proper professional IDs

## 🔧 **Troubleshooting**

### **Common Issues:**

#### **"Access denied for user 'root'"**
- Make sure MySQL is running
- Check your MySQL root password
- Try: `mysql -u root -p` and enter your password

#### **"Database 'jewellers_db' doesn't exist"**
- The reset script should create it automatically
- Manually create: `CREATE DATABASE jewellers_db;`

#### **"Table doesn't exist" errors**
- Make sure the schema import completed successfully
- Check for any error messages during import
- Re-run the schema import if needed

#### **"No data found" after import**
- Make sure the data import completed successfully
- Check for any error messages during data import
- Re-run the data import if needed

### **Verification Commands:**
```sql
-- Check if database exists
SHOW DATABASES LIKE 'jewellers_db';

-- Check if tables exist
USE jewellers_db;
SHOW TABLES;

-- Check if data exists
SELECT COUNT(*) FROM customers;
SELECT COUNT(*) FROM inventory;
SELECT COUNT(*) FROM id_sequences;
```

## 🎯 **Next Steps After Import**

1. **Start the application:** `npm run dev`
2. **Login** with the default credentials
3. **Change default password** immediately
4. **Update business settings** with your actual business information
5. **Test professional ID generation** by creating new records
6. **Configure metal rates** with current market prices
7. **Add your actual inventory categories** if needed
8. **Train users** on the new professional ID formats

## 📊 **Professional ID Benefits**

After the import, your system will have:

✅ **Professional Appearance** - All IDs look business-standard  
✅ **Easy Recognition** - Staff can identify item types from codes  
✅ **Better Organization** - Systematic numbering for all entities  
✅ **Audit Trail** - Clear sequence tracking for compliance  
✅ **Scalability** - Supports high transaction volumes  
✅ **Financial Year Support** - Automatic FY transitions  
✅ **Data Integrity** - Built-in validation and error handling  

**Your jewelry management system is now ready with professional business-standard identifiers!** 💎✨🎯
