-- COMPREHENSIVE SAMPLE DATA FOR <PERSON><PERSON><PERSON><PERSON>RY MANAGEMENT SYSTEM
-- Version: 4.0.0
-- Date: January 31, 2025
-- Description: Realistic sample data for testing all system features

USE jewellers_db;

-- Disable foreign key checks temporarily for data insertion
SET FOREIGN_KEY_CHECKS = 0;

-- ============================================================================
-- 1. BUSINESS SETTINGS
-- ============================================================================

INSERT INTO business_settings (
  business_name, business_type, address, city, state, pincode, country,
  phone, email, website, gst_number, pan_number, license_number, established_year,
  business_hours, cgst_rate, sgst_rate, igst_rate, tcs_rate, tds_rate,
  hsn_codes, currency_symbol, currency_code, decimal_places,
  date_format, time_format, timezone, language, theme,
  auto_backup, backup_frequency, backup_retention,
  low_stock_alert, low_stock_threshold, scheme_reminders, repair_reminders,
  email_notifications, sms_notifications
) VALUES (
  'Shree Jewellers', 'jewelry_store', 
  '123 Main Street, Commercial Complex, Zaveri Bazaar', 'Mumbai', 'Maharashtra', '400002', 'India',
  '+91 98765 43210', '<EMAIL>', 'www.shreejewellers.com',
  '27**********1Z5', '**********', 'JL/2024/001', '2020',
  JSON_OBJECT(
    'monday', JSON_OBJECT('open', '09:00', 'close', '19:00', 'closed', false),
    'tuesday', JSON_OBJECT('open', '09:00', 'close', '19:00', 'closed', false),
    'wednesday', JSON_OBJECT('open', '09:00', 'close', '19:00', 'closed', false),
    'thursday', JSON_OBJECT('open', '09:00', 'close', '19:00', 'closed', false),
    'friday', JSON_OBJECT('open', '09:00', 'close', '19:00', 'closed', false),
    'saturday', JSON_OBJECT('open', '09:00', 'close', '19:00', 'closed', false),
    'sunday', JSON_OBJECT('open', '10:00', 'close', '18:00', 'closed', false)
  ),
  1.50, 1.50, 3.00, 1.00, 1.00,
  JSON_OBJECT(
    'gold', '71131900',
    'silver', '71131100', 
    'diamond', '71023100',
    'platinum', '71110000'
  ),
  '₹', 'INR', 2,
  'DD/MM/YYYY', '24', 'Asia/Kolkata', 'en', 'light',
  true, 'daily', 30,
  true, 5, true, true,
  true, false
);

-- ============================================================================
-- 2. USERS - System users with different roles
-- ============================================================================

INSERT INTO users (id, name, email, phone, password_hash, role, permissions, is_active) VALUES
('user_001', 'Admin User', '<EMAIL>', '+91 98765 43210', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'super_admin', JSON_ARRAY('system_admin'), true),
('user_002', 'Manager User', '<EMAIL>', '+91 98765 43211', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'manager', JSON_ARRAY('sales_view', 'sales_create', 'sales_edit', 'inventory_view', 'inventory_create', 'customers_view', 'customers_create', 'reports_view'), true),
('user_003', 'Sales Staff', '<EMAIL>', '+91 98765 43212', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'sales_staff', JSON_ARRAY('sales_view', 'sales_create', 'customers_view', 'customers_create', 'inventory_view'), true),
('user_004', 'Accountant', '<EMAIL>', '+91 98765 43213', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'accountant', JSON_ARRAY('sales_view', 'purchases_view', 'reports_view', 'reports_export'), true),
('user_005', 'Technician', '<EMAIL>', '+91 98765 43214', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'technician', JSON_ARRAY('repairs_view', 'repairs_edit'), true);

-- ============================================================================
-- 3. METAL RATES - Current market rates
-- ============================================================================

INSERT INTO metal_rates (id, metal_type, purity, rate_per_gram, margin_percentage, effective_date, is_active) VALUES
('rate_001', 'gold', '24K', 7200.00, 5.00, CURDATE(), true),
('rate_002', 'gold', '22K', 6600.00, 5.00, CURDATE(), true),
('rate_003', 'gold', '18K', 5400.00, 5.00, CURDATE(), true),
('rate_004', 'gold', '14K', 4200.00, 5.00, CURDATE(), true),
('rate_005', 'silver', '999', 85.00, 8.00, CURDATE(), true),
('rate_006', 'silver', '925', 78.00, 8.00, CURDATE(), true),
('rate_007', 'platinum', '950', 3200.00, 3.00, CURDATE(), true);

-- ============================================================================
-- 4. INVENTORY CATEGORIES
-- ============================================================================

INSERT INTO inventory_categories (id, name, description, hsn_code, tax_rate, is_active, sort_order) VALUES
('cat_001', 'Rings', 'Gold, Silver, and Diamond Rings', '71131900', 3.00, true, 1),
('cat_002', 'Necklaces', 'Traditional and Modern Necklaces', '71131900', 3.00, true, 2),
('cat_003', 'Earrings', 'Studs, Hoops, and Danglers', '71131900', 3.00, true, 3),
('cat_004', 'Bracelets', 'Gold and Silver Bracelets', '71131900', 3.00, true, 4),
('cat_005', 'Pendants', 'Religious and Fashion Pendants', '71131900', 3.00, true, 5),
('cat_006', 'Chains', 'Gold and Silver Chains', '71131900', 3.00, true, 6),
('cat_007', 'Bangles', 'Traditional Indian Bangles', '71131900', 3.00, true, 7),
('cat_008', 'Sets', 'Complete Jewelry Sets', '71131900', 3.00, true, 8),
('cat_009', 'Diamonds', 'Loose Diamonds and Diamond Jewelry', '71023100', 0.25, true, 9),
('cat_010', 'Pearls', 'Natural and Cultured Pearls', '71161000', 3.00, true, 10);

-- ============================================================================
-- 5. CUSTOMERS - Sample customer data
-- ============================================================================

INSERT INTO customers (
  id, customer_code, name, phone, alternate_phone, email, date_of_birth, anniversary_date, gender,
  address, city, state, pincode, country,
  aadhar_number, pan_number, kyc_status,
  occupation, annual_income, customer_type, source,
  preferred_metal, communication_preference, is_active, total_purchases, loyalty_points
) VALUES
('cust_001', 'CUST001', 'Rajesh Kumar', '9876543210', '9876543211', '<EMAIL>', '1985-05-15', '2010-12-01', 'male',
 '123 Residential Complex, Andheri', 'Mumbai', 'Maharashtra', '400058', 'India',
 '123456789012', '**********', 'verified',
 'Business Owner', 1200000.00, 'premium', 'referral',
 'gold', 'phone', true, 250000.00, 2500),

('cust_002', 'CUST002', 'Priya Sharma', '9876543220', NULL, '<EMAIL>', '1990-08-22', '2015-02-14', 'female',
 '456 Housing Society, Bandra', 'Mumbai', 'Maharashtra', '400050', 'India',
 '123456789013', '**********', 'verified',
 'Software Engineer', 800000.00, 'regular', 'walk-in',
 'gold', 'email', true, 150000.00, 1500),

('cust_003', 'CUST003', 'Amit Patel', '9876543230', '9876543231', '<EMAIL>', '1982-03-10', '2008-11-25', 'male',
 '789 Commercial Street, Borivali', 'Mumbai', 'Maharashtra', '400092', 'India',
 '123456789014', '**********', 'verified',
 'Doctor', 1500000.00, 'vip', 'referral',
 'diamond', 'phone', true, 500000.00, 5000),

('cust_004', 'CUST004', 'Sunita Joshi', '9876543240', NULL, '<EMAIL>', '1988-07-18', '2012-04-05', 'female',
 '321 Apartment Complex, Thane', 'Thane', 'Maharashtra', '400601', 'India',
 '123456789015', '**********', 'pending',
 'Teacher', 600000.00, 'regular', 'advertisement',
 'silver', 'email', true, 75000.00, 750),

('cust_005', 'CUST005', 'Vikram Singh', '9876543250', '9876543251', '<EMAIL>', '1975-12-05', '2000-06-15', 'male',
 '654 Villa Complex, Juhu', 'Mumbai', 'Maharashtra', '400049', 'India',
 '123456789016', '**********', 'verified',
 'Businessman', 2000000.00, 'vip', 'referral',
 'platinum', 'phone', true, 800000.00, 8000);

-- ============================================================================
-- 6. SUPPLIERS - Sample supplier data
-- ============================================================================

INSERT INTO suppliers (
  id, supplier_code, name, contact_person, phone, alternate_phone, email,
  address, city, state, pincode, country,
  gst_number, pan_number, payment_terms, credit_limit, credit_days,
  specializes_in, rating, quality_rating, delivery_rating, is_active, is_preferred
) VALUES
('supp_001', 'SUPP001', 'Rajesh Gold Suppliers', 'Rajesh Kumar', '+91 98765 43210', '+91 98765 43211', '<EMAIL>',
 '123 Jewelry Market, Zaveri Bazaar', 'Mumbai', 'Maharashtra', '400002', 'India',
 '27**********1Z5', '**********', 'net_30', 500000.00, 30,
 JSON_ARRAY('gold', 'silver'), 4.5, 4.8, 4.2, true, true),

('supp_002', 'SUPP002', 'Diamond House Pvt Ltd', 'Suresh Patel', '+91 98765 43220', NULL, '<EMAIL>',
 '456 Diamond District, Varachha', 'Surat', 'Gujarat', '395006', 'India',
 '24**********2L6', '**********', 'net_15', 1000000.00, 15,
 JSON_ARRAY('diamond', 'precious_stones'), 4.8, 4.9, 4.7, true, true),

('supp_003', 'SUPP003', 'Silver Craft Industries', 'Amit Sharma', '+91 98765 43230', '+91 98765 43231', '<EMAIL>',
 '789 Silver Market, Johari Bazaar', 'Jaipur', 'Rajasthan', '302003', 'India',
 '08**********3S7', '**********', 'net_45', 300000.00, 45,
 JSON_ARRAY('silver', 'artificial_jewelry'), 4.2, 4.0, 4.5, true, false);

-- ============================================================================
-- 7. INVENTORY - Sample jewelry items
-- ============================================================================

INSERT INTO inventory (
  id, barcode, name, description, category_id, metal_type, purity,
  gross_weight, stone_weight, stone_details, stone_amount,
  making_charges, other_charges, current_value, selling_price, mrp,
  stock, min_stock, max_stock, size, gender, occasion, design_number,
  location, status, hsn_code, images, tags
) VALUES
('GOLD001', '1234567890123', 'Gold Ring with Diamond', 'Beautiful 22K gold ring with solitaire diamond', 'cat_001', 'gold', '22K',
 8.500, 0.250, '1 Solitaire Diamond, 0.25ct, VS1, F Color', 25000.00,
 8500.00, 500.00, 81200.00, 85000.00, 90000.00,
 1, 1, 5, '16', 'female', 'engagement', 'GR001',
 'Showcase A1', 'active', '71131900', 
 JSON_ARRAY('/images/gold-ring-001.jpg'), JSON_ARRAY('gold', 'diamond', 'ring', 'engagement')),

('GOLD002', '1234567890124', '22K Gold Chain', 'Traditional 22K gold chain for daily wear', 'cat_006', 'gold', '22K',
 25.000, 0.000, NULL, 0.00,
 12500.00, 1000.00, 178500.00, 185000.00, 195000.00,
 2, 1, 10, '20 inches', 'unisex', 'daily_wear', 'GC001',
 'Showcase B2', 'active', '71131900',
 JSON_ARRAY('/images/gold-chain-001.jpg'), JSON_ARRAY('gold', 'chain', 'traditional')),

('SILVER001', '1234567890125', 'Silver Bangles Set', 'Set of 4 traditional silver bangles', 'cat_007', 'silver', '925',
 120.000, 0.000, NULL, 0.00,
 2400.00, 200.00, 11960.00, 12500.00, 13500.00,
 3, 2, 15, 'Medium', 'female', 'traditional', 'SB001',
 'Showcase C1', 'active', '71131100',
 JSON_ARRAY('/images/silver-bangles-001.jpg'), JSON_ARRAY('silver', 'bangles', 'traditional', 'set')),

('DIAMOND001', '1234567890126', 'Diamond Earrings', 'Elegant diamond stud earrings in 18K gold', 'cat_003', 'gold', '18K',
 6.500, 1.200, '2 Diamonds, 0.6ct each, VVS1, D Color', 120000.00,
 15000.00, 2000.00, 172100.00, 180000.00, 195000.00,
 1, 1, 3, 'One Size', 'female', 'party', 'DE001',
 'Showcase A3', 'active', '71023100',
 JSON_ARRAY('/images/diamond-earrings-001.jpg'), JSON_ARRAY('diamond', 'earrings', 'gold', 'party')),

('NECKLACE001', '1234567890127', 'Gold Necklace Set', 'Complete necklace set with matching earrings', 'cat_008', 'gold', '22K',
 45.000, 2.500, 'Ruby and Emerald stones', 35000.00,
 22500.00, 3000.00, 358000.00, 375000.00, 395000.00,
 1, 1, 2, 'One Size', 'female', 'wedding', 'NS001',
 'Showcase A2', 'active', '71131900',
 JSON_ARRAY('/images/necklace-set-001.jpg'), JSON_ARRAY('gold', 'necklace', 'set', 'wedding', 'ruby', 'emerald'));

-- ============================================================================
-- 8. SALES - Sample sales transactions
-- ============================================================================

INSERT INTO sales (
  id, invoice_number, customer_id, sale_date,
  subtotal, discount_amount, discount_percentage,
  cgst_amount, sgst_amount, igst_amount,
  total_amount, paid_amount, payment_method,
  exchange_amount, status, notes, created_by
) VALUES
('sale_001', 'INV-2024-001', 'cust_001', '2024-01-15',
 82000.00, 2000.00, 2.50,
 1200.00, 1200.00, 0.00,
 82400.00, 82400.00, 'cash',
 0.00, 'completed', 'Gold ring purchase for engagement', 'user_002'),

('sale_002', 'INV-2024-002', 'cust_002', '2024-01-20',
 180000.00, 5000.00, 2.78,
 2625.00, 2625.00, 0.00,
 180250.00, 180250.00, 'card',
 0.00, 'completed', 'Gold chain for festival', 'user_003'),

('sale_003', 'INV-2024-003', 'cust_003', '2024-01-25',
 175000.00, 0.00, 0.00,
 2625.00, 2625.00, 0.00,
 180250.00, 150000.00, 'mixed',
 25000.00, 'completed', 'Diamond earrings with old gold exchange', 'user_002');

-- ============================================================================
-- 9. SALE ITEMS - Items in sales transactions
-- ============================================================================

INSERT INTO sale_items (
  id, sale_id, inventory_id, item_name, item_description,
  metal_type, purity, gross_weight, stone_weight, net_weight,
  rate_per_gram, making_charges, stone_amount, other_charges,
  quantity, unit_price, discount_amount, total_amount,
  hsn_code, tax_rate, cgst_amount, sgst_amount, igst_amount
) VALUES
('sitem_001', 'sale_001', 'GOLD001', 'Gold Ring with Diamond', 'Beautiful 22K gold ring with solitaire diamond',
 'gold', '22K', 8.500, 0.250, 8.250,
 6600.00, 8500.00, 25000.00, 500.00,
 1, 82000.00, 2000.00, 80000.00,
 '71131900', 3.00, 1200.00, 1200.00, 0.00),

('sitem_002', 'sale_002', 'GOLD002', '22K Gold Chain', 'Traditional 22K gold chain for daily wear',
 'gold', '22K', 25.000, 0.000, 25.000,
 6600.00, 12500.00, 0.00, 1000.00,
 1, 180000.00, 5000.00, 175000.00,
 '71131900', 3.00, 2625.00, 2625.00, 0.00),

('sitem_003', 'sale_003', 'DIAMOND001', 'Diamond Earrings', 'Elegant diamond stud earrings in 18K gold',
 'gold', '18K', 6.500, 1.200, 5.300,
 5400.00, 15000.00, 120000.00, 2000.00,
 1, 175000.00, 0.00, 175000.00,
 '71023100', 0.25, 43.75, 43.75, 0.00);

-- ============================================================================
-- 10. SCHEMES - Sample gold schemes
-- ============================================================================

INSERT INTO schemes (
  id, scheme_number, customer_id, scheme_name, scheme_type,
  total_amount, monthly_amount, paid_amount, duration_months,
  start_date, maturity_date, gold_rate_locked, bonus_percentage,
  status, auto_debit, reminder_enabled, notes, created_by
) VALUES
('scheme_001', 'SCH-2024-001', 'cust_001', 'Gold Savings Scheme', 'monthly',
 120000.00, 10000.00, 30000.00, 12,
 '2024-01-01', '2024-12-31', 6600.00, 5.00,
 'active', false, true, '12-month gold savings plan', 'user_002'),

('scheme_002', 'SCH-2024-002', 'cust_002', 'Festival Special Scheme', 'advance',
 60000.00, 0.00, 60000.00, 6,
 '2024-02-01', '2024-07-31', 6600.00, 8.00,
 'active', false, true, 'Advance payment scheme for festival', 'user_003'),

('scheme_003', 'SCH-2024-003', 'cust_004', 'Monthly Gold Plan', 'monthly',
 84000.00, 7000.00, 14000.00, 12,
 '2024-01-15', '2025-01-14', 6600.00, 3.00,
 'active', true, true, 'Regular monthly savings', 'user_002');

-- ============================================================================
-- 11. SCHEME PAYMENTS - Sample scheme payment records
-- ============================================================================

INSERT INTO scheme_payments (
  id, scheme_id, payment_date, amount, payment_method,
  status, receipt_number, gold_rate_on_date, gold_weight_equivalent,
  notes, created_by
) VALUES
('spay_001', 'scheme_001', '2024-01-01', 10000.00, 'cash',
 'completed', 'RCP-001', 6600.00, 1.515,
 'First payment', 'user_002'),

('spay_002', 'scheme_001', '2024-02-01', 10000.00, 'upi',
 'completed', 'RCP-002', 6650.00, 1.504,
 'Second payment', 'user_002'),

('spay_003', 'scheme_001', '2024-03-01', 10000.00, 'cash',
 'completed', 'RCP-003', 6700.00, 1.493,
 'Third payment', 'user_002'),

('spay_004', 'scheme_002', '2024-02-01', 60000.00, 'bank_transfer',
 'completed', 'RCP-004', 6650.00, 9.023,
 'Full advance payment', 'user_003'),

('spay_005', 'scheme_003', '2024-01-15', 7000.00, 'card',
 'completed', 'RCP-005', 6600.00, 1.061,
 'First payment', 'user_002'),

('spay_006', 'scheme_003', '2024-02-15', 7000.00, 'upi',
 'completed', 'RCP-006', 6650.00, 1.053,
 'Second payment', 'user_002');

-- ============================================================================
-- 12. REPAIRS - Sample repair orders
-- ============================================================================

INSERT INTO repairs (
  id, repair_number, customer_id, item_name, item_description,
  repair_type, problem_description, repair_instructions,
  received_date, promised_date, started_date, completed_date,
  estimated_cost, actual_cost, advance_paid,
  status, priority, assigned_to, quality_check_by,
  warranty_period, special_instructions, created_by
) VALUES
('repair_001', 'REP-2024-001', 'cust_001', 'Gold Chain', '22K gold chain with broken link',
 'chain_repair', 'Chain link is broken', 'Repair the broken link and polish the chain',
 '2024-01-10', '2024-01-15', '2024-01-11', '2024-01-14',
 1500.00, 1200.00, 500.00,
 'completed', 'normal', 'user_005', 'user_002',
 30, 'Handle with care', 'user_003'),

('repair_002', 'REP-2024-002', 'cust_002', 'Silver Ring', '925 silver ring needs resizing',
 'resize', 'Ring is too small', 'Increase ring size from 14 to 16',
 '2024-01-12', '2024-01-18', '2024-01-13', NULL,
 800.00, 0.00, 300.00,
 'in_progress', 'normal', 'user_005', NULL,
 15, 'Customer wants it ready for anniversary', 'user_003'),

('repair_003', 'REP-2024-003', 'cust_003', 'Diamond Earrings', 'Diamond earrings missing stone',
 'stone_setting', 'One diamond is missing', 'Replace missing diamond and secure setting',
 '2024-01-20', '2024-01-30', NULL, NULL,
 15000.00, 0.00, 5000.00,
 'received', 'high', 'user_005', NULL,
 90, 'Use matching diamond quality', 'user_002');

-- ============================================================================
-- 13. PURCHASES - Sample purchase orders
-- ============================================================================

INSERT INTO purchases (
  id, purchase_number, supplier_id, purchase_date, expected_delivery_date,
  subtotal, discount_amount, cgst_amount, sgst_amount, igst_amount,
  total_amount, paid_amount, status, payment_status,
  supplier_invoice_number, supplier_invoice_date, notes, created_by
) VALUES
('purch_001', 'PO-2024-001', 'supp_001', '2024-01-05', '2024-01-10',
 200000.00, 5000.00, 2925.00, 2925.00, 0.00,
 200850.00, 100000.00, 'received', 'partial',
 'SUPP-INV-001', '2024-01-05', 'Gold jewelry purchase for inventory', 'user_002'),

('purch_002', 'PO-2024-002', 'supp_002', '2024-01-15', '2024-01-20',
 500000.00, 10000.00, 1225.00, 1225.00, 0.00,
 492450.00, 492450.00, 'completed', 'completed',
 'DH-INV-002', '2024-01-15', 'Diamond jewelry for premium collection', 'user_002'),

('purch_003', 'PO-2024-003', 'supp_003', '2024-01-25', '2024-02-01',
 150000.00, 3000.00, 2205.00, 2205.00, 0.00,
 151410.00, 0.00, 'ordered', 'pending',
 NULL, NULL, 'Silver jewelry for festival season', 'user_002');

-- ============================================================================
-- 14. EXCHANGE RATES - Sample exchange rates for old gold/silver
-- ============================================================================

INSERT INTO exchange_rates (id, metal_type, purity, rate_per_gram, margin_percentage, effective_date, is_active) VALUES
('exrate_001', 'gold', '24K', 6800.00, 5.00, CURDATE(), true),
('exrate_002', 'gold', '22K', 6200.00, 5.00, CURDATE(), true),
('exrate_003', 'gold', '18K', 5100.00, 5.00, CURDATE(), true),
('exrate_004', 'silver', '999', 80.00, 8.00, CURDATE(), true),
('exrate_005', 'silver', '925', 74.00, 8.00, CURDATE(), true);

-- ============================================================================
-- 15. EXCHANGE TRANSACTIONS - Sample exchange transactions
-- ============================================================================

INSERT INTO exchange_transactions (
  id, transaction_number, customer_id, transaction_date, total_amount,
  payment_method, status, notes, created_by
) VALUES
('exch_001', 'EXG-2024-001', 'cust_001', '2024-01-25', 25000.00,
 'adjustment', 'completed', 'Old gold exchange for new purchase', 'user_002'),

('exch_002', 'EXG-2024-002', 'cust_003', '2024-01-28', 15600.00,
 'cash', 'completed', 'Silver jewelry exchange', 'user_003');

-- ============================================================================
-- 16. EXCHANGE ITEMS - Items in exchange transactions
-- ============================================================================

INSERT INTO exchange_items (
  id, transaction_id, item_description, metal_type, purity,
  gross_weight, stone_weight, rate_per_gram,
  item_condition, hallmark_available, notes
) VALUES
('exitem_001', 'exch_001', 'Old Gold Ring', 'gold', '22K',
 4.000, 0.200, 6200.00,
 'good', true, 'Customer\'s old engagement ring'),

('exitem_002', 'exch_002', 'Silver Bangles Set', 'silver', '925',
 200.000, 0.000, 78.00,
 'fair', false, 'Set of 6 old silver bangles');

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- ============================================================================
-- DATA VERIFICATION
-- ============================================================================

-- Verify data insertion
SELECT 'Data insertion completed successfully!' as message;

-- Show record counts
SELECT
  'users' as table_name, COUNT(*) as record_count FROM users
UNION ALL
SELECT 'customers', COUNT(*) FROM customers
UNION ALL
SELECT 'inventory', COUNT(*) FROM inventory
UNION ALL
SELECT 'sales', COUNT(*) FROM sales
UNION ALL
SELECT 'schemes', COUNT(*) FROM schemes
UNION ALL
SELECT 'repairs', COUNT(*) FROM repairs
UNION ALL
SELECT 'suppliers', COUNT(*) FROM suppliers
UNION ALL
SELECT 'purchases', COUNT(*) FROM purchases
UNION ALL
SELECT 'exchange_transactions', COUNT(*) FROM exchange_transactions;

-- Show sample data summary
SELECT
  'Sample data includes:' as summary,
  '5 users, 5 customers, 5 inventory items, 3 sales, 3 schemes, 3 repairs, 3 suppliers, 3 purchases, 2 exchanges' as details;
