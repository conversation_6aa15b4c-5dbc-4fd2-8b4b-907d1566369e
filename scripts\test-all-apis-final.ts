#!/usr/bin/env tsx

// Test all APIs after fixes
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

async function testAllAPIsFinal() {
  console.log('🧪 Final API Testing After All Fixes...\n')

  const apiEndpoints = [
    { name: 'Sales', url: 'http://localhost:3000/api/sales', key: 'sales' },
    { name: 'Customers', url: 'http://localhost:3000/api/customers', key: 'customers' },
    { name: 'Inventory', url: 'http://localhost:3000/api/inventory', key: 'inventory' },
    { name: 'Purchases', url: 'http://localhost:3000/api/purchases', key: 'purchases' },
    { name: 'Schemes', url: 'http://localhost:3000/api/schemes', key: 'schemes' },
    { name: 'Repairs', url: 'http://localhost:3000/api/repairs', key: 'repairs' },
    { name: 'Users', url: 'http://localhost:3000/api/users', key: 'users' },
    { name: 'Categories', url: 'http://localhost:3000/api/categories', key: 'categories' },
    { name: 'Settings', url: 'http://localhost:3000/api/settings', key: 'settings' }
  ]

  console.log('🌐 Testing all API endpoints...\n')

  const results: Record<string, any> = {}

  for (const endpoint of apiEndpoints) {
    try {
      console.log(`📡 Testing ${endpoint.name}...`)
      const response = await fetch(endpoint.url)
      
      if (response.ok) {
        const data = await response.json()
        let items = data[endpoint.key]
        
        // Handle settings special case
        if (endpoint.key === 'settings' && data.settings) {
          items = [data.settings]
        }
        
        const count = Array.isArray(items) ? items.length : (items ? 1 : 0)
        results[endpoint.name] = { success: true, count, status: response.status }
        
        console.log(`   ✅ ${endpoint.name}: ${response.status} - ${count} items`)
        
        // Show sample data for first few items
        if (Array.isArray(items) && items.length > 0) {
          const sample = items[0]
          console.log(`   📋 Sample: ${JSON.stringify(sample).substring(0, 100)}...`)
        } else if (items && !Array.isArray(items)) {
          console.log(`   📋 Data: ${JSON.stringify(items).substring(0, 100)}...`)
        }
        
      } else {
        const errorText = await response.text()
        results[endpoint.name] = { success: false, error: errorText, status: response.status }
        console.log(`   ❌ ${endpoint.name}: ${response.status} - ${errorText}`)
      }
    } catch (error) {
      results[endpoint.name] = { success: false, error: error.message, status: 'NETWORK_ERROR' }
      console.log(`   ❌ ${endpoint.name}: Network Error - ${error}`)
    }
    console.log('')
  }

  // Summary
  console.log('📊 FINAL API TEST SUMMARY:')
  console.log('=' .repeat(60))
  
  const working = Object.entries(results).filter(([_, result]) => result.success)
  const failing = Object.entries(results).filter(([_, result]) => !result.success)
  
  console.log(`✅ Working APIs: ${working.length}/${Object.keys(results).length}`)
  working.forEach(([name, result]) => {
    console.log(`   ✅ ${name}: ${result.count} items`)
  })
  
  if (failing.length > 0) {
    console.log(`\n❌ Failing APIs: ${failing.length}`)
    failing.forEach(([name, result]) => {
      console.log(`   ❌ ${name}: ${result.error}`)
    })
  }
  
  console.log('=' .repeat(60))
  
  // Critical systems check
  const criticalSystems = ['Sales', 'Customers', 'Inventory']
  const criticalWorking = criticalSystems.filter(system => results[system]?.success)
  
  console.log(`\n🎯 Critical Systems: ${criticalWorking.length}/${criticalSystems.length} working`)
  
  if (criticalWorking.length === criticalSystems.length) {
    console.log('🟢 ALL CRITICAL SYSTEMS OPERATIONAL')
    console.log('✅ Core business functionality is working')
  } else {
    console.log('🔴 SOME CRITICAL SYSTEMS HAVE ISSUES')
    console.log('⚠️  Core business functionality may be affected')
  }

  // Overall status
  const overallSuccess = working.length >= 7 // At least 7 out of 9 APIs working
  
  console.log(`\n🏆 OVERALL STATUS: ${overallSuccess ? '🟢 EXCELLENT' : '🟡 NEEDS ATTENTION'}`)
  
  if (overallSuccess) {
    console.log('🎉 System is ready for production use!')
    console.log('📊 All major functionality is operational')
    console.log('🚀 Users can access all core features')
  } else {
    console.log('🔧 Some APIs need additional attention')
    console.log('📋 Check the failing APIs above')
  }

  console.log('\n🎉 Final API Testing Completed!')
}

// Run the final API test
testAllAPIsFinal().catch(console.error)
