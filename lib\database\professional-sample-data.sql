-- PROFESSIONAL SAMPLE DATA FOR JEWELRY MANAGEMENT SYSTEM
-- Version: 5.0.0 - Professional ID Standards
-- Date: January 31, 2025
-- Description: Sample data with professional jewelry business ID formats

USE jewellers_db;

-- Disable foreign key checks temporarily for data insertion
SET FOREIGN_KEY_CHECKS = 0;

-- ============================================================================
-- 1. INITIALIZE ID SEQUENCES
-- ============================================================================

-- Initialize sequence table for current financial year
INSERT INTO id_sequences (id, entity_type, current_sequence, financial_year, created_at, updated_at) VALUES
('seq_001', 'customer', 5, '2024-25', NOW(), NOW()),
('seq_002', 'inventory', 5, '2024-25', NOW(), NOW()),
('seq_003', 'sale', 3, '2024-25', NOW(), NOW()),
('seq_004', 'invoice', 3, '2024-25', NOW(), NOW()),
('seq_005', 'repair', 3, '2024-25', NOW(), NOW()),
('seq_006', 'scheme', 3, '2024-25', NOW(), NOW()),
('seq_007', 'purchase', 3, '2024-25', NOW(), NOW()),
('seq_008', 'supplier', 3, '2024-25', NOW(), NOW()),
('seq_009', 'exchange', 2, '2024-25', NOW(), NOW()),
('seq_010', 'receipt', 10, '2024-25', NOW(), NOW()),
('seq_011', 'estimate', 5, '2024-25', NOW(), NOW());

-- ============================================================================
-- 2. BUSINESS SETTINGS (Updated)
-- ============================================================================

INSERT INTO business_settings (
  business_name, business_type, address, city, state, pincode, country,
  phone, email, website, gst_number, pan_number, license_number, established_year,
  business_hours, cgst_rate, sgst_rate, igst_rate, tcs_rate, tds_rate,
  hsn_codes, currency_symbol, currency_code, decimal_places,
  date_format, time_format, timezone, language, theme,
  auto_backup, backup_frequency, backup_retention,
  low_stock_alert, low_stock_threshold, scheme_reminders, repair_reminders,
  email_notifications, sms_notifications
) VALUES (
  'Shree Jewellers', 'jewelry_store', 
  '123 Main Street, Commercial Complex, Zaveri Bazaar', 'Mumbai', 'Maharashtra', '400002', 'India',
  '+91 98765 43210', '<EMAIL>', 'www.shreejewellers.com',
  '27**********1Z5', '**********', 'JL/2024/001', '2020',
  JSON_OBJECT(
    'monday', JSON_OBJECT('open', '09:00', 'close', '19:00', 'closed', false),
    'tuesday', JSON_OBJECT('open', '09:00', 'close', '19:00', 'closed', false),
    'wednesday', JSON_OBJECT('open', '09:00', 'close', '19:00', 'closed', false),
    'thursday', JSON_OBJECT('open', '09:00', 'close', '19:00', 'closed', false),
    'friday', JSON_OBJECT('open', '09:00', 'close', '19:00', 'closed', false),
    'saturday', JSON_OBJECT('open', '09:00', 'close', '19:00', 'closed', false),
    'sunday', JSON_OBJECT('open', '10:00', 'close', '18:00', 'closed', false)
  ),
  1.50, 1.50, 3.00, 1.00, 1.00,
  JSON_OBJECT(
    'gold', '71131900',
    'silver', '71131100', 
    'diamond', '71023100',
    'platinum', '71110000'
  ),
  '₹', 'INR', 2,
  'DD/MM/YYYY', '24', 'Asia/Kolkata', 'en', 'light',
  true, 'daily', 30,
  true, 5, true, true,
  true, false
);

-- ============================================================================
-- 3. USERS - Professional user codes
-- ============================================================================

INSERT INTO users (id, user_code, name, email, phone, password_hash, role, permissions, is_active) VALUES
('user_001', 'USER-2024-0001', 'Admin User', '<EMAIL>', '+91 98765 43210', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'super_admin', JSON_ARRAY('system_admin'), true),
('user_002', 'USER-2024-0002', 'Manager User', '<EMAIL>', '+91 98765 43211', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'manager', JSON_ARRAY('sales_view', 'sales_create', 'sales_edit', 'inventory_view', 'inventory_create', 'customers_view', 'customers_create', 'reports_view'), true),
('user_003', 'USER-2024-0003', 'Sales Staff', '<EMAIL>', '+91 98765 43212', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'sales_staff', JSON_ARRAY('sales_view', 'sales_create', 'customers_view', 'customers_create', 'inventory_view'), true),
('user_004', 'USER-2024-0004', 'Accountant', '<EMAIL>', '+91 98765 43213', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'accountant', JSON_ARRAY('sales_view', 'purchases_view', 'reports_view', 'reports_export'), true),
('user_005', 'USER-2024-0005', 'Technician', '<EMAIL>', '+91 98765 43214', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'technician', JSON_ARRAY('repairs_view', 'repairs_edit'), true);

-- ============================================================================
-- 4. METAL RATES
-- ============================================================================

INSERT INTO metal_rates (id, metal_type, purity, rate_per_gram, margin_percentage, effective_date, is_active) VALUES
('rate_001', 'gold', '24K', 7200.00, 5.00, CURDATE(), true),
('rate_002', 'gold', '22K', 6600.00, 5.00, CURDATE(), true),
('rate_003', 'gold', '18K', 5400.00, 5.00, CURDATE(), true),
('rate_004', 'gold', '14K', 4200.00, 5.00, CURDATE(), true),
('rate_005', 'silver', '999', 85.00, 8.00, CURDATE(), true),
('rate_006', 'silver', '925', 78.00, 8.00, CURDATE(), true),
('rate_007', 'platinum', '950', 3200.00, 3.00, CURDATE(), true);

-- ============================================================================
-- 5. INVENTORY CATEGORIES
-- ============================================================================

INSERT INTO inventory_categories (id, name, description, hsn_code, tax_rate, is_active, sort_order) VALUES
('cat_001', 'Rings', 'Gold, Silver, and Diamond Rings', '71131900', 3.00, true, 1),
('cat_002', 'Necklaces', 'Traditional and Modern Necklaces', '71131900', 3.00, true, 2),
('cat_003', 'Earrings', 'Studs, Hoops, and Danglers', '71131900', 3.00, true, 3),
('cat_004', 'Bracelets', 'Gold and Silver Bracelets', '71131900', 3.00, true, 4),
('cat_005', 'Pendants', 'Religious and Fashion Pendants', '71131900', 3.00, true, 5),
('cat_006', 'Chains', 'Gold and Silver Chains', '71131900', 3.00, true, 6),
('cat_007', 'Bangles', 'Traditional Indian Bangles', '71131900', 3.00, true, 7),
('cat_008', 'Sets', 'Complete Jewelry Sets', '71131900', 3.00, true, 8),
('cat_009', 'Diamonds', 'Loose Diamonds and Diamond Jewelry', '71023100', 0.25, true, 9),
('cat_010', 'Pearls', 'Natural and Cultured Pearls', '71161000', 3.00, true, 10);

-- ============================================================================
-- 6. CUSTOMERS - Professional customer codes
-- ============================================================================

INSERT INTO customers (
  id, customer_code, name, phone, alternate_phone, email, date_of_birth, anniversary_date, gender,
  address, city, state, pincode, country,
  aadhar_number, pan_number, kyc_status,
  occupation, annual_income, customer_type, source,
  preferred_metal, communication_preference, is_active, total_purchases, loyalty_points
) VALUES
('cust_001', 'CUST-2024-000001', 'Rajesh Kumar', '9876543210', '9876543211', '<EMAIL>', '1985-05-15', '2010-12-01', 'male',
 '123 Residential Complex, Andheri', 'Mumbai', 'Maharashtra', '400058', 'India',
 '123456789012', '**********', 'verified',
 'Business Owner', 1200000.00, 'premium', 'referral',
 'gold', 'phone', true, 250000.00, 2500),

('cust_002', 'CUST-2024-000002', 'Priya Sharma', '9876543220', NULL, '<EMAIL>', '1990-08-22', '2015-02-14', 'female',
 '456 Housing Society, Bandra', 'Mumbai', 'Maharashtra', '400050', 'India',
 '123456789013', '**********', 'verified',
 'Software Engineer', 800000.00, 'regular', 'walk-in',
 'gold', 'email', true, 150000.00, 1500),

('cust_003', 'CUST-2024-000003', 'Amit Patel', '9876543230', '9876543231', '<EMAIL>', '1982-03-10', '2008-11-25', 'male',
 '789 Commercial Street, Borivali', 'Mumbai', 'Maharashtra', '400092', 'India',
 '123456789014', '**********', 'verified',
 'Doctor', 1500000.00, 'vip', 'referral',
 'diamond', 'phone', true, 500000.00, 5000),

('cust_004', 'CUST-2024-000004', 'Sunita Joshi', '9876543240', NULL, '<EMAIL>', '1988-07-18', '2012-04-05', 'female',
 '321 Apartment Complex, Thane', 'Thane', 'Maharashtra', '400601', 'India',
 '123456789015', '**********', 'pending',
 'Teacher', 600000.00, 'regular', 'advertisement',
 'silver', 'email', true, 75000.00, 750),

('cust_005', 'CUST-2024-000005', 'Vikram Singh', '9876543250', '9876543251', '<EMAIL>', '1975-12-05', '2000-06-15', 'male',
 '654 Villa Complex, Juhu', 'Mumbai', 'Maharashtra', '400049', 'India',
 '123456789016', '**********', 'verified',
 'Businessman', 2000000.00, 'vip', 'referral',
 'platinum', 'phone', true, 800000.00, 8000);

-- ============================================================================
-- 7. SUPPLIERS - Professional supplier codes
-- ============================================================================

INSERT INTO suppliers (
  id, supplier_code, name, contact_person, phone, alternate_phone, email,
  address, city, state, pincode, country,
  gst_number, pan_number, payment_terms, credit_limit, credit_days,
  specializes_in, rating, quality_rating, delivery_rating, is_active, is_preferred
) VALUES
('supp_001', 'SUPP-2024-0001', 'Rajesh Gold Suppliers', 'Rajesh Kumar', '+91 98765 43210', '+91 98765 43211', '<EMAIL>',
 '123 Jewelry Market, Zaveri Bazaar', 'Mumbai', 'Maharashtra', '400002', 'India',
 '27**********1Z5', '**********', 'net_30', 500000.00, 30,
 JSON_ARRAY('gold', 'silver'), 4.5, 4.8, 4.2, true, true),

('supp_002', 'SUPP-2024-0002', 'Diamond House Pvt Ltd', 'Suresh Patel', '+91 98765 43220', NULL, '<EMAIL>',
 '456 Diamond District, Varachha', 'Surat', 'Gujarat', '395006', 'India',
 '24**********2L6', '**********', 'net_15', 1000000.00, 15,
 JSON_ARRAY('diamond', 'precious_stones'), 4.8, 4.9, 4.7, true, true),

('supp_003', 'SUPP-2024-0003', 'Silver Craft Industries', 'Amit Sharma', '+91 98765 43230', '+91 98765 43231', '<EMAIL>',
 '789 Silver Market, Johari Bazaar', 'Jaipur', 'Rajasthan', '302003', 'India',
 '08**********3S7', '**********', 'net_45', 300000.00, 45,
 JSON_ARRAY('silver', 'artificial_jewelry'), 4.2, 4.0, 4.5, true, false);

-- ============================================================================
-- 8. INVENTORY - Professional item codes and barcodes
-- ============================================================================

INSERT INTO inventory (
  id, barcode, name, description, category_id, metal_type, purity,
  gross_weight, stone_weight, stone_details, stone_amount,
  making_charges, other_charges, current_value, selling_price, mrp,
  stock, min_stock, max_stock, size, gender, occasion, design_number,
  location, status, hsn_code, images, tags
) VALUES
('RGGD22-2024-0001', 'SJ01224000001', 'Gold Ring with Diamond', 'Beautiful 22K gold ring with solitaire diamond', 'cat_001', 'gold', '22K',
 8.500, 0.250, '1 Solitaire Diamond, 0.25ct, VS1, F Color', 25000.00,
 8500.00, 500.00, 81200.00, 85000.00, 90000.00,
 1, 1, 5, '16', 'female', 'engagement', 'GR001',
 'Showcase A1', 'active', '71131900',
 JSON_ARRAY('/images/gold-ring-001.jpg'), JSON_ARRAY('gold', 'diamond', 'ring', 'engagement')),

('CHGD22-2024-0002', 'SJ06224000002', '22K Gold Chain', 'Traditional 22K gold chain for daily wear', 'cat_006', 'gold', '22K',
 25.000, 0.000, NULL, 0.00,
 12500.00, 1000.00, 178500.00, 185000.00, 195000.00,
 2, 1, 10, '20 inches', 'unisex', 'daily_wear', 'GC001',
 'Showcase B2', 'active', '71131900',
 JSON_ARRAY('/images/gold-chain-001.jpg'), JSON_ARRAY('gold', 'chain', 'traditional')),

('BGSL925-2024-0003', 'SJ07925000003', 'Silver Bangles Set', 'Set of 4 traditional silver bangles', 'cat_007', 'silver', '925',
 120.000, 0.000, NULL, 0.00,
 2400.00, 200.00, 11960.00, 12500.00, 13500.00,
 3, 2, 15, 'Medium', 'female', 'traditional', 'SB001',
 'Showcase C1', 'active', '71131100',
 JSON_ARRAY('/images/silver-bangles-001.jpg'), JSON_ARRAY('silver', 'bangles', 'traditional', 'set')),

('ERDM18-2024-0004', 'SJ03118000004', 'Diamond Earrings', 'Elegant diamond stud earrings in 18K gold', 'cat_003', 'gold', '18K',
 6.500, 1.200, '2 Diamonds, 0.6ct each, VVS1, D Color', 120000.00,
 15000.00, 2000.00, 172100.00, 180000.00, 195000.00,
 1, 1, 3, 'One Size', 'female', 'party', 'DE001',
 'Showcase A3', 'active', '71023100',
 JSON_ARRAY('/images/diamond-earrings-001.jpg'), JSON_ARRAY('diamond', 'earrings', 'gold', 'party')),

('STGD22-2024-0005', 'SJ08224000005', 'Gold Necklace Set', 'Complete necklace set with matching earrings', 'cat_008', 'gold', '22K',
 45.000, 2.500, 'Ruby and Emerald stones', 35000.00,
 22500.00, 3000.00, 358000.00, 375000.00, 395000.00,
 1, 1, 2, 'One Size', 'female', 'wedding', 'NS001',
 'Showcase A2', 'active', '71131900',
 JSON_ARRAY('/images/necklace-set-001.jpg'), JSON_ARRAY('gold', 'necklace', 'set', 'wedding', 'ruby', 'emerald'));

-- ============================================================================
-- 9. SALES - Professional sale and invoice numbers
-- ============================================================================

INSERT INTO sales (
  id, sale_number, invoice_number, customer_id, sale_date,
  subtotal, discount_amount, discount_percentage,
  cgst_amount, sgst_amount, igst_amount,
  total_amount, paid_amount, payment_method,
  exchange_amount, status, notes, created_by
) VALUES
('sale_001', 'SALE-2024-000001', 'INV-2024-25-000001', 'cust_001', '2024-01-15',
 82000.00, 2000.00, 2.50,
 1200.00, 1200.00, 0.00,
 82400.00, 82400.00, 'cash',
 0.00, 'completed', 'Gold ring purchase for engagement', 'user_002'),

('sale_002', 'SALE-2024-000002', 'INV-2024-25-000002', 'cust_002', '2024-01-20',
 180000.00, 5000.00, 2.78,
 2625.00, 2625.00, 0.00,
 180250.00, 180250.00, 'card',
 0.00, 'completed', 'Gold chain for festival', 'user_003'),

('sale_003', 'SALE-2024-000003', 'INV-2024-25-000003', 'cust_003', '2024-01-25',
 175000.00, 0.00, 0.00,
 2625.00, 2625.00, 0.00,
 180250.00, 150000.00, 'mixed',
 25000.00, 'completed', 'Diamond earrings with old gold exchange', 'user_002');

-- ============================================================================
-- 10. SALE ITEMS - Items in sales transactions
-- ============================================================================

INSERT INTO sale_items (
  id, sale_id, inventory_id, item_name, item_description,
  metal_type, purity, gross_weight, stone_weight, net_weight,
  rate_per_gram, making_charges, stone_amount, other_charges,
  quantity, unit_price, discount_amount, total_amount,
  hsn_code, tax_rate, cgst_amount, sgst_amount, igst_amount
) VALUES
('sitem_001', 'sale_001', 'RGGD22-2024-0001', 'Gold Ring with Diamond', 'Beautiful 22K gold ring with solitaire diamond',
 'gold', '22K', 8.500, 0.250, 8.250,
 6600.00, 8500.00, 25000.00, 500.00,
 1, 82000.00, 2000.00, 80000.00,
 '71131900', 3.00, 1200.00, 1200.00, 0.00),

('sitem_002', 'sale_002', 'CHGD22-2024-0002', '22K Gold Chain', 'Traditional 22K gold chain for daily wear',
 'gold', '22K', 25.000, 0.000, 25.000,
 6600.00, 12500.00, 0.00, 1000.00,
 1, 180000.00, 5000.00, 175000.00,
 '71131900', 3.00, 2625.00, 2625.00, 0.00),

('sitem_003', 'sale_003', 'ERDM18-2024-0004', 'Diamond Earrings', 'Elegant diamond stud earrings in 18K gold',
 'gold', '18K', 6.500, 1.200, 5.300,
 5400.00, 15000.00, 120000.00, 2000.00,
 1, 175000.00, 0.00, 175000.00,
 '71023100', 0.25, 43.75, 43.75, 0.00);

-- ============================================================================
-- 11. SCHEMES - Professional scheme numbers
-- ============================================================================

INSERT INTO schemes (
  id, scheme_number, customer_id, scheme_name, scheme_type,
  total_amount, monthly_amount, paid_amount, duration_months,
  start_date, maturity_date, gold_rate_locked, bonus_percentage,
  status, auto_debit, reminder_enabled, notes, created_by
) VALUES
('scheme_001', 'SCH-2024-25-000001', 'cust_001', 'Gold Savings Scheme', 'monthly',
 120000.00, 10000.00, 30000.00, 12,
 '2024-01-01', '2024-12-31', 6600.00, 5.00,
 'active', false, true, '12-month gold savings plan', 'user_002'),

('scheme_002', 'SCH-2024-25-000002', 'cust_002', 'Festival Special Scheme', 'advance',
 60000.00, 0.00, 60000.00, 6,
 '2024-02-01', '2024-07-31', 6600.00, 8.00,
 'active', false, true, 'Advance payment scheme for festival', 'user_003'),

('scheme_003', 'SCH-2024-25-000003', 'cust_004', 'Monthly Gold Plan', 'monthly',
 84000.00, 7000.00, 14000.00, 12,
 '2024-01-15', '2025-01-14', 6600.00, 3.00,
 'active', true, true, 'Regular monthly savings', 'user_002');

-- ============================================================================
-- 12. SCHEME PAYMENTS - Professional receipt numbers
-- ============================================================================

INSERT INTO scheme_payments (
  id, scheme_id, payment_date, amount, payment_method,
  status, receipt_number, gold_rate_on_date, gold_weight_equivalent,
  notes, created_by
) VALUES
('spay_001', 'scheme_001', '2024-01-01', 10000.00, 'cash',
 'completed', 'RCP-2024-25-000001', 6600.00, 1.515,
 'First payment', 'user_002'),

('spay_002', 'scheme_001', '2024-02-01', 10000.00, 'upi',
 'completed', 'RCP-2024-25-000002', 6650.00, 1.504,
 'Second payment', 'user_002'),

('spay_003', 'scheme_001', '2024-03-01', 10000.00, 'cash',
 'completed', 'RCP-2024-25-000003', 6700.00, 1.493,
 'Third payment', 'user_002'),

('spay_004', 'scheme_002', '2024-02-01', 60000.00, 'bank_transfer',
 'completed', 'RCP-2024-25-000004', 6650.00, 9.023,
 'Full advance payment', 'user_003'),

('spay_005', 'scheme_003', '2024-01-15', 7000.00, 'card',
 'completed', 'RCP-2024-25-000005', 6600.00, 1.061,
 'First payment', 'user_002'),

('spay_006', 'scheme_003', '2024-02-15', 7000.00, 'upi',
 'completed', 'RCP-2024-25-000006', 6650.00, 1.053,
 'Second payment', 'user_002');

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- ============================================================================
-- DATA VERIFICATION
-- ============================================================================

-- Verify professional ID formats
SELECT 'Professional ID Verification' as verification_type;

-- Check customer IDs
SELECT 'Customer IDs' as entity_type, customer_code, name
FROM customers
ORDER BY customer_code;

-- Check inventory item codes
SELECT 'Inventory Codes' as entity_type, id as item_code, barcode, name
FROM inventory
ORDER BY id;

-- Check sales and invoice numbers
SELECT 'Sales & Invoices' as entity_type, sale_number, invoice_number, total_amount
FROM sales
ORDER BY sale_number;

-- Check scheme numbers
SELECT 'Scheme Numbers' as entity_type, scheme_number, scheme_name, total_amount
FROM schemes
ORDER BY scheme_number;

-- Check supplier codes
SELECT 'Supplier Codes' as entity_type, supplier_code, name
FROM suppliers
ORDER BY supplier_code;

-- Show sequence status
SELECT 'Current Sequences' as info, entity_type, current_sequence, financial_year
FROM id_sequences
ORDER BY entity_type;

SELECT
  'Professional sample data loaded successfully!' as message,
  'All IDs follow jewelry business standards' as note,
  NOW() as loaded_at;
