-- COMPREHENSIVE JEWELRY MANAGEMENT SYSTEM DATABASE SCHEMA
-- Version: 4.0.0 - Complete Production-Ready Implementation
-- Date: January 31, 2025
-- Description: Updated schema with all enhanced features and modules

-- Create database with proper character set and collation
CREATE DATABASE IF NOT EXISTS jewellers_db 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE jewellers_db;

-- Enable foreign key checks and strict mode
SET FOREIGN_KEY_CHECKS = 1;
SET sql_mode = 'STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION';

-- ============================================================================
-- CORE SYSTEM TABLES
-- ============================================================================

-- 1. ID SEQUENCES - Professional ID sequence management
CREATE TABLE id_sequences (
  id VARCHAR(36) PRIMARY KEY,
  entity_type VARCHAR(50) NOT NULL,
  current_sequence INT NOT NULL DEFAULT 0,
  prefix VARCHAR(20),
  financial_year VARCHAR(10) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_entity_fy (entity_type, financial_year),
  INDEX idx_entity_type (entity_type),
  INDEX idx_financial_year (financial_year)
);

-- 2. USERS - Enhanced user management with roles and permissions
CREATE TABLE users (
  id VARCHAR(36) PRIMARY KEY,
  user_code VARCHAR(50) UNIQUE, -- Professional user code: USER-YYYY-NNNN
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  phone VARCHAR(20),
  password_hash VARCHAR(255) NOT NULL,
  role ENUM('super_admin', 'admin', 'manager', 'sales_staff', 'accountant', 'technician') DEFAULT 'sales_staff',
  permissions JSON, -- Flexible permission system
  is_active BOOLEAN DEFAULT TRUE,
  last_login TIMESTAMP NULL,
  password_changed_at TIMESTAMP NULL,
  failed_login_attempts INT DEFAULT 0,
  locked_until TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_email (email),
  INDEX idx_role (role),
  INDEX idx_active (is_active)
);

-- 2. BUSINESS SETTINGS - Complete business configuration
CREATE TABLE business_settings (
  id INT PRIMARY KEY AUTO_INCREMENT,
  business_name VARCHAR(255) NOT NULL,
  business_type ENUM('jewelry_store', 'jewelry_manufacturer', 'gold_dealer', 'diamond_dealer', 'wholesale', 'retail') DEFAULT 'jewelry_store',
  
  -- Address Information
  address TEXT,
  city VARCHAR(100),
  state VARCHAR(100),
  pincode VARCHAR(20),
  country VARCHAR(100) DEFAULT 'India',
  
  -- Contact Information
  phone VARCHAR(20),
  email VARCHAR(255),
  website VARCHAR(255),
  
  -- Legal Information
  gst_number VARCHAR(50),
  pan_number VARCHAR(20),
  license_number VARCHAR(100),
  established_year VARCHAR(4),
  
  -- Business Hours (JSON format)
  business_hours JSON,
  
  -- Tax Configuration
  cgst_rate DECIMAL(5,2) DEFAULT 1.50,
  sgst_rate DECIMAL(5,2) DEFAULT 1.50,
  igst_rate DECIMAL(5,2) DEFAULT 3.00,
  tcs_rate DECIMAL(5,2) DEFAULT 1.00,
  tds_rate DECIMAL(5,2) DEFAULT 1.00,
  
  -- HSN Codes (JSON format)
  hsn_codes JSON,
  
  -- Currency Settings
  currency_symbol VARCHAR(10) DEFAULT '₹',
  currency_code VARCHAR(10) DEFAULT 'INR',
  decimal_places INT DEFAULT 2,
  
  -- System Settings
  date_format VARCHAR(20) DEFAULT 'DD/MM/YYYY',
  time_format VARCHAR(10) DEFAULT '24',
  timezone VARCHAR(50) DEFAULT 'Asia/Kolkata',
  language VARCHAR(10) DEFAULT 'en',
  theme VARCHAR(20) DEFAULT 'light',
  
  -- Backup Settings
  auto_backup BOOLEAN DEFAULT TRUE,
  backup_frequency ENUM('hourly', 'daily', 'weekly', 'monthly') DEFAULT 'daily',
  backup_retention INT DEFAULT 30,
  
  -- Notification Settings
  low_stock_alert BOOLEAN DEFAULT TRUE,
  low_stock_threshold INT DEFAULT 5,
  scheme_reminders BOOLEAN DEFAULT TRUE,
  repair_reminders BOOLEAN DEFAULT TRUE,
  email_notifications BOOLEAN DEFAULT TRUE,
  sms_notifications BOOLEAN DEFAULT FALSE,
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 3. METAL RATES - Enhanced metal rate management
CREATE TABLE metal_rates (
  id VARCHAR(36) PRIMARY KEY,
  metal_type ENUM('gold', 'silver', 'platinum') NOT NULL,
  purity VARCHAR(20) NOT NULL, -- 24K, 22K, 18K, 14K, 999, 925, 950
  rate_per_gram DECIMAL(12,2) NOT NULL,
  margin_percentage DECIMAL(5,2) DEFAULT 5.00,
  effective_date DATE NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  auto_update BOOLEAN DEFAULT FALSE,
  source VARCHAR(100), -- manual, api, market
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_metal_purity (metal_type, purity),
  INDEX idx_active_rates (is_active, effective_date),
  UNIQUE KEY unique_active_rate (metal_type, purity, is_active, effective_date)
);

-- ============================================================================
-- CUSTOMER MANAGEMENT
-- ============================================================================

-- 4. CUSTOMERS - Enhanced customer management with KYC
CREATE TABLE customers (
  id VARCHAR(36) PRIMARY KEY,
  customer_code VARCHAR(50) UNIQUE NOT NULL, -- Professional format: CUST-YYYY-NNNNNN
  name VARCHAR(255) NOT NULL,
  phone VARCHAR(20),
  alternate_phone VARCHAR(20),
  email VARCHAR(255),
  date_of_birth DATE,
  anniversary_date DATE,
  gender ENUM('male', 'female', 'other'),
  
  -- Address Information
  address TEXT,
  city VARCHAR(100),
  state VARCHAR(100),
  pincode VARCHAR(20),
  country VARCHAR(100) DEFAULT 'India',
  
  -- KYC Information
  aadhar_number VARCHAR(20),
  pan_number VARCHAR(20),
  passport_number VARCHAR(50),
  driving_license VARCHAR(50),
  kyc_status ENUM('pending', 'verified', 'rejected') DEFAULT 'pending',
  kyc_documents JSON, -- Store document references
  
  -- Business Information
  occupation VARCHAR(100),
  annual_income DECIMAL(15,2),
  company_name VARCHAR(255),
  
  -- Financial Tracking
  credit_limit DECIMAL(15,2) DEFAULT 0.00,
  total_purchases DECIMAL(15,2) DEFAULT 0.00,
  total_outstanding DECIMAL(15,2) DEFAULT 0.00,
  loyalty_points INT DEFAULT 0,
  
  -- Customer Segmentation
  customer_type ENUM('regular', 'premium', 'vip', 'wholesale') DEFAULT 'regular',
  source VARCHAR(100), -- walk-in, referral, online, advertisement
  
  -- Preferences
  preferred_metal VARCHAR(50),
  preferred_categories JSON,
  communication_preference ENUM('phone', 'email', 'sms', 'whatsapp') DEFAULT 'phone',
  
  -- Status
  is_active BOOLEAN DEFAULT TRUE,
  last_visit TIMESTAMP,
  notes TEXT,
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_name (name),
  INDEX idx_phone (phone),
  INDEX idx_email (email),
  INDEX idx_customer_code (customer_code),
  INDEX idx_customer_type (customer_type),
  INDEX idx_kyc_status (kyc_status)
);

-- ============================================================================
-- INVENTORY MANAGEMENT
-- ============================================================================

-- 5. INVENTORY CATEGORIES
CREATE TABLE inventory_categories (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  parent_id VARCHAR(36),
  hsn_code VARCHAR(20),
  tax_rate DECIMAL(5,2) DEFAULT 3.00,
  is_active BOOLEAN DEFAULT TRUE,
  sort_order INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (parent_id) REFERENCES inventory_categories(id) ON DELETE SET NULL,
  INDEX idx_parent (parent_id),
  INDEX idx_active (is_active)
);

-- 6. INVENTORY - Enhanced inventory management
CREATE TABLE inventory (
  id VARCHAR(50) PRIMARY KEY, -- Professional format: RGGD22-YYYY-NNNN
  barcode VARCHAR(100) UNIQUE, -- Professional barcode: SJ01224000001
  name VARCHAR(255) NOT NULL,
  description TEXT,
  category_id VARCHAR(36),
  
  -- Metal Information
  metal_type ENUM('gold', 'silver', 'platinum', 'diamond', 'other') NOT NULL,
  purity VARCHAR(20),
  
  -- Weight Information
  gross_weight DECIMAL(10,3) DEFAULT 0.000,
  stone_weight DECIMAL(10,3) DEFAULT 0.000,
  net_weight DECIMAL(10,3) GENERATED ALWAYS AS (gross_weight - stone_weight) STORED,
  
  -- Stone Information
  stone_details TEXT,
  stone_amount DECIMAL(15,2) DEFAULT 0.00,
  diamond_weight DECIMAL(8,3) DEFAULT 0.000,
  diamond_pieces INT DEFAULT 0,
  
  -- Pricing Information
  making_charges DECIMAL(15,2) DEFAULT 0.00,
  other_charges DECIMAL(15,2) DEFAULT 0.00,
  current_value DECIMAL(15,2) DEFAULT 0.00,
  selling_price DECIMAL(15,2) DEFAULT 0.00,
  mrp DECIMAL(15,2) DEFAULT 0.00,
  
  -- Stock Information
  stock INT DEFAULT 1,
  min_stock INT DEFAULT 1,
  max_stock INT DEFAULT 100,
  
  -- Additional Information
  size VARCHAR(50),
  gender ENUM('male', 'female', 'unisex', 'kids') DEFAULT 'unisex',
  occasion VARCHAR(100),
  design_number VARCHAR(100),
  location VARCHAR(100),
  
  -- Status and Tracking
  status ENUM('active', 'sold', 'reserved', 'damaged', 'repair', 'inactive') DEFAULT 'active',
  hsn_code VARCHAR(20),
  images JSON, -- Store image URLs
  tags JSON, -- Store tags for search
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (category_id) REFERENCES inventory_categories(id) ON DELETE SET NULL,
  INDEX idx_category (category_id),
  INDEX idx_metal_type (metal_type),
  INDEX idx_stock (stock),
  INDEX idx_status (status),
  INDEX idx_hsn_code (hsn_code),
  INDEX idx_barcode (barcode)
);

-- ============================================================================
-- SALES MANAGEMENT
-- ============================================================================

-- 7. SALES - Enhanced sales management
CREATE TABLE sales (
  id VARCHAR(36) PRIMARY KEY,
  sale_number VARCHAR(50) UNIQUE NOT NULL, -- Professional format: SALE-YYYY-NNNNNN
  invoice_number VARCHAR(100) UNIQUE NOT NULL, -- Professional format: INV-FY-NNNNNN
  customer_id VARCHAR(36),
  sale_date DATE NOT NULL,
  
  -- Amounts
  subtotal DECIMAL(15,2) DEFAULT 0.00,
  discount_amount DECIMAL(15,2) DEFAULT 0.00,
  discount_percentage DECIMAL(5,2) DEFAULT 0.00,
  
  -- Tax Calculations
  cgst_amount DECIMAL(15,2) DEFAULT 0.00,
  sgst_amount DECIMAL(15,2) DEFAULT 0.00,
  igst_amount DECIMAL(15,2) DEFAULT 0.00,
  
  -- Final Amounts
  total_amount DECIMAL(15,2) NOT NULL,
  paid_amount DECIMAL(15,2) DEFAULT 0.00,
  balance_amount DECIMAL(15,2) GENERATED ALWAYS AS (total_amount - paid_amount) STORED,
  
  -- Payment Information
  payment_method ENUM('cash', 'card', 'upi', 'bank_transfer', 'cheque', 'mixed') DEFAULT 'cash',
  payment_details JSON, -- Store payment breakdown for mixed payments
  
  -- Exchange Information
  exchange_amount DECIMAL(15,2) DEFAULT 0.00,
  exchange_details JSON, -- Store exchange item details
  
  -- Status and Notes
  status ENUM('draft', 'completed', 'cancelled', 'returned') DEFAULT 'completed',
  notes TEXT,
  
  -- Tracking
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_customer (customer_id),
  INDEX idx_date (sale_date),
  INDEX idx_status (status),
  INDEX idx_invoice (invoice_number)
);

-- 8. SALE ITEMS - Individual items in sales
CREATE TABLE sale_items (
  id VARCHAR(36) PRIMARY KEY,
  sale_id VARCHAR(36) NOT NULL,
  inventory_id VARCHAR(50),
  
  -- Item Details (for sold items that may be removed from inventory)
  item_name VARCHAR(255) NOT NULL,
  item_description TEXT,
  metal_type VARCHAR(50),
  purity VARCHAR(20),
  gross_weight DECIMAL(10,3),
  stone_weight DECIMAL(10,3),
  net_weight DECIMAL(10,3),
  
  -- Pricing
  rate_per_gram DECIMAL(12,2),
  making_charges DECIMAL(15,2),
  stone_amount DECIMAL(15,2),
  other_charges DECIMAL(15,2),
  
  quantity INT DEFAULT 1,
  unit_price DECIMAL(15,2) NOT NULL,
  discount_amount DECIMAL(15,2) DEFAULT 0.00,
  total_amount DECIMAL(15,2) NOT NULL,
  
  -- Tax Information
  hsn_code VARCHAR(20),
  tax_rate DECIMAL(5,2) DEFAULT 3.00,
  cgst_amount DECIMAL(15,2) DEFAULT 0.00,
  sgst_amount DECIMAL(15,2) DEFAULT 0.00,
  igst_amount DECIMAL(15,2) DEFAULT 0.00,
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (sale_id) REFERENCES sales(id) ON DELETE CASCADE,
  FOREIGN KEY (inventory_id) REFERENCES inventory(id) ON DELETE SET NULL,
  INDEX idx_sale (sale_id),
  INDEX idx_inventory (inventory_id)
);

-- ============================================================================
-- REPAIR MANAGEMENT
-- ============================================================================

-- 9. REPAIRS - Enhanced repair management
CREATE TABLE repairs (
  id VARCHAR(36) PRIMARY KEY,
  repair_number VARCHAR(100) UNIQUE NOT NULL, -- Professional format: REP-FY-NNNNNN
  customer_id VARCHAR(36),

  -- Item Information
  item_name VARCHAR(255) NOT NULL,
  item_description TEXT,
  item_photos JSON, -- Store photo URLs

  -- Repair Details
  repair_type ENUM('repair', 'resize', 'polish', 'stone_setting', 'chain_repair', 'custom_making', 'cleaning') DEFAULT 'repair',
  problem_description TEXT,
  repair_instructions TEXT,

  -- Dates
  received_date DATE NOT NULL,
  promised_date DATE,
  started_date DATE,
  completed_date DATE,
  delivered_date DATE,

  -- Pricing
  estimated_cost DECIMAL(15,2) DEFAULT 0.00,
  actual_cost DECIMAL(15,2) DEFAULT 0.00,
  advance_paid DECIMAL(15,2) DEFAULT 0.00,
  balance_amount DECIMAL(15,2) GENERATED ALWAYS AS (actual_cost - advance_paid) STORED,

  -- Status and Tracking
  status ENUM('received', 'in_progress', 'completed', 'ready_for_delivery', 'delivered', 'cancelled') DEFAULT 'received',
  priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',

  -- Assignment
  assigned_to VARCHAR(36), -- technician user id
  quality_check_by VARCHAR(36), -- quality checker user id
  quality_notes TEXT,

  -- Customer Communication
  customer_notified BOOLEAN DEFAULT FALSE,
  notification_sent_at TIMESTAMP NULL,

  -- Additional Information
  warranty_period INT DEFAULT 0, -- in days
  special_instructions TEXT,
  internal_notes TEXT,

  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
  FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (quality_check_by) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_customer (customer_id),
  INDEX idx_status (status),
  INDEX idx_promised_date (promised_date),
  INDEX idx_assigned_to (assigned_to),
  INDEX idx_repair_number (repair_number)
);

-- ============================================================================
-- GOLD SCHEME MANAGEMENT
-- ============================================================================

-- 10. SCHEMES - Enhanced scheme management
CREATE TABLE schemes (
  id VARCHAR(36) PRIMARY KEY,
  scheme_number VARCHAR(100) UNIQUE NOT NULL, -- Professional format: SCH-FY-NNNNNN
  customer_id VARCHAR(36) NOT NULL,

  -- Scheme Details
  scheme_name VARCHAR(255) NOT NULL,
  scheme_type ENUM('monthly', 'advance', 'flexible', 'festival') DEFAULT 'monthly',

  -- Financial Information
  total_amount DECIMAL(15,2) NOT NULL,
  monthly_amount DECIMAL(15,2) NOT NULL,
  paid_amount DECIMAL(15,2) DEFAULT 0.00,
  balance_amount DECIMAL(15,2) GENERATED ALWAYS AS (total_amount - paid_amount) STORED,

  -- Duration and Dates
  duration_months INT NOT NULL,
  start_date DATE NOT NULL,
  maturity_date DATE NOT NULL,

  -- Gold Rate Information
  gold_rate_locked DECIMAL(12,2),
  gold_rate_at_maturity DECIMAL(12,2),
  rate_protection BOOLEAN DEFAULT FALSE,

  -- Bonus and Benefits
  bonus_percentage DECIMAL(5,2) DEFAULT 0.00,
  bonus_amount DECIMAL(15,2) DEFAULT 0.00,
  making_charges_waiver BOOLEAN DEFAULT FALSE,

  -- Status and Tracking
  status ENUM('active', 'completed', 'cancelled', 'matured', 'defaulted') DEFAULT 'active',
  auto_debit BOOLEAN DEFAULT FALSE,

  -- Maturity Information
  maturity_value DECIMAL(15,2) DEFAULT 0.00,
  maturity_gold_weight DECIMAL(10,3) DEFAULT 0.000,
  redemption_type ENUM('gold', 'cash', 'jewelry') DEFAULT 'jewelry',

  -- Notifications
  reminder_enabled BOOLEAN DEFAULT TRUE,
  last_reminder_sent TIMESTAMP NULL,

  notes TEXT,
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_customer (customer_id),
  INDEX idx_status (status),
  INDEX idx_maturity_date (maturity_date),
  INDEX idx_scheme_number (scheme_number)
);

-- 11. SCHEME PAYMENTS - Track individual scheme payments
CREATE TABLE scheme_payments (
  id VARCHAR(36) PRIMARY KEY,
  scheme_id VARCHAR(36) NOT NULL,

  -- Payment Details
  payment_date DATE NOT NULL,
  amount DECIMAL(15,2) NOT NULL,
  payment_method ENUM('cash', 'card', 'upi', 'bank_transfer', 'cheque') DEFAULT 'cash',

  -- Payment Status
  status ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'completed',

  -- Reference Information
  transaction_reference VARCHAR(100),
  receipt_number VARCHAR(100),

  -- Gold Rate at Payment
  gold_rate_on_date DECIMAL(12,2),
  gold_weight_equivalent DECIMAL(10,3),

  notes TEXT,
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  FOREIGN KEY (scheme_id) REFERENCES schemes(id) ON DELETE CASCADE,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_scheme (scheme_id),
  INDEX idx_payment_date (payment_date),
  INDEX idx_status (status)
);

-- ============================================================================
-- PURCHASE MANAGEMENT
-- ============================================================================

-- 12. SUPPLIERS - Enhanced supplier management
CREATE TABLE suppliers (
  id VARCHAR(36) PRIMARY KEY,
  supplier_code VARCHAR(50) UNIQUE NOT NULL, -- Professional format: SUPP-YYYY-NNNN
  name VARCHAR(255) NOT NULL,
  contact_person VARCHAR(255),

  -- Contact Information
  phone VARCHAR(20),
  alternate_phone VARCHAR(20),
  email VARCHAR(255),
  website VARCHAR(255),

  -- Address Information
  address TEXT,
  city VARCHAR(100),
  state VARCHAR(100),
  pincode VARCHAR(20),
  country VARCHAR(100) DEFAULT 'India',

  -- Legal Information
  gst_number VARCHAR(50),
  pan_number VARCHAR(20),
  license_number VARCHAR(100),

  -- Business Terms
  payment_terms ENUM('cash', 'net_15', 'net_30', 'net_45', 'net_60') DEFAULT 'net_30',
  credit_limit DECIMAL(15,2) DEFAULT 0.00,
  credit_days INT DEFAULT 30,
  discount_offered DECIMAL(5,2) DEFAULT 0.00,

  -- Financial Tracking
  total_purchases DECIMAL(15,2) DEFAULT 0.00,
  total_outstanding DECIMAL(15,2) DEFAULT 0.00,
  last_purchase_date DATE,
  last_payment_date DATE,

  -- Performance Metrics
  rating DECIMAL(3,2) DEFAULT 0.00,
  quality_rating DECIMAL(3,2) DEFAULT 0.00,
  delivery_rating DECIMAL(3,2) DEFAULT 0.00,

  -- Specialization
  specializes_in JSON, -- metals, categories, services
  certifications JSON,

  -- Status
  is_active BOOLEAN DEFAULT TRUE,
  is_preferred BOOLEAN DEFAULT FALSE,

  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_name (name),
  INDEX idx_supplier_code (supplier_code),
  INDEX idx_active (is_active),
  INDEX idx_rating (rating)
);

-- 13. PURCHASES - Enhanced purchase management
CREATE TABLE purchases (
  id VARCHAR(36) PRIMARY KEY,
  purchase_number VARCHAR(100) UNIQUE NOT NULL, -- Professional format: PO-FY-NNNNNN
  supplier_id VARCHAR(36) NOT NULL,

  -- Purchase Details
  purchase_date DATE NOT NULL,
  expected_delivery_date DATE,
  actual_delivery_date DATE,

  -- Amounts
  subtotal DECIMAL(15,2) DEFAULT 0.00,
  discount_amount DECIMAL(15,2) DEFAULT 0.00,

  -- Tax Calculations
  cgst_amount DECIMAL(15,2) DEFAULT 0.00,
  sgst_amount DECIMAL(15,2) DEFAULT 0.00,
  igst_amount DECIMAL(15,2) DEFAULT 0.00,

  -- Final Amounts
  total_amount DECIMAL(15,2) NOT NULL,
  paid_amount DECIMAL(15,2) DEFAULT 0.00,
  balance_amount DECIMAL(15,2) GENERATED ALWAYS AS (total_amount - paid_amount) STORED,

  -- Status and Tracking
  status ENUM('draft', 'ordered', 'received', 'completed', 'cancelled') DEFAULT 'draft',
  payment_status ENUM('pending', 'partial', 'completed') DEFAULT 'pending',

  -- Reference Information
  supplier_invoice_number VARCHAR(100),
  supplier_invoice_date DATE,

  notes TEXT,
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE RESTRICT,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_supplier (supplier_id),
  INDEX idx_purchase_date (purchase_date),
  INDEX idx_status (status),
  INDEX idx_purchase_number (purchase_number)
);

-- 14. PURCHASE ITEMS - Individual items in purchases
CREATE TABLE purchase_items (
  id VARCHAR(36) PRIMARY KEY,
  purchase_id VARCHAR(36) NOT NULL,

  -- Item Details
  item_name VARCHAR(255) NOT NULL,
  item_description TEXT,
  category VARCHAR(100),
  metal_type VARCHAR(50),
  purity VARCHAR(20),

  -- Weight Information
  gross_weight DECIMAL(10,3),
  stone_weight DECIMAL(10,3),
  net_weight DECIMAL(10,3),

  -- Pricing
  quantity INT DEFAULT 1,
  rate_per_gram DECIMAL(12,2),
  making_charges DECIMAL(15,2),
  unit_price DECIMAL(15,2) NOT NULL,
  total_amount DECIMAL(15,2) NOT NULL,

  -- Tax Information
  hsn_code VARCHAR(20),
  tax_rate DECIMAL(5,2) DEFAULT 3.00,
  cgst_amount DECIMAL(15,2) DEFAULT 0.00,
  sgst_amount DECIMAL(15,2) DEFAULT 0.00,
  igst_amount DECIMAL(15,2) DEFAULT 0.00,

  -- Status
  received_quantity INT DEFAULT 0,
  status ENUM('ordered', 'received', 'cancelled') DEFAULT 'ordered',

  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  FOREIGN KEY (purchase_id) REFERENCES purchases(id) ON DELETE CASCADE,
  INDEX idx_purchase (purchase_id),
  INDEX idx_status (status)
);

-- ============================================================================
-- EXCHANGE MANAGEMENT
-- ============================================================================

-- 15. EXCHANGE RATES - Current exchange rates for old gold/silver
CREATE TABLE exchange_rates (
  id VARCHAR(36) PRIMARY KEY,
  metal_type ENUM('gold', 'silver', 'platinum') NOT NULL,
  purity VARCHAR(20) NOT NULL, -- 24K, 22K, 18K, 14K, 999, 925, 950
  rate_per_gram DECIMAL(12,2) NOT NULL,
  margin_percentage DECIMAL(5,2) DEFAULT 5.00,
  effective_date DATE NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_metal_purity (metal_type, purity),
  INDEX idx_active_rates (is_active, effective_date),
  UNIQUE KEY unique_active_rate (metal_type, purity, is_active, effective_date)
);

-- 16. EXCHANGE TRANSACTIONS - Old gold/silver exchange transactions
CREATE TABLE exchange_transactions (
  id VARCHAR(36) PRIMARY KEY,
  transaction_number VARCHAR(100) UNIQUE NOT NULL, -- Professional format: EXG-FY-NNNNNN
  customer_id VARCHAR(36),

  -- Transaction Details
  transaction_date DATE NOT NULL,
  total_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00,

  -- Payment Information
  payment_method ENUM('cash', 'bank_transfer', 'adjustment') DEFAULT 'cash',
  payment_reference VARCHAR(100),

  -- Status and Tracking
  status ENUM('pending', 'completed', 'cancelled') DEFAULT 'pending',

  -- Purchase Bill Integration
  purchase_bill_generated BOOLEAN DEFAULT FALSE,
  purchase_bill_id VARCHAR(36),
  purchase_bill_number VARCHAR(100),
  purchase_bill_date DATE,

  notes TEXT,
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_customer (customer_id),
  INDEX idx_transaction_date (transaction_date),
  INDEX idx_status (status),
  INDEX idx_transaction_number (transaction_number)
);

-- 17. EXCHANGE ITEMS - Individual items in exchange transactions
CREATE TABLE exchange_items (
  id VARCHAR(36) PRIMARY KEY,
  transaction_id VARCHAR(36) NOT NULL,

  -- Item Details
  item_description VARCHAR(255) NOT NULL,
  metal_type ENUM('gold', 'silver', 'platinum') NOT NULL,
  purity VARCHAR(20) NOT NULL,

  -- Weight Information
  gross_weight DECIMAL(10,3) NOT NULL,
  stone_weight DECIMAL(10,3) DEFAULT 0.000,
  net_weight DECIMAL(10,3) GENERATED ALWAYS AS (gross_weight - stone_weight) STORED,

  -- Pricing
  rate_per_gram DECIMAL(12,2) NOT NULL,
  amount DECIMAL(15,2) GENERATED ALWAYS AS (net_weight * rate_per_gram) STORED,

  -- Condition and Quality
  item_condition ENUM('excellent', 'good', 'fair', 'poor') DEFAULT 'good',
  hallmark_available BOOLEAN DEFAULT FALSE,
  certificate_number VARCHAR(100),

  -- Additional Information
  photos JSON, -- Store photo URLs
  notes TEXT,

  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  FOREIGN KEY (transaction_id) REFERENCES exchange_transactions(id) ON DELETE CASCADE,
  INDEX idx_transaction (transaction_id),
  INDEX idx_metal_type (metal_type),
  INDEX idx_purity (purity)
);

-- ============================================================================
-- AUDIT AND LOGGING
-- ============================================================================

-- 18. AUDIT_LOG - System audit trail
CREATE TABLE audit_log (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36),
  action VARCHAR(100) NOT NULL,
  table_name VARCHAR(100),
  record_id VARCHAR(36),
  old_values JSON,
  new_values JSON,
  ip_address VARCHAR(45),
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_user (user_id),
  INDEX idx_action (action),
  INDEX idx_table (table_name),
  INDEX idx_created_at (created_at)
);

-- 19. SYSTEM_LOGS - Application logs
CREATE TABLE system_logs (
  id VARCHAR(36) PRIMARY KEY,
  level ENUM('debug', 'info', 'warning', 'error', 'critical') NOT NULL,
  message TEXT NOT NULL,
  context JSON,
  user_id VARCHAR(36),
  ip_address VARCHAR(45),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_level (level),
  INDEX idx_user (user_id),
  INDEX idx_created_at (created_at)
);

-- ============================================================================
-- NOTIFICATIONS AND REMINDERS
-- ============================================================================

-- 20. NOTIFICATIONS - System notifications
CREATE TABLE notifications (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36),
  type ENUM('info', 'warning', 'error', 'success') DEFAULT 'info',
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  action_url VARCHAR(500),
  is_read BOOLEAN DEFAULT FALSE,
  is_system BOOLEAN DEFAULT FALSE,
  expires_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  read_at TIMESTAMP NULL,

  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_user (user_id),
  INDEX idx_read (is_read),
  INDEX idx_created_at (created_at)
);

-- 21. REMINDERS - Automated reminders
CREATE TABLE reminders (
  id VARCHAR(36) PRIMARY KEY,
  type ENUM('scheme_payment', 'repair_delivery', 'customer_birthday', 'customer_anniversary', 'low_stock', 'follow_up') NOT NULL,
  reference_id VARCHAR(36), -- ID of related record
  reference_table VARCHAR(100), -- Table name of related record

  -- Reminder Details
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  reminder_date DATE NOT NULL,
  reminder_time TIME DEFAULT '10:00:00',

  -- Status
  status ENUM('pending', 'sent', 'cancelled') DEFAULT 'pending',
  sent_at TIMESTAMP NULL,

  -- Recipient Information
  recipient_type ENUM('customer', 'user', 'system') DEFAULT 'customer',
  recipient_id VARCHAR(36),

  -- Delivery Method
  delivery_method ENUM('email', 'sms', 'whatsapp', 'notification') DEFAULT 'notification',

  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_reminder_date (reminder_date),
  INDEX idx_status (status),
  INDEX idx_type (type),
  INDEX idx_recipient (recipient_type, recipient_id)
);

-- ============================================================================
-- REPORTS AND ANALYTICS
-- ============================================================================

-- 22. REPORT_TEMPLATES - Saved report templates
CREATE TABLE report_templates (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  report_type ENUM('sales', 'inventory', 'customer', 'financial', 'scheme', 'repair', 'exchange') NOT NULL,

  -- Template Configuration
  filters JSON, -- Store filter criteria
  columns JSON, -- Store column configuration
  grouping JSON, -- Store grouping options
  sorting JSON, -- Store sorting options

  -- Access Control
  is_public BOOLEAN DEFAULT FALSE,
  created_by VARCHAR(36),

  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_type (report_type),
  INDEX idx_created_by (created_by)
);

-- ============================================================================
-- INDEXES AND CONSTRAINTS
-- ============================================================================

-- Additional indexes for performance optimization
CREATE INDEX idx_sales_date_customer ON sales(sale_date, customer_id);
CREATE INDEX idx_inventory_metal_category ON inventory(metal_type, category_id);
CREATE INDEX idx_customers_type_active ON customers(customer_type, is_active);
CREATE INDEX idx_schemes_status_maturity ON schemes(status, maturity_date);
CREATE INDEX idx_repairs_status_promised ON repairs(status, promised_date);

-- ============================================================================
-- TRIGGERS
-- ============================================================================

DELIMITER //

-- Trigger to update customer total purchases
CREATE TRIGGER tr_sales_update_customer_total
AFTER INSERT ON sales
FOR EACH ROW
BEGIN
  UPDATE customers
  SET total_purchases = total_purchases + NEW.total_amount,
      last_visit = NEW.sale_date
  WHERE id = NEW.customer_id;
END //

-- Trigger to update inventory stock on sale
CREATE TRIGGER tr_sale_items_update_inventory
AFTER INSERT ON sale_items
FOR EACH ROW
BEGIN
  UPDATE inventory
  SET stock = stock - NEW.quantity,
      updated_at = CURRENT_TIMESTAMP
  WHERE id = NEW.inventory_id AND stock >= NEW.quantity;
END //

-- Trigger to update scheme paid amount
CREATE TRIGGER tr_scheme_payments_update_scheme
AFTER INSERT ON scheme_payments
FOR EACH ROW
BEGIN
  UPDATE schemes
  SET paid_amount = paid_amount + NEW.amount,
      updated_at = CURRENT_TIMESTAMP
  WHERE id = NEW.scheme_id;
END //

DELIMITER ;
