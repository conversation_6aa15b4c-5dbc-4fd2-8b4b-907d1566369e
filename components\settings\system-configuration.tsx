"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { toast } from "sonner"
import { 
  Database, Shield, Bell, Globe, Download, Upload,
  Server, Lock, Key, Wifi, Cloud, HardDrive,
  AlertTriangle, CheckCircle, Settings, Smartphone
} from "lucide-react"

export function SystemConfiguration() {
  const [activeTab, setActiveTab] = useState("security")
  const [isLoading, setIsLoading] = useState(false)

  // Security Settings
  const [securitySettings, setSecuritySettings] = useState({
    twoFactorAuth: false,
    sessionTimeout: "60",
    maxLoginAttempts: "5",
    passwordExpiry: "90",
    ipWhitelist: "",
    sslEnabled: true,
    encryptionLevel: "AES256",
    auditLogging: true,
    loginNotifications: true,
    suspiciousActivityAlert: true
  })

  // Backup Settings
  const [backupSettings, setBackupSettings] = useState({
    autoBackup: true,
    backupFrequency: "daily",
    backupTime: "02:00",
    backupRetention: "30",
    cloudBackup: false,
    cloudProvider: "aws",
    localBackupPath: "/backups",
    encryptBackups: true,
    lastBackup: "2024-01-15T02:00:00Z",
    backupSize: "2.5 GB"
  })

  // Integration Settings
  const [integrationSettings, setIntegrationSettings] = useState({
    smsProvider: "twilio",
    smsApiKey: "",
    emailProvider: "smtp",
    emailHost: "",
    emailPort: "587",
    emailUsername: "",
    emailPassword: "",
    whatsappEnabled: false,
    whatsappApiKey: "",
    paymentGateway: "razorpay",
    paymentApiKey: "",
    gstApiEnabled: false,
    gstApiKey: ""
  })

  // System Performance
  const [systemStats, setSystemStats] = useState({
    cpuUsage: 45,
    memoryUsage: 62,
    diskUsage: 78,
    networkStatus: "connected",
    databaseSize: "1.2 GB",
    activeUsers: 3,
    systemUptime: "15 days, 4 hours",
    lastUpdate: "2024-01-10T10:30:00Z"
  })

  // Notification Settings
  const [notificationSettings, setNotificationSettings] = useState({
    systemAlerts: true,
    performanceAlerts: true,
    securityAlerts: true,
    backupAlerts: true,
    updateAlerts: true,
    maintenanceMode: false,
    debugMode: false,
    logLevel: "info"
  })

  const handleSaveSettings = async (settingsType: string) => {
    setIsLoading(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast.success(`${settingsType} settings saved successfully!`)
    } catch (error) {
      toast.error(`Failed to save ${settingsType} settings`)
    } finally {
      setIsLoading(false)
    }
  }

  const handleBackupNow = async () => {
    setIsLoading(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 3000))
      setBackupSettings(prev => ({
        ...prev,
        lastBackup: new Date().toISOString()
      }))
      toast.success("Backup completed successfully!")
    } catch (error) {
      toast.error("Backup failed")
    } finally {
      setIsLoading(false)
    }
  }

  const handleTestConnection = async (service: string) => {
    setIsLoading(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 2000))
      toast.success(`${service} connection test successful!`)
    } catch (error) {
      toast.error(`${service} connection test failed`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">System Configuration</h2>
          <p className="text-muted-foreground">Advanced system settings and integrations</p>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="security" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Security
          </TabsTrigger>
          <TabsTrigger value="backup" className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            Backup
          </TabsTrigger>
          <TabsTrigger value="integrations" className="flex items-center gap-2">
            <Globe className="h-4 w-4" />
            Integrations
          </TabsTrigger>
          <TabsTrigger value="performance" className="flex items-center gap-2">
            <Server className="h-4 w-4" />
            Performance
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            System Alerts
          </TabsTrigger>
        </TabsList>

        {/* Security Tab */}
        <TabsContent value="security" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Lock className="h-5 w-5" />
                  Authentication & Access
                </CardTitle>
                <CardDescription>Configure user authentication and access controls</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label>Two-Factor Authentication</Label>
                    <p className="text-sm text-muted-foreground">Require 2FA for all users</p>
                  </div>
                  <Switch
                    checked={securitySettings.twoFactorAuth}
                    onCheckedChange={(checked) => 
                      setSecuritySettings({...securitySettings, twoFactorAuth: checked})
                    }
                  />
                </div>

                <div className="space-y-2">
                  <Label>Session Timeout (minutes)</Label>
                  <Select 
                    value={securitySettings.sessionTimeout} 
                    onValueChange={(value) => 
                      setSecuritySettings({...securitySettings, sessionTimeout: value})
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="15">15 minutes</SelectItem>
                      <SelectItem value="30">30 minutes</SelectItem>
                      <SelectItem value="60">1 hour</SelectItem>
                      <SelectItem value="120">2 hours</SelectItem>
                      <SelectItem value="480">8 hours</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Max Login Attempts</Label>
                  <Select 
                    value={securitySettings.maxLoginAttempts} 
                    onValueChange={(value) => 
                      setSecuritySettings({...securitySettings, maxLoginAttempts: value})
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="3">3 attempts</SelectItem>
                      <SelectItem value="5">5 attempts</SelectItem>
                      <SelectItem value="10">10 attempts</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Password Expiry (days)</Label>
                  <Input
                    value={securitySettings.passwordExpiry}
                    onChange={(e) => 
                      setSecuritySettings({...securitySettings, passwordExpiry: e.target.value})
                    }
                    placeholder="90"
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Key className="h-5 w-5" />
                  Encryption & Security
                </CardTitle>
                <CardDescription>Advanced security and encryption settings</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label>SSL/TLS Encryption</Label>
                    <p className="text-sm text-muted-foreground">Enable HTTPS encryption</p>
                  </div>
                  <Switch
                    checked={securitySettings.sslEnabled}
                    onCheckedChange={(checked) => 
                      setSecuritySettings({...securitySettings, sslEnabled: checked})
                    }
                  />
                </div>

                <div className="space-y-2">
                  <Label>Encryption Level</Label>
                  <Select 
                    value={securitySettings.encryptionLevel} 
                    onValueChange={(value) => 
                      setSecuritySettings({...securitySettings, encryptionLevel: value})
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="AES128">AES-128</SelectItem>
                      <SelectItem value="AES256">AES-256</SelectItem>
                      <SelectItem value="RSA2048">RSA-2048</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label>Audit Logging</Label>
                    <p className="text-sm text-muted-foreground">Log all user activities</p>
                  </div>
                  <Switch
                    checked={securitySettings.auditLogging}
                    onCheckedChange={(checked) => 
                      setSecuritySettings({...securitySettings, auditLogging: checked})
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label>Login Notifications</Label>
                    <p className="text-sm text-muted-foreground">Notify on new logins</p>
                  </div>
                  <Switch
                    checked={securitySettings.loginNotifications}
                    onCheckedChange={(checked) => 
                      setSecuritySettings({...securitySettings, loginNotifications: checked})
                    }
                  />
                </div>

                <div className="space-y-2">
                  <Label>IP Whitelist</Label>
                  <Input
                    value={securitySettings.ipWhitelist}
                    onChange={(e) => 
                      setSecuritySettings({...securitySettings, ipWhitelist: e.target.value})
                    }
                    placeholder="***********/24, 10.0.0.0/8"
                  />
                  <p className="text-xs text-muted-foreground">
                    Comma-separated list of allowed IP addresses/ranges
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="flex justify-end">
            <Button onClick={() => handleSaveSettings("Security")} disabled={isLoading}>
              {isLoading ? "Saving..." : "Save Security Settings"}
            </Button>
          </div>
        </TabsContent>

        {/* Backup Tab */}
        <TabsContent value="backup" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  Backup Configuration
                </CardTitle>
                <CardDescription>Configure automatic backup settings</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label>Automatic Backup</Label>
                    <p className="text-sm text-muted-foreground">Enable scheduled backups</p>
                  </div>
                  <Switch
                    checked={backupSettings.autoBackup}
                    onCheckedChange={(checked) => 
                      setBackupSettings({...backupSettings, autoBackup: checked})
                    }
                  />
                </div>

                <div className="space-y-2">
                  <Label>Backup Frequency</Label>
                  <Select 
                    value={backupSettings.backupFrequency} 
                    onValueChange={(value) => 
                      setBackupSettings({...backupSettings, backupFrequency: value})
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="hourly">Hourly</SelectItem>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Backup Time</Label>
                  <Input
                    type="time"
                    value={backupSettings.backupTime}
                    onChange={(e) => 
                      setBackupSettings({...backupSettings, backupTime: e.target.value})
                    }
                  />
                </div>

                <div className="space-y-2">
                  <Label>Retention Period (days)</Label>
                  <Input
                    value={backupSettings.backupRetention}
                    onChange={(e) => 
                      setBackupSettings({...backupSettings, backupRetention: e.target.value})
                    }
                    placeholder="30"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label>Encrypt Backups</Label>
                    <p className="text-sm text-muted-foreground">Encrypt backup files</p>
                  </div>
                  <Switch
                    checked={backupSettings.encryptBackups}
                    onCheckedChange={(checked) => 
                      setBackupSettings({...backupSettings, encryptBackups: checked})
                    }
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Cloud className="h-5 w-5" />
                  Cloud Backup
                </CardTitle>
                <CardDescription>Configure cloud backup settings</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label>Cloud Backup</Label>
                    <p className="text-sm text-muted-foreground">Enable cloud storage backup</p>
                  </div>
                  <Switch
                    checked={backupSettings.cloudBackup}
                    onCheckedChange={(checked) => 
                      setBackupSettings({...backupSettings, cloudBackup: checked})
                    }
                  />
                </div>

                {backupSettings.cloudBackup && (
                  <>
                    <div className="space-y-2">
                      <Label>Cloud Provider</Label>
                      <Select 
                        value={backupSettings.cloudProvider} 
                        onValueChange={(value) => 
                          setBackupSettings({...backupSettings, cloudProvider: value})
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="aws">Amazon S3</SelectItem>
                          <SelectItem value="gcp">Google Cloud</SelectItem>
                          <SelectItem value="azure">Microsoft Azure</SelectItem>
                          <SelectItem value="dropbox">Dropbox</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </>
                )}

                <Separator />

                <div className="space-y-2">
                  <Label>Backup Status</Label>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Last Backup</span>
                      <span>{new Date(backupSettings.lastBackup).toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Backup Size</span>
                      <span>{backupSettings.backupSize}</span>
                    </div>
                  </div>
                </div>

                <Button 
                  onClick={handleBackupNow} 
                  disabled={isLoading}
                  className="w-full"
                >
                  {isLoading ? "Creating Backup..." : "Backup Now"}
                </Button>
              </CardContent>
            </Card>
          </div>

          <div className="flex justify-end">
            <Button onClick={() => handleSaveSettings("Backup")} disabled={isLoading}>
              {isLoading ? "Saving..." : "Save Backup Settings"}
            </Button>
          </div>
        </TabsContent>
