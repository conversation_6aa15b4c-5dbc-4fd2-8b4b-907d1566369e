"use client"

import type React from "react"
import { useState, useEffect, useMemo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"
import { Textarea } from "@/components/ui/textarea"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useStore } from "@/lib/store"
import type { Scheme, Customer } from "@/lib/types"
import { 
  Plus, Calendar, Calculator, Co<PERSON>, Trending<PERSON>p, 
  Bell, Gift, Users, FileText, Star, Award
} from "lucide-react"

interface ComprehensiveSchemeFormProps {
  scheme?: Scheme
  onSubmit: () => void
  onCancel: () => void
}

interface SchemePayment {
  id: string
  dueDate: string
  amount: number
  paidDate: string
  paidAmount: number
  status: "pending" | "paid" | "overdue" | "partial"
  goldRate: number
  goldWeight: number
  notes: string
}

interface MaturityCalculation {
  totalPaid: number
  bonusAmount: number
  goldAccumulated: number
  currentGoldValue: number
  maturityValue: number
  profitAmount: number
}

export function ComprehensiveSchemeForm({ scheme, onSubmit, onCancel }: ComprehensiveSchemeFormProps) {
  const { addScheme, updateScheme, customers, getMetalRate } = useStore()
  
  // Basic form data
  const [formData, setFormData] = useState({
    schemeName: "",
    customerId: "",
    schemeType: "monthly_gold",
    schemeCategory: "gold_savings",
    installmentAmount: "",
    duration: "",
    startDate: "",
    maturityDate: "",
    goldRate: "",
    bonusPercentage: "",
    interestRate: "",
    status: "active",
    notes: "",
    autoDebit: false,
    smsNotifications: true,
    emailNotifications: false,
    whatsappNotifications: true,
  })

  // Payment schedule
  const [paymentSchedule, setPaymentSchedule] = useState<SchemePayment[]>([])
  
  // Current gold rates
  const [goldRates, setGoldRates] = useState({
    gold22K: 6500,
    gold24K: 7000,
    silver: 85,
  })

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [activeTab, setActiveTab] = useState("details")

  // Initialize form
  useEffect(() => {
    if (scheme) {
      // Load existing scheme data
      setFormData({
        schemeName: scheme.name,
        customerId: scheme.customer.id,
        schemeType: scheme.schemeType || "monthly_gold",
        schemeCategory: scheme.schemeCategory || "gold_savings",
        installmentAmount: scheme.monthlyAmount.toString(),
        duration: scheme.duration.toString(),
        startDate: scheme.startDate,
        maturityDate: scheme.maturityDate || "",
        goldRate: scheme.goldRate?.toString() || "",
        bonusPercentage: scheme.bonusPercentage?.toString() || "",
        interestRate: scheme.interestRate?.toString() || "",
        status: scheme.status,
        notes: scheme.notes || "",
        autoDebit: scheme.autoDebit || false,
        smsNotifications: scheme.smsNotifications !== false,
        emailNotifications: scheme.emailNotifications || false,
        whatsappNotifications: scheme.whatsappNotifications !== false,
      })
      
      setPaymentSchedule(scheme.paymentSchedule || [])
    } else {
      // Set defaults for new scheme
      const today = new Date().toISOString().split("T")[0]
      const currentGoldRate = getMetalRate("gold", "22K")
      
      setFormData(prev => ({
        ...prev,
        startDate: today,
        goldRate: currentGoldRate.toString()
      }))
      
      // Set current gold rates
      setGoldRates({
        gold22K: getMetalRate("gold", "22K"),
        gold24K: getMetalRate("gold", "24K"),
        silver: getMetalRate("silver", "925"),
      })
    }
  }, [scheme, getMetalRate])

  // Auto-calculate maturity date when duration changes
  useEffect(() => {
    if (formData.startDate && formData.duration) {
      const startDate = new Date(formData.startDate)
      const duration = parseInt(formData.duration)
      const maturityDate = new Date(startDate)
      
      if (formData.schemeType === "monthly_gold" || formData.schemeType === "monthly_amount") {
        maturityDate.setMonth(maturityDate.getMonth() + duration)
      } else if (formData.schemeType === "quarterly") {
        maturityDate.setMonth(maturityDate.getMonth() + (duration * 3))
      } else if (formData.schemeType === "yearly") {
        maturityDate.setFullYear(maturityDate.getFullYear() + duration)
      }
      
      setFormData(prev => ({
        ...prev,
        maturityDate: maturityDate.toISOString().split("T")[0]
      }))
    }
  }, [formData.startDate, formData.duration, formData.schemeType])

  // Generate payment schedule when key parameters change
  useEffect(() => {
    if (formData.startDate && formData.duration && formData.installmentAmount && formData.schemeType) {
      generatePaymentSchedule()
    }
  }, [formData.startDate, formData.duration, formData.installmentAmount, formData.schemeType])

  // Helper functions
  const handleInputChange = (field: string, value: any) => {
    setFormData({ ...formData, [field]: value })
    if (errors[field]) {
      setErrors({ ...errors, [field]: "" })
    }
  }

  const generatePaymentSchedule = () => {
    const startDate = new Date(formData.startDate)
    const duration = parseInt(formData.duration)
    const installmentAmount = parseFloat(formData.installmentAmount)
    const goldRate = parseFloat(formData.goldRate) || goldRates.gold22K
    
    if (!startDate || !duration || !installmentAmount) return
    
    const schedule: SchemePayment[] = []
    
    for (let i = 0; i < duration; i++) {
      const dueDate = new Date(startDate)
      
      if (formData.schemeType === "monthly_gold" || formData.schemeType === "monthly_amount") {
        dueDate.setMonth(dueDate.getMonth() + i + 1)
      } else if (formData.schemeType === "quarterly") {
        dueDate.setMonth(dueDate.getMonth() + ((i + 1) * 3))
      } else if (formData.schemeType === "yearly") {
        dueDate.setFullYear(dueDate.getFullYear() + i + 1)
      }
      
      const goldWeight = formData.schemeType.includes("gold") 
        ? installmentAmount / goldRate 
        : 0
      
      schedule.push({
        id: `payment_${i + 1}`,
        dueDate: dueDate.toISOString().split("T")[0],
        amount: installmentAmount,
        paidDate: "",
        paidAmount: 0,
        status: "pending",
        goldRate: goldRate,
        goldWeight: goldWeight,
        notes: ""
      })
    }
    
    setPaymentSchedule(schedule)
  }

  const getSchemeTypes = () => {
    return [
      { value: "monthly_gold", label: "Monthly Gold Scheme" },
      { value: "monthly_amount", label: "Monthly Amount Scheme" },
      { value: "quarterly", label: "Quarterly Scheme" },
      { value: "yearly", label: "Yearly Scheme" },
      { value: "flexible", label: "Flexible Payment Scheme" },
      { value: "advance_booking", label: "Advance Booking Scheme" }
    ]
  }

  const getSchemeCategories = () => {
    return [
      { value: "gold_savings", label: "Gold Savings Scheme" },
      { value: "diamond_scheme", label: "Diamond Scheme" },
      { value: "wedding_scheme", label: "Wedding Scheme" },
      { value: "festival_scheme", label: "Festival Scheme" },
      { value: "custom_scheme", label: "Custom Scheme" }
    ]
  }

  // Calculate maturity details
  const maturityCalculation = useMemo((): MaturityCalculation => {
    const totalInstallments = parseInt(formData.duration) || 0
    const installmentAmount = parseFloat(formData.installmentAmount) || 0
    const bonusPercentage = parseFloat(formData.bonusPercentage) || 0
    const goldRate = parseFloat(formData.goldRate) || goldRates.gold22K
    
    const totalPaid = totalInstallments * installmentAmount
    const bonusAmount = (totalPaid * bonusPercentage) / 100
    const goldAccumulated = formData.schemeType.includes("gold") 
      ? totalPaid / goldRate 
      : 0
    const currentGoldValue = goldAccumulated * goldRates.gold22K
    const maturityValue = totalPaid + bonusAmount
    const profitAmount = bonusAmount + (currentGoldValue - totalPaid)
    
    return {
      totalPaid,
      bonusAmount,
      goldAccumulated,
      currentGoldValue,
      maturityValue,
      profitAmount
    }
  }, [formData.duration, formData.installmentAmount, formData.bonusPercentage, formData.goldRate, formData.schemeType, goldRates])

  const updatePaymentStatus = (index: number, field: string, value: any) => {
    const updatedSchedule = [...paymentSchedule]
    const payment = updatedSchedule[index]
    
    if (field === "paidAmount") {
      payment.paidAmount = parseFloat(value) || 0
      payment.status = payment.paidAmount >= payment.amount ? "paid" : 
                     payment.paidAmount > 0 ? "partial" : "pending"
      
      if (payment.paidAmount >= payment.amount && !payment.paidDate) {
        payment.paidDate = new Date().toISOString().split("T")[0]
      }
    } else {
      (payment as any)[field] = value
    }
    
    updatedSchedule[index] = payment
    setPaymentSchedule(updatedSchedule)
  }

  return (
    <form className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="details" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Details
          </TabsTrigger>
          <TabsTrigger value="schedule" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Schedule
          </TabsTrigger>
          <TabsTrigger value="calculations" className="flex items-center gap-2">
            <Calculator className="h-4 w-4" />
            Calculations
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            Notifications
          </TabsTrigger>
        </TabsList>

        {/* Scheme Information Header */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Coins className="h-5 w-5" />
              Scheme Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="customerId">Customer *</Label>
                <Select value={formData.customerId || undefined} onValueChange={(value) => handleInputChange("customerId", value)}>
                  <SelectTrigger className={errors.customerId ? "border-red-500" : ""}>
                    <SelectValue placeholder="Select customer" />
                  </SelectTrigger>
                  <SelectContent>
                    {customers.filter(customer => customer.id && customer.id.trim() !== "").map((customer) => (
                      <SelectItem key={customer.id} value={customer.id}>
                        <div className="flex flex-col">
                          <span>{customer.name}</span>
                          <span className="text-xs text-muted-foreground">{customer.phone}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.customerId && <p className="text-sm text-red-500">{errors.customerId}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="schemeName">Scheme Name *</Label>
                <Input
                  id="schemeName"
                  value={formData.schemeName}
                  onChange={(e) => handleInputChange("schemeName", e.target.value)}
                  placeholder="Gold Savings Scheme 2024"
                  required
                  className={errors.schemeName ? "border-red-500" : ""}
                />
                {errors.schemeName && <p className="text-sm text-red-500">{errors.schemeName}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select value={formData.status} onValueChange={(value) => handleInputChange("status", value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                    <SelectItem value="on_hold">On Hold</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Details Tab */}
        <TabsContent value="details" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Scheme Configuration</CardTitle>
                <CardDescription>Basic scheme setup and parameters</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Scheme Type *</Label>
                  <Select value={formData.schemeType} onValueChange={(value) => handleInputChange("schemeType", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select scheme type" />
                    </SelectTrigger>
                    <SelectContent>
                      {getSchemeTypes().map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Scheme Category</Label>
                  <Select value={formData.schemeCategory} onValueChange={(value) => handleInputChange("schemeCategory", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {getSchemeCategories().map((category) => (
                        <SelectItem key={category.value} value={category.value}>
                          {category.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="installmentAmount">Installment Amount (₹) *</Label>
                    <Input
                      id="installmentAmount"
                      type="number"
                      step="0.01"
                      min="0"
                      value={formData.installmentAmount}
                      onChange={(e) => handleInputChange("installmentAmount", e.target.value)}
                      placeholder="5000"
                      required
                      className={errors.installmentAmount ? "border-red-500" : ""}
                    />
                    {errors.installmentAmount && <p className="text-sm text-red-500">{errors.installmentAmount}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="duration">Duration *</Label>
                    <Input
                      id="duration"
                      type="number"
                      min="1"
                      value={formData.duration}
                      onChange={(e) => handleInputChange("duration", e.target.value)}
                      placeholder="12"
                      required
                      className={errors.duration ? "border-red-500" : ""}
                    />
                    <p className="text-xs text-muted-foreground">
                      {formData.schemeType === "monthly_gold" || formData.schemeType === "monthly_amount" ? "Months" :
                       formData.schemeType === "quarterly" ? "Quarters" :
                       formData.schemeType === "yearly" ? "Years" : "Periods"}
                    </p>
                    {errors.duration && <p className="text-sm text-red-500">{errors.duration}</p>}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="startDate">Start Date *</Label>
                    <Input
                      id="startDate"
                      type="date"
                      value={formData.startDate}
                      onChange={(e) => handleInputChange("startDate", e.target.value)}
                      required
                      className={errors.startDate ? "border-red-500" : ""}
                    />
                    {errors.startDate && <p className="text-sm text-red-500">{errors.startDate}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="maturityDate">Maturity Date</Label>
                    <Input
                      id="maturityDate"
                      type="date"
                      value={formData.maturityDate}
                      readOnly
                      className="bg-gray-50"
                    />
                    <p className="text-xs text-muted-foreground">Auto-calculated</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Gold Rate & Benefits</CardTitle>
                <CardDescription>Current rates and scheme benefits</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="goldRate">Gold Rate (₹/g) *</Label>
                  <Input
                    id="goldRate"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.goldRate}
                    onChange={(e) => handleInputChange("goldRate", e.target.value)}
                    placeholder="6500"
                    className={errors.goldRate ? "border-red-500" : ""}
                  />
                  {errors.goldRate && <p className="text-sm text-red-500">{errors.goldRate}</p>}
                  <div className="text-xs text-muted-foreground">
                    <p>Current Rates: 22K: ₹{goldRates.gold22K}/g, 24K: ₹{goldRates.gold24K}/g</p>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="bonusPercentage">Bonus Percentage (%)</Label>
                    <Input
                      id="bonusPercentage"
                      type="number"
                      step="0.01"
                      min="0"
                      max="100"
                      value={formData.bonusPercentage}
                      onChange={(e) => handleInputChange("bonusPercentage", e.target.value)}
                      placeholder="5"
                    />
                    <p className="text-xs text-muted-foreground">Bonus on maturity</p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="interestRate">Interest Rate (%)</Label>
                    <Input
                      id="interestRate"
                      type="number"
                      step="0.01"
                      min="0"
                      max="100"
                      value={formData.interestRate}
                      onChange={(e) => handleInputChange("interestRate", e.target.value)}
                      placeholder="0"
                    />
                    <p className="text-xs text-muted-foreground">Annual interest rate</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="notes">Scheme Notes</Label>
                  <Textarea
                    id="notes"
                    value={formData.notes}
                    onChange={(e) => handleInputChange("notes", e.target.value)}
                    placeholder="Special terms, conditions, or notes about this scheme..."
                    rows={3}
                  />
                </div>

                {/* Current Gold Rates Display */}
                <Card className="bg-yellow-50 border-yellow-200">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm text-yellow-900">Current Market Rates</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <p className="text-yellow-700 font-medium">Gold 22K</p>
                        <p className="text-yellow-900 font-semibold">₹{goldRates.gold22K}/g</p>
                      </div>
                      <div>
                        <p className="text-yellow-700 font-medium">Gold 24K</p>
                        <p className="text-yellow-900 font-semibold">₹{goldRates.gold24K}/g</p>
                      </div>
                      <div>
                        <p className="text-yellow-700 font-medium">Silver</p>
                        <p className="text-yellow-900 font-semibold">₹{goldRates.silver}/g</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Schedule Tab */}
        <TabsContent value="schedule" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Payment Schedule
              </CardTitle>
              <CardDescription>
                Detailed payment schedule with due dates and gold accumulation
              </CardDescription>
            </CardHeader>
            <CardContent>
              {paymentSchedule.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Installment</TableHead>
                      <TableHead>Due Date</TableHead>
                      <TableHead>Amount (₹)</TableHead>
                      <TableHead>Gold Weight (g)</TableHead>
                      <TableHead>Gold Rate (₹/g)</TableHead>
                      <TableHead>Paid Amount</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {paymentSchedule.map((payment, index) => (
                      <TableRow key={payment.id}>
                        <TableCell className="font-medium">#{index + 1}</TableCell>
                        <TableCell>{new Date(payment.dueDate).toLocaleDateString()}</TableCell>
                        <TableCell>₹{payment.amount.toLocaleString()}</TableCell>
                        <TableCell className="text-blue-600 font-medium">
                          {payment.goldWeight.toFixed(3)}g
                        </TableCell>
                        <TableCell>₹{payment.goldRate.toLocaleString()}</TableCell>
                        <TableCell>
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            max={payment.amount}
                            value={payment.paidAmount}
                            onChange={(e) => updatePaymentStatus(index, "paidAmount", e.target.value)}
                            className="w-24"
                          />
                        </TableCell>
                        <TableCell>
                          <Badge variant={
                            payment.status === "paid" ? "default" :
                            payment.status === "partial" ? "secondary" :
                            payment.status === "overdue" ? "destructive" : "outline"
                          }>
                            {payment.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Button
                            type="button"
                            size="sm"
                            variant="outline"
                            onClick={() => updatePaymentStatus(index, "paidAmount", payment.amount)}
                            disabled={payment.status === "paid"}
                          >
                            Mark Paid
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Payment schedule will be generated automatically</p>
                  <p className="text-sm">Fill in the scheme details to see the payment schedule</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
