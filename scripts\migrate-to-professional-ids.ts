/**
 * Migration Script: Convert UUID-based IDs to Professional Business IDs
 * This script will convert all existing UUID-based identifiers to professional jewelry business formats
 */

import mysql from 'mysql2/promise'
import { 
  generateCustomerId,
  generateInventoryItemCode,
  generateInvoiceNumber,
  generateSaleId,
  generateRepairJobNumber,
  generateSchemeNumber,
  generatePurchaseOrderNumber,
  generateSupplierCode,
  generateExchangeNumber,
  generateReceiptNumber,
  generateBarcode,
  JEWELRY_CATEGORIES,
  METAL_CODES
} from '../lib/utils/professional-id-generator'

// Database configuration
const DB_CONFIG = {
  host: 'localhost',
  user: 'root',
  password: '', // Set your password
  database: 'jewellers_db'
}

interface MigrationResult {
  entity: string
  totalRecords: number
  migrated: number
  errors: string[]
  newSequence: number
}

class ProfessionalIdMigrator {
  private connection: mysql.Connection | null = null
  private results: MigrationResult[] = []

  async connect(): Promise<void> {
    this.connection = await mysql.createConnection(DB_CONFIG)
    console.log('✅ Connected to database')
  }

  async disconnect(): Promise<void> {
    if (this.connection) {
      await this.connection.end()
      console.log('✅ Disconnected from database')
    }
  }

  async initializeSequences(): Promise<void> {
    if (!this.connection) throw new Error('No database connection')

    console.log('\n🔄 Initializing sequence table...')

    // Create sequence table if not exists
    await this.connection.execute(`
      CREATE TABLE IF NOT EXISTS id_sequences (
        id VARCHAR(36) PRIMARY KEY,
        entity_type VARCHAR(50) NOT NULL,
        current_sequence INT NOT NULL DEFAULT 0,
        prefix VARCHAR(20),
        financial_year VARCHAR(10) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_entity_fy (entity_type, financial_year),
        INDEX idx_entity_type (entity_type),
        INDEX idx_financial_year (financial_year)
      )
    `)

    // Initialize sequences for current financial year
    const financialYear = '2024-25'
    const entityTypes = [
      'customer', 'inventory', 'sale', 'invoice', 'repair', 
      'scheme', 'purchase', 'supplier', 'exchange', 'receipt', 'estimate'
    ]

    for (const entityType of entityTypes) {
      await this.connection.execute(`
        INSERT IGNORE INTO id_sequences (id, entity_type, current_sequence, financial_year, created_at, updated_at)
        VALUES (UUID(), ?, 0, ?, NOW(), NOW())
      `, [entityType, financialYear])
    }

    console.log('✅ Sequence table initialized')
  }

  async migrateCustomers(): Promise<void> {
    if (!this.connection) throw new Error('No database connection')

    console.log('\n🔄 Migrating customer IDs...')

    const result: MigrationResult = {
      entity: 'customers',
      totalRecords: 0,
      migrated: 0,
      errors: [],
      newSequence: 0
    }

    try {
      // Get all customers
      const [customers] = await this.connection.execute('SELECT id, name FROM customers ORDER BY created_at')
      result.totalRecords = (customers as any[]).length

      if (result.totalRecords === 0) {
        console.log('   No customers to migrate')
        return
      }

      // Start transaction
      await this.connection.beginTransaction()

      let sequence = 1
      const idMapping = new Map<string, string>()

      for (const customer of customers as any[]) {
        const newCustomerId = generateCustomerId(sequence)
        const newCustomerCode = newCustomerId // Same format for code
        
        idMapping.set(customer.id, newCustomerId)
        
        console.log(`   ${customer.name}: ${customer.id} → ${newCustomerId}`)
        sequence++
      }

      // Update customers table
      for (const [oldId, newId] of idMapping) {
        await this.connection.execute(
          'UPDATE customers SET id = ?, customer_code = ? WHERE id = ?',
          [newId, newId, oldId]
        )
      }

      // Update related tables
      for (const [oldId, newId] of idMapping) {
        await this.connection.execute('UPDATE sales SET customer_id = ? WHERE customer_id = ?', [newId, oldId])
        await this.connection.execute('UPDATE repairs SET customer_id = ? WHERE customer_id = ?', [newId, oldId])
        await this.connection.execute('UPDATE schemes SET customer_id = ? WHERE customer_id = ?', [newId, oldId])
        await this.connection.execute('UPDATE exchange_transactions SET customer_id = ? WHERE customer_id = ?', [newId, oldId])
      }

      // Update sequence
      await this.connection.execute(
        'UPDATE id_sequences SET current_sequence = ? WHERE entity_type = ? AND financial_year = ?',
        [sequence - 1, 'customer', '2024-25']
      )

      await this.connection.commit()
      result.migrated = result.totalRecords
      result.newSequence = sequence - 1

      console.log(`✅ Migrated ${result.migrated} customers`)

    } catch (error) {
      await this.connection.rollback()
      result.errors.push(`Migration failed: ${error}`)
      console.error(`❌ Customer migration failed: ${error}`)
    }

    this.results.push(result)
  }

  async migrateInventory(): Promise<void> {
    if (!this.connection) throw new Error('No database connection')

    console.log('\n🔄 Migrating inventory item codes...')

    const result: MigrationResult = {
      entity: 'inventory',
      totalRecords: 0,
      migrated: 0,
      errors: [],
      newSequence: 0
    }

    try {
      // Get all inventory items
      const [items] = await this.connection.execute(`
        SELECT id, name, category_id, metal_type, purity 
        FROM inventory 
        ORDER BY created_at
      `)
      result.totalRecords = (items as any[]).length

      if (result.totalRecords === 0) {
        console.log('   No inventory items to migrate')
        return
      }

      // Get category mapping
      const [categories] = await this.connection.execute('SELECT id, name FROM inventory_categories')
      const categoryMap = new Map<string, string>()
      for (const cat of categories as any[]) {
        categoryMap.set(cat.id, cat.name)
      }

      await this.connection.beginTransaction()

      let sequence = 1
      const idMapping = new Map<string, string>()

      for (const item of items as any[]) {
        const categoryName = categoryMap.get(item.category_id) || 'Rings'
        const metalType = item.metal_type || 'gold'
        const purity = item.purity || '22K'

        const newItemCode = generateInventoryItemCode(categoryName, metalType, purity, sequence)
        const newBarcode = generateBarcode(categoryName, metalType, purity, sequence)
        
        idMapping.set(item.id, newItemCode)
        
        console.log(`   ${item.name}: ${item.id} → ${newItemCode}`)
        sequence++
      }

      // Update inventory table
      for (const [oldId, newId] of idMapping) {
        const item = (items as any[]).find(i => i.id === oldId)
        const categoryName = categoryMap.get(item.category_id) || 'Rings'
        const newBarcode = generateBarcode(categoryName, item.metal_type, item.purity, 
          parseInt(newId.split('-')[2]))

        await this.connection.execute(
          'UPDATE inventory SET id = ?, barcode = ? WHERE id = ?',
          [newId, newBarcode, oldId]
        )
      }

      // Update related tables
      for (const [oldId, newId] of idMapping) {
        await this.connection.execute('UPDATE sale_items SET inventory_id = ? WHERE inventory_id = ?', [newId, oldId])
        await this.connection.execute('UPDATE purchase_items SET inventory_id = ? WHERE inventory_id = ?', [newId, oldId])
      }

      // Update sequence
      await this.connection.execute(
        'UPDATE id_sequences SET current_sequence = ? WHERE entity_type = ? AND financial_year = ?',
        [sequence - 1, 'inventory', '2024-25']
      )

      await this.connection.commit()
      result.migrated = result.totalRecords
      result.newSequence = sequence - 1

      console.log(`✅ Migrated ${result.migrated} inventory items`)

    } catch (error) {
      await this.connection.rollback()
      result.errors.push(`Migration failed: ${error}`)
      console.error(`❌ Inventory migration failed: ${error}`)
    }

    this.results.push(result)
  }

  async migrateSales(): Promise<void> {
    if (!this.connection) throw new Error('No database connection')

    console.log('\n🔄 Migrating sales and invoice numbers...')

    const result: MigrationResult = {
      entity: 'sales',
      totalRecords: 0,
      migrated: 0,
      errors: [],
      newSequence: 0
    }

    try {
      // Get all sales
      const [sales] = await this.connection.execute('SELECT id, invoice_number FROM sales ORDER BY created_at')
      result.totalRecords = (sales as any[]).length

      if (result.totalRecords === 0) {
        console.log('   No sales to migrate')
        return
      }

      await this.connection.beginTransaction()

      let saleSequence = 1
      let invoiceSequence = 1

      for (const sale of sales as any[]) {
        const newSaleId = generateSaleId(saleSequence)
        const newInvoiceNumber = generateInvoiceNumber(invoiceSequence)
        
        await this.connection.execute(
          'UPDATE sales SET sale_number = ?, invoice_number = ? WHERE id = ?',
          [newSaleId, newInvoiceNumber, sale.id]
        )
        
        console.log(`   Sale: ${sale.id} → ${newSaleId} | Invoice: ${sale.invoice_number} → ${newInvoiceNumber}`)
        saleSequence++
        invoiceSequence++
      }

      // Update sequences
      await this.connection.execute(
        'UPDATE id_sequences SET current_sequence = ? WHERE entity_type = ? AND financial_year = ?',
        [saleSequence - 1, 'sale', '2024-25']
      )
      await this.connection.execute(
        'UPDATE id_sequences SET current_sequence = ? WHERE entity_type = ? AND financial_year = ?',
        [invoiceSequence - 1, 'invoice', '2024-25']
      )

      await this.connection.commit()
      result.migrated = result.totalRecords
      result.newSequence = Math.max(saleSequence - 1, invoiceSequence - 1)

      console.log(`✅ Migrated ${result.migrated} sales records`)

    } catch (error) {
      await this.connection.rollback()
      result.errors.push(`Migration failed: ${error}`)
      console.error(`❌ Sales migration failed: ${error}`)
    }

    this.results.push(result)
  }

  async generateMigrationReport(): Promise<void> {
    console.log('\n' + '='.repeat(80))
    console.log('📊 MIGRATION REPORT')
    console.log('='.repeat(80))

    let totalMigrated = 0
    let totalErrors = 0

    for (const result of this.results) {
      console.log(`\n${result.entity.toUpperCase()}:`)
      console.log(`  Total Records: ${result.totalRecords}`)
      console.log(`  Migrated: ${result.migrated}`)
      console.log(`  Errors: ${result.errors.length}`)
      console.log(`  New Sequence: ${result.newSequence}`)
      
      if (result.errors.length > 0) {
        console.log(`  Error Details:`)
        result.errors.forEach(error => console.log(`    - ${error}`))
      }

      totalMigrated += result.migrated
      totalErrors += result.errors.length
    }

    console.log('\n' + '-'.repeat(40))
    console.log(`SUMMARY:`)
    console.log(`  Total Records Migrated: ${totalMigrated}`)
    console.log(`  Total Errors: ${totalErrors}`)
    console.log(`  Migration Status: ${totalErrors === 0 ? '✅ SUCCESS' : '❌ PARTIAL/FAILED'}`)
    console.log('='.repeat(80))
  }

  async run(): Promise<void> {
    try {
      console.log('🚀 Starting Professional ID Migration...')
      
      await this.connect()
      await this.initializeSequences()
      
      // Run migrations in order (respecting foreign key dependencies)
      await this.migrateCustomers()
      await this.migrateInventory()
      await this.migrateSales()
      // Add more migrations as needed
      
      await this.generateMigrationReport()
      
    } catch (error) {
      console.error('💥 Migration failed:', error)
    } finally {
      await this.disconnect()
    }
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  const migrator = new ProfessionalIdMigrator()
  migrator.run().catch(console.error)
}

export { ProfessionalIdMigrator }
