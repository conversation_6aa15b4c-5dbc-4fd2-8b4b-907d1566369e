"use client"

import { useState, useMemo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { useStore } from "@/lib/store"
import { 
  Download, Filter, Users, TrendingUp, TrendingDown, 
  Heart, Gift, Phone, Mail, MapPin, Calendar,
  Star, Award, Target, Activity, BarChart3
} from "lucide-react"

export function CustomerAnalytics() {
  const { customers, sales } = useStore()
  const [activeTab, setActiveTab] = useState("overview")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [tierFilter, setTierFilter] = useState("all")
  const [searchTerm, setSearchTerm] = useState("")

  // Enhanced customer analytics
  const analytics = useMemo(() => {
    const totalCustomers = customers.length
    const activeCustomers = customers.filter(c => c.last_purchase_date && 
      new Date(c.last_purchase_date) > new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)).length
    
    // Customer segmentation
    const individualCustomers = customers.filter(c => c.customer_type === "individual" || !c.customer_type).length
    const businessCustomers = customers.filter(c => c.customer_type === "business").length
    
    // Loyalty tier distribution
    const bronzeCustomers = customers.filter(c => (c.loyalty_tier || "bronze") === "bronze").length
    const silverCustomers = customers.filter(c => c.loyalty_tier === "silver").length
    const goldCustomers = customers.filter(c => c.loyalty_tier === "gold").length
    const platinumCustomers = customers.filter(c => c.loyalty_tier === "platinum").length
    
    // Purchase analytics
    const totalPurchases = customers.reduce((sum, c) => sum + (c.total_purchases || 0), 0)
    const avgPurchaseValue = totalPurchases / totalCustomers || 0
    
    // Geographic distribution
    const stateDistribution = customers.reduce((acc, customer) => {
      const state = customer.state || "Unknown"
      acc[state] = (acc[state] || 0) + 1
      return acc
    }, {} as Record<string, number>)
    
    const topStates = Object.entries(stateDistribution)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
    
    // Age and gender analytics
    const genderDistribution = customers.reduce((acc, customer) => {
      const gender = customer.gender || "not_specified"
      acc[gender] = (acc[gender] || 0) + 1
      return acc
    }, {} as Record<string, number>)
    
    // Communication preferences
    const languageDistribution = customers.reduce((acc, customer) => {
      const language = customer.preferred_language || "english"
      acc[language] = (acc[language] || 0) + 1
      return acc
    }, {} as Record<string, number>)
    
    // Marketing consent analytics
    const marketingConsent = customers.filter(c => c.marketing_consent).length
    const whatsappConsent = customers.filter(c => c.whatsapp_consent).length
    const emailConsent = customers.filter(c => c.email_consent).length
    const smsConsent = customers.filter(c => c.sms_consent).length
    
    // High-value customers (top 20% by purchase value)
    const sortedByValue = customers
      .filter(c => c.total_purchases && c.total_purchases > 0)
      .sort((a, b) => (b.total_purchases || 0) - (a.total_purchases || 0))
    const highValueCustomers = sortedByValue.slice(0, Math.ceil(sortedByValue.length * 0.2))
    
    // Birthday and anniversary tracking
    const upcomingBirthdays = customers.filter(c => {
      if (!c.date_of_birth) return false
      const today = new Date()
      const birthday = new Date(c.date_of_birth)
      const thisYearBirthday = new Date(today.getFullYear(), birthday.getMonth(), birthday.getDate())
      const daysDiff = Math.ceil((thisYearBirthday.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
      return daysDiff >= 0 && daysDiff <= 30
    }).length
    
    const upcomingAnniversaries = customers.filter(c => {
      if (!c.anniversary_date) return false
      const today = new Date()
      const anniversary = new Date(c.anniversary_date)
      const thisYearAnniversary = new Date(today.getFullYear(), anniversary.getMonth(), anniversary.getDate())
      const daysDiff = Math.ceil((thisYearAnniversary.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
      return daysDiff >= 0 && daysDiff <= 30
    }).length
    
    return {
      totalCustomers,
      activeCustomers,
      individualCustomers,
      businessCustomers,
      bronzeCustomers,
      silverCustomers,
      goldCustomers,
      platinumCustomers,
      totalPurchases,
      avgPurchaseValue,
      topStates,
      genderDistribution,
      languageDistribution,
      marketingConsent,
      whatsappConsent,
      emailConsent,
      smsConsent,
      highValueCustomers,
      upcomingBirthdays,
      upcomingAnniversaries,
      customerRetentionRate: (activeCustomers / totalCustomers) * 100 || 0,
      avgCustomerLifetime: 18, // Mock data - months
      customerSatisfactionScore: 4.2 // Mock data - out of 5
    }
  }, [customers])

  // Filtered customers
  const filteredCustomers = useMemo(() => {
    return customers.filter((customer) => {
      if (categoryFilter !== "all" && customer.customer_category !== categoryFilter) return false
      if (tierFilter !== "all" && (customer.loyalty_tier || "bronze") !== tierFilter) return false
      if (searchTerm && !customer.name?.toLowerCase().includes(searchTerm.toLowerCase()) && 
          !customer.phone?.includes(searchTerm) && !customer.email?.toLowerCase().includes(searchTerm.toLowerCase())) return false
      return true
    })
  }, [customers, categoryFilter, tierFilter, searchTerm])

  const exportReport = () => {
    const reportData = {
      summary: analytics,
      customers: filteredCustomers,
      generatedAt: new Date().toISOString()
    }
    
    const dataStr = JSON.stringify(reportData, null, 2)
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)
    
    const exportFileDefaultName = `customer-analytics-${new Date().toISOString().split('T')[0]}.json`
    
    const linkElement = document.createElement('a')
    linkElement.setAttribute('href', dataUri)
    linkElement.setAttribute('download', exportFileDefaultName)
    linkElement.click()
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-2xl font-bold tracking-tight">Customer Analytics</h3>
          <p className="text-muted-foreground">Comprehensive customer insights and relationship management analytics</p>
        </div>
        <Button onClick={exportReport}>
          <Download className="h-4 w-4 mr-2" />
          Export Report
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label>Search</Label>
              <Input
                placeholder="Search customers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label>Category</Label>
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="regular">Regular</SelectItem>
                  <SelectItem value="premium">Premium</SelectItem>
                  <SelectItem value="vip">VIP</SelectItem>
                  <SelectItem value="wholesale">Wholesale</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Loyalty Tier</Label>
              <Select value={tierFilter} onValueChange={setTierFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Tiers" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Tiers</SelectItem>
                  <SelectItem value="bronze">Bronze</SelectItem>
                  <SelectItem value="silver">Silver</SelectItem>
                  <SelectItem value="gold">Gold</SelectItem>
                  <SelectItem value="platinum">Platinum</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>&nbsp;</Label>
              <Button 
                variant="outline" 
                onClick={() => {
                  setCategoryFilter("all")
                  setTierFilter("all")
                  setSearchTerm("")
                }}
                className="w-full"
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="segmentation" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Segmentation
          </TabsTrigger>
          <TabsTrigger value="engagement" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Engagement
          </TabsTrigger>
          <TabsTrigger value="detailed" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            Detailed
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Customers</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.totalCustomers}</div>
                <p className="text-xs text-muted-foreground">
                  {analytics.activeCustomers} active in last 90 days
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg Purchase Value</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">₹{analytics.avgPurchaseValue.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">Per customer lifetime</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Customer Retention</CardTitle>
                <Heart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">{analytics.customerRetentionRate.toFixed(1)}%</div>
                <p className="text-xs text-muted-foreground">90-day retention rate</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Upcoming Events</CardTitle>
                <Gift className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-600">
                  {analytics.upcomingBirthdays + analytics.upcomingAnniversaries}
                </div>
                <p className="text-xs text-muted-foreground">
                  {analytics.upcomingBirthdays} birthdays, {analytics.upcomingAnniversaries} anniversaries
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Customer Distribution */}
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Customer Type Distribution</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Individual Customers</span>
                    <span>{analytics.individualCustomers}</span>
                  </div>
                  <Progress value={(analytics.individualCustomers / analytics.totalCustomers) * 100} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Business Customers</span>
                    <span>{analytics.businessCustomers}</span>
                  </div>
                  <Progress value={(analytics.businessCustomers / analytics.totalCustomers) * 100} className="h-2" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Loyalty Tier Distribution</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Bronze</span>
                    <span>{analytics.bronzeCustomers}</span>
                  </div>
                  <Progress value={(analytics.bronzeCustomers / analytics.totalCustomers) * 100} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Silver</span>
                    <span>{analytics.silverCustomers}</span>
                  </div>
                  <Progress value={(analytics.silverCustomers / analytics.totalCustomers) * 100} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Gold</span>
                    <span>{analytics.goldCustomers}</span>
                  </div>
                  <Progress value={(analytics.goldCustomers / analytics.totalCustomers) * 100} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Platinum</span>
                    <span>{analytics.platinumCustomers}</span>
                  </div>
                  <Progress value={(analytics.platinumCustomers / analytics.totalCustomers) * 100} className="h-2" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Geographic Distribution */}
          <Card>
            <CardHeader>
              <CardTitle>Top States by Customer Count</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics.topStates.map(([state, count]) => (
                  <div key={state}>
                    <div className="flex justify-between text-sm mb-1">
                      <span>{state}</span>
                      <span>{count} customers</span>
                    </div>
                    <Progress value={(count / analytics.totalCustomers) * 100} className="h-2" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Segmentation Tab */}
        <TabsContent value="segmentation" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>High-Value Customers</CardTitle>
                <CardDescription>Top 20% customers by purchase value</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-green-600 mb-4">
                  {analytics.highValueCustomers.length}
                </div>
                <div className="space-y-2">
                  {analytics.highValueCustomers.slice(0, 5).map((customer) => (
                    <div key={customer.id} className="flex justify-between items-center">
                      <div>
                        <p className="font-medium">{customer.name}</p>
                        <p className="text-sm text-muted-foreground">{customer.phone}</p>
                      </div>
                      <Badge variant="outline">₹{(customer.total_purchases || 0).toLocaleString()}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Gender Distribution</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {Object.entries(analytics.genderDistribution).map(([gender, count]) => (
                  <div key={gender}>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="capitalize">{gender.replace('_', ' ')}</span>
                      <span>{count}</span>
                    </div>
                    <Progress value={(count / analytics.totalCustomers) * 100} className="h-2" />
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Engagement Tab */}
        <TabsContent value="engagement" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Communication Preferences</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Marketing Consent</span>
                    <span>{analytics.marketingConsent}</span>
                  </div>
                  <Progress value={(analytics.marketingConsent / analytics.totalCustomers) * 100} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>WhatsApp Consent</span>
                    <span>{analytics.whatsappConsent}</span>
                  </div>
                  <Progress value={(analytics.whatsappConsent / analytics.totalCustomers) * 100} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Email Consent</span>
                    <span>{analytics.emailConsent}</span>
                  </div>
                  <Progress value={(analytics.emailConsent / analytics.totalCustomers) * 100} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>SMS Consent</span>
                    <span>{analytics.smsConsent}</span>
                  </div>
                  <Progress value={(analytics.smsConsent / analytics.totalCustomers) * 100} className="h-2" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Language Preferences</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {Object.entries(analytics.languageDistribution).map(([language, count]) => (
                  <div key={language}>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="capitalize">{language}</span>
                      <span>{count}</span>
                    </div>
                    <Progress value={(count / analytics.totalCustomers) * 100} className="h-2" />
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Detailed Tab */}
        <TabsContent value="detailed" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Customer Details</CardTitle>
              <CardDescription>
                Complete customer listing with key information ({filteredCustomers.length} customers shown)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Customer</TableHead>
                    <TableHead>Contact</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Tier</TableHead>
                    <TableHead>Total Purchases</TableHead>
                    <TableHead>Last Visit</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredCustomers.map((customer) => (
                    <TableRow key={customer.id}>
                      <TableCell>
                        <div className="font-medium">{customer.name}</div>
                        {customer.business_name && (
                          <div className="text-xs text-muted-foreground">{customer.business_name}</div>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div className="flex items-center gap-1">
                            <Phone className="h-3 w-3" />
                            {customer.phone}
                          </div>
                          {customer.email && (
                            <div className="flex items-center gap-1 text-muted-foreground">
                              <Mail className="h-3 w-3" />
                              {customer.email}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className="capitalize">
                          {customer.customer_type || "individual"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary" className="capitalize">
                          {customer.loyalty_tier || "bronze"}
                        </Badge>
                      </TableCell>
                      <TableCell className="font-medium">
                        ₹{(customer.total_purchases || 0).toLocaleString()}
                      </TableCell>
                      <TableCell>
                        {customer.last_purchase_date ? (
                          <div className="text-sm">
                            {new Date(customer.last_purchase_date).toLocaleDateString()}
                          </div>
                        ) : (
                          <span className="text-muted-foreground">Never</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {customer.last_purchase_date &&
                         new Date(customer.last_purchase_date) > new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) ? (
                          <Badge variant="default">Active</Badge>
                        ) : (
                          <Badge variant="secondary">Inactive</Badge>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {filteredCustomers.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  No customers match the current filters
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
