"use client"

import type React from "react"
import { useState, use<PERSON>ffect, useMemo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"
import { Textarea } from "@/components/ui/textarea"
import { useStore } from "@/lib/store"
import type { RepairOrder, Customer } from "@/lib/types"
import { 
  Plus, Trash2, <PERSON><PERSON>, <PERSON>, User, FileText, 
  Camera, CheckCircle, AlertCircle, Calendar,
  Phone, Mail, <PERSON>ting<PERSON>, <PERSON>, Award
} from "lucide-react"

interface ComprehensiveRepairFormProps {
  repair?: RepairOrder
  onSubmit: () => void
  onCancel: () => void
}

interface RepairItem {
  id: string
  description: string
  metalType: string
  purity: string
  weight: number
  condition: string
  repairType: string
  estimatedCost: number
  actualCost: number
  partsRequired: string[]
  notes: string
}

interface QualityCheck {
  id: string
  checkPoint: string
  status: "pending" | "passed" | "failed"
  notes: string
  checkedBy: string
  checkedDate: string
}

interface RepairProgress {
  stage: string
  status: "pending" | "in_progress" | "completed" | "on_hold"
  startDate: string
  endDate: string
  assignedTo: string
  notes: string
  estimatedHours: number
  actualHours: number
}

export function ComprehensiveRepairForm({ repair, onSubmit, onCancel }: ComprehensiveRepairFormProps) {
  const { addRepair, updateRepair, customers } = useStore()
  
  // Basic form data
  const [formData, setFormData] = useState({
    customerId: "",
    jobNumber: "",
    receivedDate: "",
    promisedDate: "",
    completedDate: "",
    deliveredDate: "",
    priority: "normal",
    status: "received",
    assignedTechnician: "",
    totalEstimate: 0,
    totalActual: 0,
    advanceAmount: 0,
    balanceAmount: 0,
    customerNotes: "",
    internalNotes: "",
    specialInstructions: "",
  })

  // Repair items
  const [repairItems, setRepairItems] = useState<RepairItem[]>([])
  
  // Quality checks
  const [qualityChecks, setQualityChecks] = useState<QualityCheck[]>([])
  
  // Progress tracking
  const [progressStages, setProgressStages] = useState<RepairProgress[]>([])
  
  // Customer communication
  const [communicationLog, setCommunicationLog] = useState<Array<{
    id: string
    type: "call" | "sms" | "email" | "whatsapp"
    message: string
    sentDate: string
    status: "sent" | "delivered" | "read"
  }>>([])

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [activeTab, setActiveTab] = useState("details")

  // Initialize form
  useEffect(() => {
    if (repair) {
      // Load existing repair data
      setFormData({
        customerId: repair.customer.id,
        jobNumber: repair.jobNumber || "",
        receivedDate: repair.receivedDate,
        promisedDate: repair.promisedDate,
        completedDate: repair.completedDate || "",
        deliveredDate: repair.deliveredDate || "",
        priority: repair.priority || "normal",
        status: repair.status,
        assignedTechnician: repair.assignedTechnician || "",
        totalEstimate: repair.totalEstimate || 0,
        totalActual: repair.totalActual || 0,
        advanceAmount: repair.advanceAmount || 0,
        balanceAmount: repair.balanceAmount || 0,
        customerNotes: repair.customerNotes || "",
        internalNotes: repair.internalNotes || "",
        specialInstructions: repair.specialInstructions || "",
      })
      
      // Load repair items, quality checks, etc.
      setRepairItems(repair.repairItems || [])
      setQualityChecks(repair.qualityChecks || [])
      setProgressStages(repair.progressStages || [])
    } else {
      // Set defaults for new repair
      const today = new Date().toISOString().split("T")[0]
      const nextWeek = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split("T")[0]
      const jobNumber = `JOB-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`
      
      setFormData(prev => ({
        ...prev,
        receivedDate: today,
        promisedDate: nextWeek,
        jobNumber
      }))
      
      // Initialize default progress stages
      const defaultStages: RepairProgress[] = [
        {
          stage: "Assessment",
          status: "pending",
          startDate: "",
          endDate: "",
          assignedTo: "",
          notes: "",
          estimatedHours: 1,
          actualHours: 0
        },
        {
          stage: "Repair Work",
          status: "pending",
          startDate: "",
          endDate: "",
          assignedTo: "",
          notes: "",
          estimatedHours: 4,
          actualHours: 0
        },
        {
          stage: "Quality Check",
          status: "pending",
          startDate: "",
          endDate: "",
          assignedTo: "",
          notes: "",
          estimatedHours: 0.5,
          actualHours: 0
        },
        {
          stage: "Final Polish",
          status: "pending",
          startDate: "",
          endDate: "",
          assignedTo: "",
          notes: "",
          estimatedHours: 1,
          actualHours: 0
        }
      ]
      
      setProgressStages(defaultStages)
      
      // Initialize default quality checks
      const defaultQualityChecks: QualityCheck[] = [
        {
          id: "qc1",
          checkPoint: "Structural Integrity",
          status: "pending",
          notes: "",
          checkedBy: "",
          checkedDate: ""
        },
        {
          id: "qc2",
          checkPoint: "Stone Security",
          status: "pending",
          notes: "",
          checkedBy: "",
          checkedDate: ""
        },
        {
          id: "qc3",
          checkPoint: "Finish Quality",
          status: "pending",
          notes: "",
          checkedBy: "",
          checkedDate: ""
        },
        {
          id: "qc4",
          checkPoint: "Customer Requirements",
          status: "pending",
          notes: "",
          checkedBy: "",
          checkedDate: ""
        }
      ]
      
      setQualityChecks(defaultQualityChecks)
    }
  }, [repair])

  // Helper functions
  const handleInputChange = (field: string, value: any) => {
    setFormData({ ...formData, [field]: value })
    if (errors[field]) {
      setErrors({ ...errors, [field]: "" })
    }
  }

  const addRepairItem = () => {
    const newItem: RepairItem = {
      id: `item_${Date.now()}`,
      description: "",
      metalType: "gold",
      purity: "22K",
      weight: 0,
      condition: "good",
      repairType: "general_repair",
      estimatedCost: 0,
      actualCost: 0,
      partsRequired: [],
      notes: ""
    }
    setRepairItems([...repairItems, newItem])
  }

  const removeRepairItem = (index: number) => {
    setRepairItems(repairItems.filter((_, i) => i !== index))
  }

  const updateRepairItem = (index: number, field: string, value: any) => {
    const updatedItems = [...repairItems]
    const item = updatedItems[index]
    
    if (field === "weight" || field === "estimatedCost" || field === "actualCost") {
      (item as any)[field] = Number(value) || 0
    } else {
      (item as any)[field] = value
    }
    
    updatedItems[index] = item
    setRepairItems(updatedItems)
  }

  const updateQualityCheck = (index: number, field: string, value: any) => {
    const updatedChecks = [...qualityChecks]
    const check = updatedChecks[index]
    
    if (field === "status" && value === "passed" || value === "failed") {
      check.checkedDate = new Date().toISOString().split("T")[0]
      check.checkedBy = formData.assignedTechnician || "System"
    }
    
    (check as any)[field] = value
    updatedChecks[index] = check
    setQualityChecks(updatedChecks)
  }

  const updateProgressStage = (index: number, field: string, value: any) => {
    const updatedStages = [...progressStages]
    const stage = updatedStages[index]
    
    if (field === "status") {
      if (value === "in_progress" && !stage.startDate) {
        stage.startDate = new Date().toISOString().split("T")[0]
      } else if (value === "completed" && !stage.endDate) {
        stage.endDate = new Date().toISOString().split("T")[0]
      }
    }
    
    if (field === "actualHours") {
      (stage as any)[field] = Number(value) || 0
    } else {
      (stage as any)[field] = value
    }
    
    updatedStages[index] = stage
    setProgressStages(updatedStages)
  }

  // Get repair types for Indian jewelry
  const getRepairTypes = () => {
    return [
      "General Repair", "Stone Setting", "Prong Repair", "Chain Repair",
      "Ring Sizing", "Clasp Repair", "Soldering", "Polish & Clean",
      "Rhodium Plating", "Antique Restoration", "Engraving", "Enamel Work",
      "Kundan Setting", "Meenakari Work", "Temple Jewelry Repair",
      "Traditional Design Restoration", "Stone Replacement", "Weight Addition"
    ]
  }

  const getTechnicians = () => {
    return [
      "Rajesh Kumar", "Suresh Patel", "Amit Sharma", "Deepak Singh",
      "Mohan Lal", "Vikram Joshi", "Ravi Gupta", "Sanjay Verma"
    ]
  }

  const getConditionOptions = () => {
    return ["Excellent", "Good", "Fair", "Poor", "Damaged"]
  }

  // Calculate totals
  const calculations = useMemo(() => {
    const totalEstimate = repairItems.reduce((sum, item) => sum + item.estimatedCost, 0)
    const totalActual = repairItems.reduce((sum, item) => sum + item.actualCost, 0)
    const balanceAmount = Math.max(0, totalActual - formData.advanceAmount)
    
    return {
      totalEstimate,
      totalActual,
      balanceAmount,
      totalHoursEstimated: progressStages.reduce((sum, stage) => sum + stage.estimatedHours, 0),
      totalHoursActual: progressStages.reduce((sum, stage) => sum + stage.actualHours, 0),
      completedStages: progressStages.filter(stage => stage.status === "completed").length,
      totalStages: progressStages.length
    }
  }, [repairItems, progressStages, formData.advanceAmount])

  return (
    <form className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="details" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Details
          </TabsTrigger>
          <TabsTrigger value="items" className="flex items-center gap-2">
            <Wrench className="h-4 w-4" />
            Items
          </TabsTrigger>
          <TabsTrigger value="progress" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Progress
          </TabsTrigger>
          <TabsTrigger value="quality" className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4" />
            Quality
          </TabsTrigger>
          <TabsTrigger value="communication" className="flex items-center gap-2">
            <Phone className="h-4 w-4" />
            Communication
          </TabsTrigger>
        </TabsList>

        {/* Job Information Header */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Job Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label htmlFor="customerId">Customer *</Label>
                <Select value={formData.customerId || undefined} onValueChange={(value) => handleInputChange("customerId", value)}>
                  <SelectTrigger className={errors.customerId ? "border-red-500" : ""}>
                    <SelectValue placeholder="Select customer" />
                  </SelectTrigger>
                  <SelectContent>
                    {customers.filter(customer => customer.id && customer.id.trim() !== "").map((customer) => (
                      <SelectItem key={customer.id} value={customer.id}>
                        <div className="flex flex-col">
                          <span>{customer.name}</span>
                          <span className="text-xs text-muted-foreground">{customer.phone}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.customerId && <p className="text-sm text-red-500">{errors.customerId}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="jobNumber">Job Number</Label>
                <Input
                  id="jobNumber"
                  value={formData.jobNumber}
                  onChange={(e) => handleInputChange("jobNumber", e.target.value)}
                  placeholder="Auto-generated"
                  className="bg-gray-50"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="priority">Priority</Label>
                <Select value={formData.priority} onValueChange={(value) => handleInputChange("priority", value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="normal">Normal</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="urgent">Urgent</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select value={formData.status} onValueChange={(value) => handleInputChange("status", value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="received">Received</SelectItem>
                    <SelectItem value="assessed">Assessed</SelectItem>
                    <SelectItem value="approved">Approved</SelectItem>
                    <SelectItem value="in_progress">In Progress</SelectItem>
                    <SelectItem value="quality_check">Quality Check</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="ready_for_delivery">Ready for Delivery</SelectItem>
                    <SelectItem value="delivered">Delivered</SelectItem>
                    <SelectItem value="on_hold">On Hold</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
              <div className="space-y-2">
                <Label htmlFor="receivedDate">Received Date *</Label>
                <Input
                  id="receivedDate"
                  type="date"
                  value={formData.receivedDate}
                  onChange={(e) => handleInputChange("receivedDate", e.target.value)}
                  required
                  className={errors.receivedDate ? "border-red-500" : ""}
                />
                {errors.receivedDate && <p className="text-sm text-red-500">{errors.receivedDate}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="promisedDate">Promised Date *</Label>
                <Input
                  id="promisedDate"
                  type="date"
                  value={formData.promisedDate}
                  onChange={(e) => handleInputChange("promisedDate", e.target.value)}
                  required
                  className={errors.promisedDate ? "border-red-500" : ""}
                />
                {errors.promisedDate && <p className="text-sm text-red-500">{errors.promisedDate}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="assignedTechnician">Assigned Technician</Label>
                <Select value={formData.assignedTechnician} onValueChange={(value) => handleInputChange("assignedTechnician", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select technician" />
                  </SelectTrigger>
                  <SelectContent>
                    {getTechnicians().map((tech) => (
                      <SelectItem key={tech} value={tech}>
                        {tech}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Details Tab */}
        <TabsContent value="details" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Customer Notes</CardTitle>
                <CardDescription>What the customer told us about the repair</CardDescription>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={formData.customerNotes}
                  onChange={(e) => handleInputChange("customerNotes", e.target.value)}
                  placeholder="Customer's description of the problem..."
                  rows={4}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Internal Notes</CardTitle>
                <CardDescription>Internal observations and technical notes</CardDescription>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={formData.internalNotes}
                  onChange={(e) => handleInputChange("internalNotes", e.target.value)}
                  placeholder="Technical assessment and internal notes..."
                  rows={4}
                />
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Special Instructions</CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                value={formData.specialInstructions}
                onChange={(e) => handleInputChange("specialInstructions", e.target.value)}
                placeholder="Any special handling instructions or customer preferences..."
                rows={3}
              />
            </CardContent>
          </Card>

          {/* Cost Summary */}
          <Card className="bg-blue-50 border-blue-200">
            <CardHeader>
              <CardTitle className="text-blue-900">Cost Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <p className="text-blue-700 font-medium">Estimated Cost</p>
                  <p className="text-2xl font-bold text-blue-900">₹{calculations.totalEstimate.toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-blue-700 font-medium">Actual Cost</p>
                  <p className="text-2xl font-bold text-green-600">₹{calculations.totalActual.toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-blue-700 font-medium">Advance Paid</p>
                  <Input
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.advanceAmount}
                    onChange={(e) => handleInputChange("advanceAmount", Number(e.target.value))}
                    className="mt-1"
                  />
                </div>
                <div>
                  <p className="text-blue-700 font-medium">Balance Due</p>
                  <p className="text-2xl font-bold text-red-600">₹{calculations.balanceAmount.toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Items Tab */}
        <TabsContent value="items" className="space-y-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Wrench className="h-5 w-5" />
                Repair Items
              </CardTitle>
              <Button type="button" onClick={addRepairItem} size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Item
              </Button>
            </CardHeader>
            <CardContent className="space-y-4">
              {repairItems.map((item, index) => (
                <Card key={item.id} className="p-4 border-l-4 border-l-orange-500">
                  <div className="flex justify-between items-start mb-4">
                    <h4 className="font-medium">Item {index + 1}</h4>
                    <Button type="button" variant="ghost" size="sm" onClick={() => removeRepairItem(index)}>
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div className="space-y-2">
                      <Label>Item Description *</Label>
                      <Input
                        value={item.description}
                        onChange={(e) => updateRepairItem(index, "description", e.target.value)}
                        placeholder="Gold ring, Silver necklace, etc."
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Repair Type</Label>
                      <Select
                        value={item.repairType}
                        onValueChange={(value) => updateRepairItem(index, "repairType", value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select repair type" />
                        </SelectTrigger>
                        <SelectContent>
                          {getRepairTypes().map((type) => (
                            <SelectItem key={type} value={type.toLowerCase().replace(/\s+/g, '_')}>
                              {type}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                    <div className="space-y-2">
                      <Label>Metal Type</Label>
                      <Select
                        value={item.metalType}
                        onValueChange={(value) => updateRepairItem(index, "metalType", value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="gold">Gold</SelectItem>
                          <SelectItem value="silver">Silver</SelectItem>
                          <SelectItem value="platinum">Platinum</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Purity</Label>
                      <Select
                        value={item.purity}
                        onValueChange={(value) => updateRepairItem(index, "purity", value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="24K">24K</SelectItem>
                          <SelectItem value="22K">22K</SelectItem>
                          <SelectItem value="18K">18K</SelectItem>
                          <SelectItem value="925">925 Silver</SelectItem>
                          <SelectItem value="950">950 Platinum</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Weight (g)</Label>
                      <Input
                        type="number"
                        step="0.001"
                        min="0"
                        value={item.weight}
                        onChange={(e) => updateRepairItem(index, "weight", e.target.value)}
                        placeholder="25.500"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Condition</Label>
                      <Select
                        value={item.condition}
                        onValueChange={(value) => updateRepairItem(index, "condition", value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {getConditionOptions().map((condition) => (
                            <SelectItem key={condition} value={condition.toLowerCase()}>
                              {condition}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div className="space-y-2">
                      <Label>Estimated Cost (₹)</Label>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        value={item.estimatedCost}
                        onChange={(e) => updateRepairItem(index, "estimatedCost", e.target.value)}
                        placeholder="1500"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Actual Cost (₹)</Label>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        value={item.actualCost}
                        onChange={(e) => updateRepairItem(index, "actualCost", e.target.value)}
                        placeholder="1650"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Item Notes</Label>
                    <Textarea
                      value={item.notes}
                      onChange={(e) => updateRepairItem(index, "notes", e.target.value)}
                      placeholder="Specific notes about this item..."
                      rows={2}
                    />
                  </div>
                </Card>
              ))}

              {repairItems.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  <Wrench className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No repair items added yet. Click "Add Item" to start.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
