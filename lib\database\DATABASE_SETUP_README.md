# 💎 Database Setup Guide - Jewelry Management System

Complete database setup guide for the comprehensive jewelry management system with updated schema and sample data.

## 📋 Overview

This directory contains all the necessary files to set up a complete, production-ready database for the jewelry management system with:

- **22 Tables** - Complete schema with all enhanced features
- **Comprehensive Sample Data** - Realistic test data for all modules
- **Automated Setup Scripts** - Easy setup for Windows and Linux/Mac
- **Production-Ready Configuration** - Optimized for performance and security

## 🗂️ Files Description

### Schema Files
- **`updated-comprehensive-schema.sql`** - Complete database schema with all tables, indexes, and constraints
- **`reset-database.sql`** - Database reset script for clean installation

### Data Files
- **`seed-sample-data.sql`** - Comprehensive sample data for testing all features
- **`complete-setup.sql`** - Combined setup guide with instructions

### Setup Scripts
- **`setup-database.sh`** - Automated setup script for Linux/Mac
- **`setup-database.bat`** - Automated setup script for Windows
- **`DATABASE_SETUP_README.md`** - This documentation file

## 🚀 Quick Setup

### Option 1: Automated Setup (Recommended)

#### For Windows:
```cmd
cd lib/database
setup-database.bat
```

#### For Linux/Mac:
```bash
cd lib/database
chmod +x setup-database.sh
./setup-database.sh
```

### Option 2: Manual Setup

1. **Create Database and Schema:**
   ```sql
   mysql -u root -p < updated-comprehensive-schema.sql
   ```

2. **Insert Sample Data:**
   ```sql
   mysql -u root -p < seed-sample-data.sql
   ```

## 📊 Database Schema Overview

### Core System Tables
- **`users`** - User management with roles and permissions
- **`business_settings`** - Complete business configuration
- **`metal_rates`** - Current metal rates with history
- **`audit_log`** - System audit trail
- **`system_logs`** - Application logs

### Customer Management
- **`customers`** - Enhanced customer profiles with KYC
- **`notifications`** - Customer notifications
- **`reminders`** - Automated reminders

### Inventory Management
- **`inventory_categories`** - Product categories
- **`inventory`** - Complete item tracking with Indian jewelry specifications

### Sales Management
- **`sales`** - GST-compliant sales transactions
- **`sale_items`** - Individual items in sales

### Repair Management
- **`repairs`** - Complete repair job tracking

### Gold Scheme Management
- **`schemes`** - Gold scheme management
- **`scheme_payments`** - Individual scheme payments

### Purchase Management
- **`suppliers`** - Supplier management
- **`purchases`** - Purchase orders
- **`purchase_items`** - Individual purchase items

### Exchange Management
- **`exchange_rates`** - Exchange rates for old gold/silver
- **`exchange_transactions`** - Exchange transactions
- **`exchange_items`** - Individual exchange items

### Reports & Analytics
- **`report_templates`** - Saved report configurations

## 🎯 Sample Data Included

### Users (5 records)
- **Super Admin** - Full system access
- **Manager** - Management operations
- **Sales Staff** - Sales and customer operations
- **Accountant** - Financial and reporting access
- **Technician** - Repair operations

### Customers (5 records)
- Different customer types (Regular, Premium, VIP)
- Complete KYC information
- Various locations in Mumbai/Maharashtra
- Realistic purchase history and loyalty points

### Inventory (5 records)
- **Gold Ring with Diamond** - Engagement ring with solitaire
- **22K Gold Chain** - Traditional daily wear chain
- **Silver Bangles Set** - Traditional silver bangles
- **Diamond Earrings** - Elegant diamond studs
- **Gold Necklace Set** - Complete wedding set

### Sales Transactions (3 records)
- Cash, card, and mixed payment methods
- GST calculations and compliance
- Exchange transactions included

### Gold Schemes (3 records)
- Monthly savings scheme
- Advance payment scheme
- Auto-debit scheme with payments

### Repair Orders (3 records)
- Chain repair (completed)
- Ring resizing (in progress)
- Stone setting (received)

### Suppliers (3 records)
- Gold supplier from Mumbai
- Diamond supplier from Surat
- Silver supplier from Jaipur

### Purchase Orders (3 records)
- Various status (received, completed, ordered)
- Different suppliers and payment terms

### Exchange Transactions (2 records)
- Old gold exchange for new purchase
- Silver jewelry exchange

## 🔐 Default Login Credentials

**⚠️ IMPORTANT: Change these immediately after setup!**

- **Email:** `<EMAIL>`
- **Password:** `admin123`
- **Role:** Super Admin

## 🛠️ Configuration Requirements

### Prerequisites
- **MySQL 8.0+** or **MariaDB 10.4+**
- **Sufficient privileges** to create databases
- **UTF8MB4 support** for proper character encoding

### Database Configuration
- **Character Set:** `utf8mb4`
- **Collation:** `utf8mb4_unicode_ci`
- **SQL Mode:** `STRICT_TRANS_TABLES`
- **Foreign Key Checks:** Enabled

## 📈 Performance Optimization

The schema includes:
- **Optimized Indexes** - For fast queries on common operations
- **Proper Foreign Keys** - Data integrity and referential constraints
- **Generated Columns** - Automatic calculations (net weight, balance amounts)
- **Triggers** - Automatic updates for totals and stock levels
- **Table Analysis** - Optimized statistics for query planning

## 🔧 Customization

### Business Settings
Update the `business_settings` table with your:
- Business information and legal details
- GST rates and tax configuration
- Metal rates and margins
- Operating hours and preferences

### Metal Rates
Update current market rates in:
- `metal_rates` table for selling rates
- `exchange_rates` table for old gold/silver rates

### Categories
Add or modify inventory categories in:
- `inventory_categories` table

## 🚨 Important Notes

### Security
- Change default passwords immediately
- Review user permissions and roles
- Set up proper backup schedules
- Enable audit logging for compliance

### Data Integrity
- All foreign key constraints are enforced
- Triggers maintain data consistency
- Generated columns ensure calculation accuracy

### Backup Strategy
- Regular automated backups recommended
- Test restore procedures
- Keep backups in secure, separate location

## 🆘 Troubleshooting

### Common Issues

1. **MySQL Connection Failed**
   - Check MySQL service is running
   - Verify credentials and permissions
   - Ensure MySQL is in system PATH

2. **Character Encoding Issues**
   - Ensure UTF8MB4 support is enabled
   - Check MySQL configuration for character sets

3. **Foreign Key Constraint Errors**
   - Ensure proper order of data insertion
   - Check referential integrity

4. **Permission Denied**
   - Run with appropriate MySQL user privileges
   - Check file permissions on SQL files

### Support
For technical support or issues:
- Check application logs for detailed error messages
- Verify database connection settings
- Ensure all prerequisites are met

## 📞 Contact

For technical support:
- **Email:** <EMAIL>
- **Phone:** +91 98765 43210

---

**© 2024 Shree Jewellers Management System. All rights reserved.**
