"use client"

import { useState, useMemo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { useStore } from "@/lib/store"
import { 
  TrendingUp, TrendingDown, IndianRupee, ShoppingCart, 
  Users, Package, Wrench, Coins, Building, Target,
  Calendar, Award, AlertCircle, CheckCircle, Clock,
  BarChart3, PieChart, Activity, Download
} from "lucide-react"

export function ExecutiveDashboard() {
  const { sales, customers, inventory, repairs, schemes, purchases } = useStore()
  const [dateRange, setDateRange] = useState("month")
  const [activeTab, setActiveTab] = useState("overview")

  // Comprehensive business metrics
  const businessMetrics = useMemo(() => {
    const now = new Date()
    const startDate = new Date()
    
    // Set date range
    if (dateRange === "today") {
      startDate.setHours(0, 0, 0, 0)
    } else if (dateRange === "week") {
      startDate.setDate(now.getDate() - 7)
    } else if (dateRange === "month") {
      startDate.setMonth(now.getMonth() - 1)
    } else if (dateRange === "quarter") {
      startDate.setMonth(now.getMonth() - 3)
    } else if (dateRange === "year") {
      startDate.setFullYear(now.getFullYear() - 1)
    }

    // Filter data by date range
    const filteredSales = sales.filter(sale => new Date(sale.date) >= startDate)
    const filteredRepairs = repairs.filter(repair => new Date(repair.receivedDate) >= startDate)
    const filteredSchemes = schemes.filter(scheme => new Date(scheme.startDate) >= startDate)
    const filteredPurchases = purchases.filter(purchase => new Date(purchase.date) >= startDate)

    // Sales Metrics
    const totalRevenue = filteredSales.reduce((sum, sale) => sum + (sale.total || 0), 0)
    const totalTransactions = filteredSales.length
    const avgTransactionValue = totalTransactions > 0 ? totalRevenue / totalTransactions : 0
    const totalGoldSold = filteredSales.reduce((sum, sale) => 
      sum + sale.items.reduce((itemSum, item) => itemSum + (item.netWeight || 0), 0), 0
    )

    // Customer Metrics
    const totalCustomers = customers.length
    const activeCustomers = customers.filter(customer => {
      const lastVisit = new Date(customer.lastVisit)
      return lastVisit >= startDate
    }).length
    const newCustomers = customers.filter(customer => 
      new Date(customer.createdAt || customer.registrationDate) >= startDate
    ).length

    // Inventory Metrics
    const totalInventoryValue = inventory.reduce((sum, item) => 
      sum + ((item.currentValue || 0) * (item.stock || 0)), 0
    )
    const lowStockItems = inventory.filter(item => (item.stock || 0) <= (item.minStock || 5)).length
    const outOfStockItems = inventory.filter(item => (item.stock || 0) === 0).length

    // Repair Metrics
    const totalRepairs = filteredRepairs.length
    const completedRepairs = filteredRepairs.filter(repair => repair.status === "completed" || repair.status === "delivered").length
    const pendingRepairs = filteredRepairs.filter(repair => repair.status === "pending" || repair.status === "in_progress").length
    const repairRevenue = filteredRepairs.reduce((sum, repair) => sum + (repair.charges || 0), 0)

    // Scheme Metrics
    const totalSchemes = filteredSchemes.length
    const activeSchemes = filteredSchemes.filter(scheme => scheme.status === "active").length
    const schemeCommitments = filteredSchemes.reduce((sum, scheme) => 
      sum + (scheme.totalAmount || (scheme.monthlyAmount * scheme.duration)), 0
    )
    const schemeCollections = filteredSchemes.reduce((sum, scheme) => sum + (scheme.paidAmount || 0), 0)

    // Purchase Metrics
    const totalPurchases = filteredPurchases.reduce((sum, purchase) => sum + (purchase.amount || 0), 0)
    const purchaseOrders = filteredPurchases.length
    const pendingPurchases = filteredPurchases.filter(purchase => purchase.status === "pending").length

    // Growth Calculations (comparing with previous period)
    const previousPeriodRevenue = totalRevenue * 0.88 // Mock 12% growth
    const revenueGrowth = previousPeriodRevenue > 0 ? ((totalRevenue - previousPeriodRevenue) / previousPeriodRevenue) * 100 : 0
    
    const previousPeriodCustomers = activeCustomers * 0.92 // Mock 8% growth
    const customerGrowth = previousPeriodCustomers > 0 ? ((activeCustomers - previousPeriodCustomers) / previousPeriodCustomers) * 100 : 0

    // Profitability Analysis
    const grossProfit = totalRevenue * 0.25 // Assuming 25% gross margin
    const operatingExpenses = totalRevenue * 0.15 // Assuming 15% operating expenses
    const netProfit = grossProfit - operatingExpenses
    const profitMargin = totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0

    // Key Performance Indicators
    const customerRetentionRate = totalCustomers > 0 ? (activeCustomers / totalCustomers) * 100 : 0
    const inventoryTurnover = totalRevenue > 0 && totalInventoryValue > 0 ? totalRevenue / totalInventoryValue : 0
    const repairCompletionRate = totalRepairs > 0 ? (completedRepairs / totalRepairs) * 100 : 0
    const schemeCollectionRate = schemeCommitments > 0 ? (schemeCollections / schemeCommitments) * 100 : 0

    // Alerts and Notifications
    const alerts = []
    if (lowStockItems > 0) alerts.push({ type: "warning", message: `${lowStockItems} items low in stock` })
    if (outOfStockItems > 0) alerts.push({ type: "error", message: `${outOfStockItems} items out of stock` })
    if (pendingRepairs > 10) alerts.push({ type: "warning", message: `${pendingRepairs} pending repairs` })
    if (pendingPurchases > 5) alerts.push({ type: "info", message: `${pendingPurchases} pending purchase orders` })

    return {
      // Revenue & Sales
      totalRevenue,
      totalTransactions,
      avgTransactionValue,
      totalGoldSold,
      revenueGrowth,
      
      // Customers
      totalCustomers,
      activeCustomers,
      newCustomers,
      customerGrowth,
      customerRetentionRate,
      
      // Inventory
      totalInventoryValue,
      lowStockItems,
      outOfStockItems,
      inventoryTurnover,
      
      // Repairs
      totalRepairs,
      completedRepairs,
      pendingRepairs,
      repairRevenue,
      repairCompletionRate,
      
      // Schemes
      totalSchemes,
      activeSchemes,
      schemeCommitments,
      schemeCollections,
      schemeCollectionRate,
      
      // Purchases
      totalPurchases,
      purchaseOrders,
      pendingPurchases,
      
      // Profitability
      grossProfit,
      netProfit,
      profitMargin,
      
      // Alerts
      alerts
    }
  }, [sales, customers, inventory, repairs, schemes, purchases, dateRange])

  const exportDashboard = () => {
    const dashboardData = {
      dateRange,
      metrics: businessMetrics,
      generatedAt: new Date().toISOString()
    }
    
    const dataStr = JSON.stringify(dashboardData, null, 2)
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)
    
    const exportFileDefaultName = `executive-dashboard-${new Date().toISOString().split('T')[0]}.json`
    
    const linkElement = document.createElement('a')
    linkElement.setAttribute('href', dataUri)
    linkElement.setAttribute('download', exportFileDefaultName)
    linkElement.click()
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Executive Dashboard</h2>
          <p className="text-muted-foreground">Comprehensive business overview and key performance indicators</p>
        </div>
        <div className="flex items-center gap-4">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="today">Today</SelectItem>
              <SelectItem value="week">This Week</SelectItem>
              <SelectItem value="month">This Month</SelectItem>
              <SelectItem value="quarter">This Quarter</SelectItem>
              <SelectItem value="year">This Year</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={exportDashboard}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Alerts */}
      {businessMetrics.alerts.length > 0 && (
        <Card className="border-orange-200 bg-orange-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-orange-900">
              <AlertCircle className="h-5 w-5" />
              Business Alerts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {businessMetrics.alerts.map((alert, index) => (
                <div key={index} className="flex items-center gap-2">
                  <Badge variant={alert.type === "error" ? "destructive" : alert.type === "warning" ? "secondary" : "outline"}>
                    {alert.type}
                  </Badge>
                  <span className="text-sm">{alert.message}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="financial" className="flex items-center gap-2">
            <IndianRupee className="h-4 w-4" />
            Financial
          </TabsTrigger>
          <TabsTrigger value="operations" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Operations
          </TabsTrigger>
          <TabsTrigger value="performance" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            Performance
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                <IndianRupee className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">₹{businessMetrics.totalRevenue.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground flex items-center">
                  {businessMetrics.revenueGrowth > 0 ? (
                    <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                  ) : (
                    <TrendingDown className="h-3 w-3 mr-1 text-red-500" />
                  )}
                  {businessMetrics.revenueGrowth > 0 ? "+" : ""}{businessMetrics.revenueGrowth.toFixed(1)}% from last period
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Customers</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{businessMetrics.activeCustomers}</div>
                <p className="text-xs text-muted-foreground flex items-center">
                  {businessMetrics.customerGrowth > 0 ? (
                    <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                  ) : (
                    <TrendingDown className="h-3 w-3 mr-1 text-red-500" />
                  )}
                  {businessMetrics.customerGrowth > 0 ? "+" : ""}{businessMetrics.customerGrowth.toFixed(1)}% growth
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Inventory Value</CardTitle>
                <Package className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">₹{businessMetrics.totalInventoryValue.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  {businessMetrics.lowStockItems} low stock items
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Net Profit</CardTitle>
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">₹{businessMetrics.netProfit.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  {businessMetrics.profitMargin.toFixed(1)}% margin
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Business Overview Cards */}
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ShoppingCart className="h-5 w-5" />
                  Sales Overview
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Total Transactions</span>
                  <span className="font-semibold">{businessMetrics.totalTransactions}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Avg Transaction Value</span>
                  <span className="font-semibold">₹{businessMetrics.avgTransactionValue.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Gold Sold</span>
                  <span className="font-semibold text-yellow-600">{businessMetrics.totalGoldSold.toFixed(1)}g</span>
                </div>
                <div className="pt-2 border-t">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Revenue Growth</span>
                    <Badge variant={businessMetrics.revenueGrowth > 0 ? "default" : "secondary"}>
                      {businessMetrics.revenueGrowth > 0 ? "+" : ""}{businessMetrics.revenueGrowth.toFixed(1)}%
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Wrench className="h-5 w-5" />
                  Repair Services
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Total Repairs</span>
                  <span className="font-semibold">{businessMetrics.totalRepairs}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Completed</span>
                  <span className="font-semibold text-green-600">{businessMetrics.completedRepairs}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Pending</span>
                  <span className="font-semibold text-orange-600">{businessMetrics.pendingRepairs}</span>
                </div>
                <div className="pt-2 border-t">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Completion Rate</span>
                    <span className="font-semibold">{businessMetrics.repairCompletionRate.toFixed(1)}%</span>
                  </div>
                  <Progress value={businessMetrics.repairCompletionRate} className="h-2 mt-1" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Coins className="h-5 w-5" />
                  Gold Schemes
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Active Schemes</span>
                  <span className="font-semibold">{businessMetrics.activeSchemes}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Total Commitment</span>
                  <span className="font-semibold">₹{businessMetrics.schemeCommitments.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Collections</span>
                  <span className="font-semibold text-green-600">₹{businessMetrics.schemeCollections.toLocaleString()}</span>
                </div>
                <div className="pt-2 border-t">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Collection Rate</span>
                    <span className="font-semibold">{businessMetrics.schemeCollectionRate.toFixed(1)}%</span>
                  </div>
                  <Progress value={businessMetrics.schemeCollectionRate} className="h-2 mt-1" />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Financial Tab */}
        <TabsContent value="financial" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Revenue Breakdown</CardTitle>
                <CardDescription>Revenue sources and profitability analysis</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Sales Revenue</span>
                    <span className="font-semibold">₹{businessMetrics.totalRevenue.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Repair Revenue</span>
                    <span className="font-semibold">₹{businessMetrics.repairRevenue.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Scheme Collections</span>
                    <span className="font-semibold">₹{businessMetrics.schemeCollections.toLocaleString()}</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Total Revenue</span>
                    <span className="font-bold">₹{(businessMetrics.totalRevenue + businessMetrics.repairRevenue + businessMetrics.schemeCollections).toLocaleString()}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Profitability Analysis</CardTitle>
                <CardDescription>Profit margins and financial health</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Gross Profit</span>
                    <span className="font-semibold text-green-600">₹{businessMetrics.grossProfit.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Operating Expenses</span>
                    <span className="font-semibold text-red-600">₹{(businessMetrics.grossProfit - businessMetrics.netProfit).toLocaleString()}</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Net Profit</span>
                    <span className="font-bold text-green-600">₹{businessMetrics.netProfit.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Profit Margin</span>
                    <span className="font-bold">{businessMetrics.profitMargin.toFixed(1)}%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Cash Flow Overview</CardTitle>
              <CardDescription>Money in vs money out analysis</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    ₹{(businessMetrics.totalRevenue + businessMetrics.repairRevenue + businessMetrics.schemeCollections).toLocaleString()}
                  </div>
                  <p className="text-sm text-muted-foreground">Money In</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    ₹{businessMetrics.totalPurchases.toLocaleString()}
                  </div>
                  <p className="text-sm text-muted-foreground">Money Out</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    ₹{((businessMetrics.totalRevenue + businessMetrics.repairRevenue + businessMetrics.schemeCollections) - businessMetrics.totalPurchases).toLocaleString()}
                  </div>
                  <p className="text-sm text-muted-foreground">Net Cash Flow</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Operations Tab */}
        <TabsContent value="operations" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Inventory Management</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Total Inventory Value</span>
                  <span className="font-semibold">₹{businessMetrics.totalInventoryValue.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Low Stock Items</span>
                  <Badge variant={businessMetrics.lowStockItems > 0 ? "destructive" : "outline"}>
                    {businessMetrics.lowStockItems}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Out of Stock</span>
                  <Badge variant={businessMetrics.outOfStockItems > 0 ? "destructive" : "outline"}>
                    {businessMetrics.outOfStockItems}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Inventory Turnover</span>
                  <span className="font-semibold">{businessMetrics.inventoryTurnover.toFixed(1)}x</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Purchase Orders</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Total Purchase Value</span>
                  <span className="font-semibold">₹{businessMetrics.totalPurchases.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Purchase Orders</span>
                  <span className="font-semibold">{businessMetrics.purchaseOrders}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Pending Orders</span>
                  <Badge variant={businessMetrics.pendingPurchases > 0 ? "secondary" : "outline"}>
                    {businessMetrics.pendingPurchases}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Customer Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Customer Retention</span>
                    <span>{businessMetrics.customerRetentionRate.toFixed(1)}%</span>
                  </div>
                  <Progress value={businessMetrics.customerRetentionRate} className="h-2" />
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Total Customers</span>
                    <span className="font-medium">{businessMetrics.totalCustomers}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Active Customers</span>
                    <span className="font-medium">{businessMetrics.activeCustomers}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>New Customers</span>
                    <span className="font-medium text-green-600">{businessMetrics.newCustomers}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Service Quality</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Repair Completion</span>
                    <span>{businessMetrics.repairCompletionRate.toFixed(1)}%</span>
                  </div>
                  <Progress value={businessMetrics.repairCompletionRate} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Scheme Collection</span>
                    <span>{businessMetrics.schemeCollectionRate.toFixed(1)}%</span>
                  </div>
                  <Progress value={businessMetrics.schemeCollectionRate} className="h-2" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Business Efficiency</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Inventory Turnover</span>
                    <span className="font-medium">{businessMetrics.inventoryTurnover.toFixed(1)}x</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Avg Transaction Value</span>
                    <span className="font-medium">₹{businessMetrics.avgTransactionValue.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Profit Margin</span>
                    <span className="font-medium">{businessMetrics.profitMargin.toFixed(1)}%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
