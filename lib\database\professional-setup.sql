-- PROFESSIONAL JEWELRY MANAGEMENT SYSTEM - COMPLETE SETUP
-- Version: 5.0.0 - Professional ID Standards
-- Date: January 31, 2025
-- Description: Complete setup with professional business ID formats

-- ============================================================================
-- SETUP INSTRUCTIONS
-- ============================================================================

/*
PROFESSIONAL JEWELRY MANAGEMENT SYSTEM SETUP

This script provides a complete setup with professional business ID formats:

PROFESSIONAL ID FORMATS:
- Customer IDs: CUST-YYYY-NNNNNN (e.g., CUST-2024-000001)
- Inventory Codes: RGGD22-YYYY-NNNN (e.g., RGGD22-2024-0001)
- Invoice Numbers: INV-FY-NNNNNN (e.g., INV-2024-25-000001)
- Repair Jobs: REP-FY-NNNNNN (e.g., REP-2024-25-000001)
- Scheme Numbers: SCH-FY-NNNNNN (e.g., SCH-2024-25-000001)
- Purchase Orders: PO-FY-NNNNNN (e.g., PO-2024-25-000001)
- Supplier Codes: SUPP-YYYY-NNNN (e.g., SUPP-2024-0001)
- Exchange Numbers: EXG-FY-NNNNNN (e.g., EXG-2024-25-000001)
- Barcodes: SJ01224000001 (Business+Category+Metal+Purity+Sequence)

FEATURES:
✅ Professional ID formats following jewelry business standards
✅ Automatic sequence management with financial year support
✅ Comprehensive sample data with realistic business scenarios
✅ GST compliance and Indian jewelry business requirements
✅ Complete audit trail and data integrity
✅ Performance optimized with proper indexes

USAGE:
1. Execute this script to set up the complete system
2. Use the professional ID service in your application
3. All new records will automatically get professional IDs
4. Use migration script to convert existing UUID data

WARNING: This will reset the database completely!
*/

-- ============================================================================
-- SAFETY CONFIRMATION
-- ============================================================================

-- Uncomment the following line to confirm you want to setup the database
-- SET @CONFIRM_SETUP = 'YES_I_WANT_TO_SETUP_PROFESSIONAL_SYSTEM';

-- Safety check
SELECT CASE 
  WHEN @CONFIRM_SETUP = 'YES_I_WANT_TO_SETUP_PROFESSIONAL_SYSTEM' THEN 'Proceeding with professional system setup...'
  ELSE 'Setup cancelled. Uncomment the confirmation line to proceed.'
END as setup_status;

-- ============================================================================
-- STEP 1: DATABASE RESET AND CREATION
-- ============================================================================

-- Disable foreign key checks
SET FOREIGN_KEY_CHECKS = 0;

-- Drop existing database
DROP DATABASE IF EXISTS jewellers_db;

-- Create fresh database
CREATE DATABASE jewellers_db 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE jewellers_db;

-- Set proper SQL mode (compatible with MySQL 8.0+)
SET sql_mode = 'STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION';

-- ============================================================================
-- STEP 2: EXECUTE PROFESSIONAL SCHEMA
-- ============================================================================

-- Execute the updated comprehensive schema
-- Note: Run updated-comprehensive-schema.sql first

-- ============================================================================
-- STEP 3: LOAD PROFESSIONAL SAMPLE DATA
-- ============================================================================

-- Execute the professional sample data
-- Note: Run professional-sample-data.sql after schema creation

-- ============================================================================
-- STEP 4: VERIFICATION AND TESTING
-- ============================================================================

-- Verify professional ID formats
SELECT 'PROFESSIONAL ID VERIFICATION' as verification_section;

-- Check customer IDs
SELECT 
  'Customer IDs' as entity_type,
  customer_code as professional_id,
  name,
  CASE 
    WHEN customer_code REGEXP '^CUST-[0-9]{4}-[0-9]{6}$' THEN '✅ Valid'
    ELSE '❌ Invalid'
  END as format_status
FROM customers 
ORDER BY customer_code;

-- Check inventory item codes
SELECT 
  'Inventory Codes' as entity_type,
  id as professional_id,
  barcode,
  name,
  CASE 
    WHEN id REGEXP '^[A-Z]{2}[A-Z]{2}[0-9]{2,3}-[0-9]{4}-[0-9]{4}$' THEN '✅ Valid'
    ELSE '❌ Invalid'
  END as format_status
FROM inventory 
ORDER BY id;

-- Check sales and invoice numbers
SELECT 
  'Sales & Invoices' as entity_type,
  CONCAT(sale_number, ' | ', invoice_number) as professional_id,
  total_amount,
  CASE 
    WHEN sale_number REGEXP '^SALE-[0-9]{4}-[0-9]{6}$' 
     AND invoice_number REGEXP '^INV-[0-9]{4}-[0-9]{2}-[0-9]{6}$' THEN '✅ Valid'
    ELSE '❌ Invalid'
  END as format_status
FROM sales 
ORDER BY sale_number;

-- Check scheme numbers
SELECT 
  'Scheme Numbers' as entity_type,
  scheme_number as professional_id,
  scheme_name,
  total_amount,
  CASE 
    WHEN scheme_number REGEXP '^SCH-[0-9]{4}-[0-9]{2}-[0-9]{6}$' THEN '✅ Valid'
    ELSE '❌ Invalid'
  END as format_status
FROM schemes 
ORDER BY scheme_number;

-- Check supplier codes
SELECT 
  'Supplier Codes' as entity_type,
  supplier_code as professional_id,
  name,
  CASE 
    WHEN supplier_code REGEXP '^SUPP-[0-9]{4}-[0-9]{4}$' THEN '✅ Valid'
    ELSE '❌ Invalid'
  END as format_status
FROM suppliers 
ORDER BY supplier_code;

-- ============================================================================
-- STEP 5: SEQUENCE STATUS VERIFICATION
-- ============================================================================

SELECT 'SEQUENCE STATUS' as verification_section;

-- Show current sequences
SELECT 
  entity_type as 'Entity Type',
  current_sequence as 'Current Sequence',
  financial_year as 'Financial Year',
  updated_at as 'Last Updated'
FROM id_sequences 
ORDER BY entity_type;

-- ============================================================================
-- STEP 6: BUSINESS CONFIGURATION
-- ============================================================================

SELECT 'BUSINESS CONFIGURATION' as verification_section;

-- Show business settings
SELECT 
  business_name as 'Business Name',
  business_type as 'Type',
  gst_number as 'GST Number',
  established_year as 'Established',
  currency_symbol as 'Currency'
FROM business_settings;

-- Show metal rates
SELECT 
  metal_type as 'Metal',
  purity as 'Purity',
  rate_per_gram as 'Rate/Gram',
  margin_percentage as 'Margin %',
  effective_date as 'Effective Date'
FROM metal_rates 
WHERE is_active = TRUE
ORDER BY metal_type, purity;

-- ============================================================================
-- STEP 7: DATA SUMMARY
-- ============================================================================

SELECT 'DATA SUMMARY' as verification_section;

-- Count records by entity
SELECT 
  'users' as entity, COUNT(*) as count FROM users
UNION ALL
SELECT 'customers', COUNT(*) FROM customers
UNION ALL
SELECT 'inventory', COUNT(*) FROM inventory
UNION ALL
SELECT 'sales', COUNT(*) FROM sales
UNION ALL
SELECT 'schemes', COUNT(*) FROM schemes
UNION ALL
SELECT 'repairs', COUNT(*) FROM repairs
UNION ALL
SELECT 'suppliers', COUNT(*) FROM suppliers
UNION ALL
SELECT 'purchases', COUNT(*) FROM purchases
UNION ALL
SELECT 'exchange_transactions', COUNT(*) FROM exchange_transactions
UNION ALL
SELECT 'id_sequences', COUNT(*) FROM id_sequences;

-- ============================================================================
-- STEP 8: PERFORMANCE OPTIMIZATION
-- ============================================================================

-- Analyze tables for better performance
ANALYZE TABLE users, customers, inventory, sales, schemes, repairs, suppliers, purchases, exchange_transactions, id_sequences;

-- Update table statistics
OPTIMIZE TABLE users, customers, inventory, sales, schemes, repairs, suppliers, purchases, exchange_transactions, id_sequences;

-- ============================================================================
-- SETUP COMPLETION
-- ============================================================================

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Final verification
SELECT 
  '🎉 PROFESSIONAL JEWELRY MANAGEMENT SYSTEM SETUP COMPLETED!' as message,
  NOW() as completed_at,
  'jewellers_db' as database_name,
  '5.0.0' as version,
  'Professional ID Standards' as feature,
  (SELECT COUNT(*) FROM information_schema.TABLES WHERE TABLE_SCHEMA = 'jewellers_db') as total_tables,
  (SELECT COUNT(*) FROM users) as total_users,
  (SELECT COUNT(*) FROM customers) as total_customers,
  (SELECT COUNT(*) FROM inventory) as total_inventory_items;

-- Show professional login credentials
SELECT 
  '🔐 DEFAULT LOGIN CREDENTIALS' as info,
  'Email: <EMAIL>' as email,
  'Password: admin123' as password,
  '⚠️ CHANGE THESE CREDENTIALS IMMEDIATELY!' as warning;

-- ============================================================================
-- NEXT STEPS
-- ============================================================================

/*
🚀 NEXT STEPS AFTER PROFESSIONAL SETUP:

1. SECURITY & ACCESS:
   ✅ Change default admin password immediately
   ✅ Create additional users with appropriate roles
   ✅ Review and update business settings

2. PROFESSIONAL ID CONFIGURATION:
   ✅ All new records will automatically get professional IDs
   ✅ Use ProfessionalIdService in your application
   ✅ Sequence numbers are automatically managed
   ✅ Financial year transitions are handled automatically

3. BUSINESS CONFIGURATION:
   ✅ Update business information in business_settings table
   ✅ Configure current metal rates for market prices
   ✅ Set up inventory categories as needed
   ✅ Configure GST rates and tax settings

4. TESTING & VALIDATION:
   ✅ Test all modules with professional sample data
   ✅ Verify all ID formats are working correctly
   ✅ Run application and check connectivity
   ✅ Test ID generation and sequence management

5. PRODUCTION PREPARATION:
   ✅ Remove or modify sample data as needed
   ✅ Set up regular backup schedules
   ✅ Configure monitoring and logging
   ✅ Train users on new professional ID formats

6. APPLICATION INTEGRATION:
   ✅ Update database connection settings
   ✅ Use ProfessionalIdService for all new records
   ✅ Update forms to show professional ID previews
   ✅ Update reports to use professional formats

PROFESSIONAL ID EXAMPLES:
- Customer: CUST-2024-000001
- Inventory: RGGD22-2024-0001 (Ring, Gold, 22K, 2024, sequence 1)
- Invoice: INV-2024-25-000001 (Financial Year 2024-25)
- Barcode: SJ01224000001 (Shree Jewellers format)

🎯 Your jewelry management system now uses professional business-standard IDs!
*/
