#!/usr/bin/env tsx

// Load environment variables FIRST
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import mysql from 'mysql2/promise'

async function fixMissingTables() {
  console.log('🔧 Fixing Missing Tables and API Issues...\n')
  
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jj_jewellers_tiruppur',
  }

  let connection: mysql.Connection | null = null

  try {
    console.log('📡 Connecting to jj_jewellers_tiruppur database...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected successfully\n')

    // Step 1: Check which tables exist
    console.log('🔍 Step 1: Checking existing tables...')
    
    const [tables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ?
    `, [dbConfig.database])

    const existingTables = (tables as any[]).map(row => row.TABLE_NAME)
    console.log('   📋 Existing tables:', existingTables.join(', '))

    const requiredTables = ['users', 'schemes', 'repairs', 'settings', 'business_settings']
    const missingTables = requiredTables.filter(table => !existingTables.includes(table))
    
    if (missingTables.length > 0) {
      console.log('   ❌ Missing tables:', missingTables.join(', '))
    } else {
      console.log('   ✅ All required tables exist')
    }

    // Step 2: Create missing tables
    if (missingTables.includes('schemes')) {
      console.log('\n🔧 Creating schemes table...')
      await connection.execute(`
        CREATE TABLE schemes (
          id VARCHAR(36) PRIMARY KEY,
          scheme_name VARCHAR(255) NOT NULL,
          customer_id VARCHAR(36),
          total_amount DECIMAL(15,2) DEFAULT 0.00,
          paid_amount DECIMAL(15,2) DEFAULT 0.00,
          monthly_amount DECIMAL(15,2) DEFAULT 0.00,
          duration INT DEFAULT 12,
          start_date DATE,
          maturity_date DATE,
          status ENUM('active', 'completed', 'cancelled') DEFAULT 'active',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_customer_id (customer_id),
          INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `)
      console.log('   ✅ Schemes table created')
    }

    if (missingTables.includes('repairs')) {
      console.log('\n🔧 Creating repairs table...')
      await connection.execute(`
        CREATE TABLE repairs (
          id VARCHAR(36) PRIMARY KEY,
          repair_number VARCHAR(50) UNIQUE,
          customer_id VARCHAR(36),
          customer_name VARCHAR(255),
          item_description TEXT,
          repair_type VARCHAR(100),
          estimated_cost DECIMAL(10,2) DEFAULT 0.00,
          actual_cost DECIMAL(10,2) DEFAULT 0.00,
          advance_amount DECIMAL(10,2) DEFAULT 0.00,
          balance_amount DECIMAL(10,2) DEFAULT 0.00,
          received_date DATE,
          promised_date DATE,
          completed_date DATE,
          status ENUM('received', 'in_progress', 'completed', 'delivered', 'cancelled') DEFAULT 'received',
          notes TEXT,
          internal_notes TEXT,
          received_by VARCHAR(100),
          assigned_to VARCHAR(100),
          completed_by VARCHAR(100),
          delivered_by VARCHAR(100),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_customer_id (customer_id),
          INDEX idx_status (status),
          INDEX idx_repair_number (repair_number)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `)
      console.log('   ✅ Repairs table created')
    }

    if (missingTables.includes('settings')) {
      console.log('\n🔧 Creating settings table...')
      await connection.execute(`
        CREATE TABLE settings (
          id INT AUTO_INCREMENT PRIMARY KEY,
          business_name VARCHAR(255) DEFAULT 'Shree Jewellers',
          address TEXT,
          phone VARCHAR(20),
          email VARCHAR(100),
          gst_number VARCHAR(20),
          metal_rates JSON,
          auto_update_rates BOOLEAN DEFAULT TRUE,
          cgst_rate DECIMAL(5,2) DEFAULT 1.50,
          sgst_rate DECIMAL(5,2) DEFAULT 1.50,
          low_stock_alert BOOLEAN DEFAULT TRUE,
          low_stock_threshold INT DEFAULT 5,
          scheme_reminders BOOLEAN DEFAULT TRUE,
          repair_reminders BOOLEAN DEFAULT TRUE,
          currency VARCHAR(10) DEFAULT 'INR',
          date_format VARCHAR(20) DEFAULT 'DD/MM/YYYY',
          backup_frequency VARCHAR(20) DEFAULT 'daily',
          invoice_template VARCHAR(50) DEFAULT 'standard',
          print_logo BOOLEAN DEFAULT TRUE,
          print_terms BOOLEAN DEFAULT TRUE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `)
      console.log('   ✅ Settings table created')

      // Insert default settings
      await connection.execute(`
        INSERT INTO settings (
          business_name, address, phone, email, gst_number, metal_rates,
          cgst_rate, sgst_rate, low_stock_threshold
        ) VALUES (
          'Shree Jewellers',
          '123 Main Street, Tiruppur, Tamil Nadu',
          '+91-421-2345678',
          '<EMAIL>',
          '33ABCDE1234F1Z5',
          '{"gold": {"22K": "6420", "18K": "5250"}, "silver": {"925": "785"}}',
          1.50,
          1.50,
          5
        )
      `)
      console.log('   ✅ Default settings inserted')
    }

    // Step 3: Add sample data for testing
    console.log('\n📊 Step 3: Adding sample data...')

    // Add sample schemes if table was created
    if (missingTables.includes('schemes')) {
      const [customers] = await connection.execute('SELECT id FROM customers LIMIT 2')
      if ((customers as any[]).length > 0) {
        const customerId = (customers as any[])[0].id
        
        await connection.execute(`
          INSERT INTO schemes (
            id, scheme_name, customer_id, total_amount, paid_amount, 
            monthly_amount, duration, start_date, status
          ) VALUES 
          (UUID(), 'Gold Savings Scheme', ?, 120000.00, 24000.00, 10000.00, 12, CURDATE(), 'active'),
          (UUID(), 'Wedding Collection', ?, 200000.00, 50000.00, 12500.00, 16, CURDATE(), 'active')
        `, [customerId, customerId])
        
        console.log('   ✅ Sample schemes added')
      }
    }

    // Add sample repairs if table was created
    if (missingTables.includes('repairs')) {
      const [customers] = await connection.execute('SELECT id, first_name, last_name FROM customers LIMIT 2')
      if ((customers as any[]).length > 0) {
        const customer = (customers as any[])[0]
        const customerName = `${customer.first_name} ${customer.last_name}`.trim()
        
        await connection.execute(`
          INSERT INTO repairs (
            id, repair_number, customer_id, customer_name, item_description,
            repair_type, estimated_cost, advance_amount, received_date, 
            promised_date, status
          ) VALUES 
          (UUID(), 'REP/2025/001', ?, ?, 'Gold chain link repair', 'chain_repair', 1500.00, 500.00, CURDATE(), DATE_ADD(CURDATE(), INTERVAL 3 DAY), 'received'),
          (UUID(), 'REP/2025/002', ?, ?, 'Ring resizing', 'resizing', 800.00, 300.00, CURDATE(), DATE_ADD(CURDATE(), INTERVAL 2 DAY), 'in_progress')
        `, [customer.id, customerName, customer.id, customerName])
        
        console.log('   ✅ Sample repairs added')
      }
    }

    // Step 4: Test all APIs
    console.log('\n🧪 Step 4: Testing all APIs after fixes...')
    
    const apiTests = [
      { name: 'schemes', url: 'http://localhost:3000/api/schemes' },
      { name: 'repairs', url: 'http://localhost:3000/api/repairs' },
      { name: 'users', url: 'http://localhost:3000/api/users' },
      { name: 'settings', url: 'http://localhost:3000/api/settings' }
    ]

    for (const test of apiTests) {
      try {
        const response = await fetch(test.url)
        if (response.ok) {
          const data = await response.json()
          const items = data[test.name] || data.settings ? [data.settings] : []
          console.log(`   ✅ ${test.name}: ${response.status} - ${Array.isArray(items) ? items.length : 1} items`)
        } else {
          const errorText = await response.text()
          console.log(`   ❌ ${test.name}: ${response.status} - ${errorText}`)
        }
      } catch (error) {
        console.log(`   ❌ ${test.name}: Network Error - ${error}`)
      }
    }

    console.log('\n📋 Step 5: Final system status...')
    
    // Get final counts
    const finalCounts: Record<string, number> = {}
    const allTables = ['users', 'customers', 'inventory', 'sales', 'purchases', 'schemes', 'repairs', 'categories']
    
    for (const table of allTables) {
      try {
        const [result] = await connection.execute(`SELECT COUNT(*) as count FROM ${table}`)
        finalCounts[table] = (result as any[])[0]?.count || 0
      } catch (error) {
        finalCounts[table] = -1
      }
    }

    console.log('   📊 Final database status:')
    Object.entries(finalCounts).forEach(([table, count]) => {
      const status = count === -1 ? '❌ ERROR' : count === 0 ? '⚠️  EMPTY' : '✅ OK'
      console.log(`      ${status} ${table}: ${count === -1 ? 'Not accessible' : `${count} records`}`)
    })

    // Check settings separately
    try {
      const [settingsResult] = await connection.execute('SELECT COUNT(*) as count FROM settings')
      const settingsCount = (settingsResult as any[])[0]?.count || 0
      console.log(`      ✅ settings: ${settingsCount} records`)
    } catch (error) {
      console.log(`      ❌ settings: Not accessible`)
    }

    console.log('\n🎉 Missing Tables Fix Completed!')

  } catch (error) {
    console.error('\n❌ Missing tables fix failed:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('\n📡 Database connection closed')
    }
  }
}

// Run the missing tables fix
fixMissingTables().catch(console.error)
