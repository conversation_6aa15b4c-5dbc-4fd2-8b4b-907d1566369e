"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { useStore } from "@/lib/store"
import { toast } from "sonner"
import { 
  Settings, Building, Users, Shield, Bell, Database, 
  Printer, Globe, CreditCard, FileText, Save, RotateCcw,
  Key, Lock, Eye, EyeOff, Download, Upload, Trash2
} from "lucide-react"

export function ComprehensiveSettings() {
  const { settings, updateSettings, getSettings } = useStore()
  const [activeTab, setActiveTab] = useState("business")
  const [localSettings, setLocalSettings] = useState(settings)
  const [isLoading, setIsLoading] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)

  // Business Settings State
  const [businessSettings, setBusinessSettings] = useState({
    businessName: "",
    businessType: "jewelry_store",
    address: "",
    city: "",
    state: "",
    pincode: "",
    country: "India",
    phone: "",
    email: "",
    website: "",
    gstNumber: "",
    panNumber: "",
    licenseNumber: "",
    establishedYear: "",
    businessHours: {
      monday: { open: "09:00", close: "19:00", closed: false },
      tuesday: { open: "09:00", close: "19:00", closed: false },
      wednesday: { open: "09:00", close: "19:00", closed: false },
      thursday: { open: "09:00", close: "19:00", closed: false },
      friday: { open: "09:00", close: "19:00", closed: false },
      saturday: { open: "09:00", close: "19:00", closed: false },
      sunday: { open: "10:00", close: "18:00", closed: false }
    }
  })

  // Tax & Financial Settings
  const [taxSettings, setTaxSettings] = useState({
    cgstRate: "1.5",
    sgstRate: "1.5",
    igstRate: "3.0",
    tcsRate: "1.0",
    tdsRate: "1.0",
    hsnCodes: {
      gold: "71131900",
      silver: "71131100",
      diamond: "71023100",
      platinum: "71110000"
    },
    taxInclusive: false,
    roundingMethod: "nearest",
    currencySymbol: "₹",
    currencyCode: "INR",
    decimalPlaces: 2
  })

  // Metal Rates Settings
  const [metalRatesSettings, setMetalRatesSettings] = useState({
    autoUpdateRates: true,
    updateFrequency: "hourly",
    rateSource: "manual",
    goldRates: {
      "24K": "7200",
      "22K": "6600",
      "18K": "5400",
      "14K": "4200"
    },
    silverRates: {
      "999": "85",
      "925": "78"
    },
    platinumRates: {
      "950": "3200"
    },
    marginSettings: {
      goldMargin: "5",
      silverMargin: "8",
      platinumMargin: "3"
    }
  })

  // Notification Settings
  const [notificationSettings, setNotificationSettings] = useState({
    lowStockAlert: true,
    lowStockThreshold: "5",
    schemeReminders: true,
    schemeReminderDays: "7",
    repairReminders: true,
    repairReminderDays: "1",
    paymentReminders: true,
    paymentReminderDays: "3",
    birthdayReminders: true,
    anniversaryReminders: true,
    emailNotifications: true,
    smsNotifications: false,
    whatsappNotifications: false,
    pushNotifications: true
  })

  // System Settings
  const [systemSettings, setSystemSettings] = useState({
    dateFormat: "DD/MM/YYYY",
    timeFormat: "24",
    timezone: "Asia/Kolkata",
    language: "en",
    theme: "light",
    autoBackup: true,
    backupFrequency: "daily",
    backupRetention: "30",
    sessionTimeout: "60",
    maxLoginAttempts: "5",
    passwordPolicy: {
      minLength: "8",
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: true
    }
  })

  // Print & Invoice Settings
  const [printSettings, setPrintSettings] = useState({
    invoiceTemplate: "standard",
    printLogo: true,
    logoSize: "medium",
    printTerms: true,
    termsText: "Thank you for your business!",
    printSignature: false,
    signatureText: "Authorized Signatory",
    paperSize: "A4",
    orientation: "portrait",
    margins: "normal",
    fontSize: "medium",
    showBarcode: true,
    showQRCode: false,
    watermark: false,
    watermarkText: "ORIGINAL"
  })

  useEffect(() => {
    if (settings) {
      setLocalSettings(settings)
      // Initialize all settings from the main settings object
      setBusinessSettings(prev => ({
        ...prev,
        businessName: settings.businessName || "",
        address: settings.address || "",
        phone: settings.phone || "",
        email: settings.email || "",
        gstNumber: settings.gstNumber || ""
      }))
      
      setTaxSettings(prev => ({
        ...prev,
        cgstRate: settings.cgstRate || "1.5",
        sgstRate: settings.sgstRate || "1.5"
      }))
      
      setMetalRatesSettings(prev => ({
        ...prev,
        autoUpdateRates: settings.autoUpdateRates || true,
        goldRates: settings.metalRates?.gold || prev.goldRates,
        silverRates: settings.metalRates?.silver || prev.silverRates
      }))
      
      setNotificationSettings(prev => ({
        ...prev,
        lowStockAlert: settings.lowStockAlert || true,
        lowStockThreshold: settings.lowStockThreshold || "5",
        schemeReminders: settings.schemeReminders || true,
        repairReminders: settings.repairReminders || true
      }))
      
      setSystemSettings(prev => ({
        ...prev,
        dateFormat: settings.dateFormat || "DD/MM/YYYY",
        backupFrequency: settings.backupFrequency || "daily"
      }))
      
      setPrintSettings(prev => ({
        ...prev,
        invoiceTemplate: settings.invoiceTemplate || "standard",
        printLogo: settings.printLogo || true,
        printTerms: settings.printTerms || true
      }))
    }
  }, [settings])

  const handleSave = async () => {
    setIsLoading(true)
    try {
      // Combine all settings into the main settings object
      const combinedSettings = {
        ...localSettings,
        businessName: businessSettings.businessName,
        address: businessSettings.address,
        phone: businessSettings.phone,
        email: businessSettings.email,
        gstNumber: businessSettings.gstNumber,
        cgstRate: taxSettings.cgstRate,
        sgstRate: taxSettings.sgstRate,
        metalRates: {
          gold: metalRatesSettings.goldRates,
          silver: metalRatesSettings.silverRates,
          platinum: metalRatesSettings.platinumRates
        },
        autoUpdateRates: metalRatesSettings.autoUpdateRates,
        lowStockAlert: notificationSettings.lowStockAlert,
        lowStockThreshold: notificationSettings.lowStockThreshold,
        schemeReminders: notificationSettings.schemeReminders,
        repairReminders: notificationSettings.repairReminders,
        dateFormat: systemSettings.dateFormat,
        backupFrequency: systemSettings.backupFrequency,
        invoiceTemplate: printSettings.invoiceTemplate,
        printLogo: printSettings.printLogo,
        printTerms: printSettings.printTerms
      }
      
      await updateSettings(combinedSettings)
      setHasChanges(false)
      toast.success("Settings saved successfully!")
    } catch (error) {
      toast.error("Failed to save settings")
      console.error("Settings save error:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleReset = () => {
    // Reset to default values
    setBusinessSettings({
      businessName: "Shree Jewellers",
      businessType: "jewelry_store",
      address: "123 Main Street, Mumbai",
      city: "Mumbai",
      state: "Maharashtra",
      pincode: "400001",
      country: "India",
      phone: "+91 98765 43210",
      email: "<EMAIL>",
      website: "",
      gstNumber: "27**********1Z5",
      panNumber: "",
      licenseNumber: "",
      establishedYear: "2020",
      businessHours: {
        monday: { open: "09:00", close: "19:00", closed: false },
        tuesday: { open: "09:00", close: "19:00", closed: false },
        wednesday: { open: "09:00", close: "19:00", closed: false },
        thursday: { open: "09:00", close: "19:00", closed: false },
        friday: { open: "09:00", close: "19:00", closed: false },
        saturday: { open: "09:00", close: "19:00", closed: false },
        sunday: { open: "10:00", close: "18:00", closed: false }
      }
    })
    setHasChanges(true)
    toast.success("Settings reset to defaults!")
  }

  const exportSettings = () => {
    const allSettings = {
      business: businessSettings,
      tax: taxSettings,
      metalRates: metalRatesSettings,
      notifications: notificationSettings,
      system: systemSettings,
      print: printSettings,
      exportedAt: new Date().toISOString()
    }
    
    const dataStr = JSON.stringify(allSettings, null, 2)
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)
    
    const exportFileDefaultName = `jewelry-settings-${new Date().toISOString().split('T')[0]}.json`
    
    const linkElement = document.createElement('a')
    linkElement.setAttribute('href', dataUri)
    linkElement.setAttribute('download', exportFileDefaultName)
    linkElement.click()
    
    toast.success("Settings exported successfully!")
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Settings & Configuration</h2>
          <p className="text-muted-foreground">Comprehensive business and system configuration</p>
        </div>
        <div className="flex items-center gap-2">
          {hasChanges && (
            <Badge variant="secondary" className="mr-2">
              Unsaved Changes
            </Badge>
          )}
          <Button variant="outline" onClick={exportSettings}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" onClick={handleReset}>
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset
          </Button>
          <Button onClick={handleSave} disabled={isLoading}>
            <Save className="h-4 w-4 mr-2" />
            {isLoading ? "Saving..." : "Save Changes"}
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="business" className="flex items-center gap-2">
            <Building className="h-4 w-4" />
            Business
          </TabsTrigger>
          <TabsTrigger value="financial" className="flex items-center gap-2">
            <CreditCard className="h-4 w-4" />
            Financial
          </TabsTrigger>
          <TabsTrigger value="rates" className="flex items-center gap-2">
            <Globe className="h-4 w-4" />
            Metal Rates
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            Notifications
          </TabsTrigger>
          <TabsTrigger value="system" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            System
          </TabsTrigger>
          <TabsTrigger value="print" className="flex items-center gap-2">
            <Printer className="h-4 w-4" />
            Print & Invoice
          </TabsTrigger>
        </TabsList>

        {/* Business Settings Tab */}
        <TabsContent value="business" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-5 w-5" />
                  Business Information
                </CardTitle>
                <CardDescription>Basic business details and contact information</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="businessName">Business Name *</Label>
                  <Input
                    id="businessName"
                    value={businessSettings.businessName}
                    onChange={(e) => {
                      setBusinessSettings({...businessSettings, businessName: e.target.value})
                      setHasChanges(true)
                    }}
                    placeholder="Shree Jewellers"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="businessType">Business Type</Label>
                  <Select
                    value={businessSettings.businessType}
                    onValueChange={(value) => {
                      setBusinessSettings({...businessSettings, businessType: value})
                      setHasChanges(true)
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="jewelry_store">Jewelry Store</SelectItem>
                      <SelectItem value="jewelry_manufacturer">Jewelry Manufacturer</SelectItem>
                      <SelectItem value="gold_dealer">Gold Dealer</SelectItem>
                      <SelectItem value="diamond_dealer">Diamond Dealer</SelectItem>
                      <SelectItem value="wholesale">Wholesale</SelectItem>
                      <SelectItem value="retail">Retail</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="address">Address *</Label>
                  <Textarea
                    id="address"
                    value={businessSettings.address}
                    onChange={(e) => {
                      setBusinessSettings({...businessSettings, address: e.target.value})
                      setHasChanges(true)
                    }}
                    placeholder="123 Main Street, Commercial Complex"
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="city">City</Label>
                    <Input
                      id="city"
                      value={businessSettings.city}
                      onChange={(e) => {
                        setBusinessSettings({...businessSettings, city: e.target.value})
                        setHasChanges(true)
                      }}
                      placeholder="Mumbai"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="state">State</Label>
                    <Input
                      id="state"
                      value={businessSettings.state}
                      onChange={(e) => {
                        setBusinessSettings({...businessSettings, state: e.target.value})
                        setHasChanges(true)
                      }}
                      placeholder="Maharashtra"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="pincode">PIN Code</Label>
                    <Input
                      id="pincode"
                      value={businessSettings.pincode}
                      onChange={(e) => {
                        setBusinessSettings({...businessSettings, pincode: e.target.value})
                        setHasChanges(true)
                      }}
                      placeholder="400001"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="country">Country</Label>
                    <Select
                      value={businessSettings.country}
                      onValueChange={(value) => {
                        setBusinessSettings({...businessSettings, country: value})
                        setHasChanges(true)
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="India">India</SelectItem>
                        <SelectItem value="UAE">UAE</SelectItem>
                        <SelectItem value="USA">USA</SelectItem>
                        <SelectItem value="UK">UK</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Contact & Legal Information</CardTitle>
                <CardDescription>Contact details and legal registration information</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number *</Label>
                  <Input
                    id="phone"
                    value={businessSettings.phone}
                    onChange={(e) => {
                      setBusinessSettings({...businessSettings, phone: e.target.value})
                      setHasChanges(true)
                    }}
                    placeholder="+91 98765 43210"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email Address *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={businessSettings.email}
                    onChange={(e) => {
                      setBusinessSettings({...businessSettings, email: e.target.value})
                      setHasChanges(true)
                    }}
                    placeholder="<EMAIL>"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="website">Website</Label>
                  <Input
                    id="website"
                    value={businessSettings.website}
                    onChange={(e) => {
                      setBusinessSettings({...businessSettings, website: e.target.value})
                      setHasChanges(true)
                    }}
                    placeholder="www.shreejewellers.com"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="gstNumber">GST Number *</Label>
                  <Input
                    id="gstNumber"
                    value={businessSettings.gstNumber}
                    onChange={(e) => {
                      setBusinessSettings({...businessSettings, gstNumber: e.target.value})
                      setHasChanges(true)
                    }}
                    placeholder="27**********1Z5"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="panNumber">PAN Number</Label>
                  <Input
                    id="panNumber"
                    value={businessSettings.panNumber}
                    onChange={(e) => {
                      setBusinessSettings({...businessSettings, panNumber: e.target.value})
                      setHasChanges(true)
                    }}
                    placeholder="**********"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="licenseNumber">License Number</Label>
                  <Input
                    id="licenseNumber"
                    value={businessSettings.licenseNumber}
                    onChange={(e) => {
                      setBusinessSettings({...businessSettings, licenseNumber: e.target.value})
                      setHasChanges(true)
                    }}
                    placeholder="JL/2024/001"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="establishedYear">Established Year</Label>
                  <Input
                    id="establishedYear"
                    value={businessSettings.establishedYear}
                    onChange={(e) => {
                      setBusinessSettings({...businessSettings, establishedYear: e.target.value})
                      setHasChanges(true)
                    }}
                    placeholder="2020"
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Business Hours</CardTitle>
              <CardDescription>Configure your business operating hours</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(businessSettings.businessHours).map(([day, hours]) => (
                  <div key={day} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-4">
                      <div className="w-20">
                        <Label className="capitalize font-medium">{day}</Label>
                      </div>
                      <Switch
                        checked={!hours.closed}
                        onCheckedChange={(checked) => {
                          setBusinessSettings({
                            ...businessSettings,
                            businessHours: {
                              ...businessSettings.businessHours,
                              [day]: { ...hours, closed: !checked }
                            }
                          })
                          setHasChanges(true)
                        }}
                      />
                    </div>
                    {!hours.closed && (
                      <div className="flex items-center gap-2">
                        <Input
                          type="time"
                          value={hours.open}
                          onChange={(e) => {
                            setBusinessSettings({
                              ...businessSettings,
                              businessHours: {
                                ...businessSettings.businessHours,
                                [day]: { ...hours, open: e.target.value }
                              }
                            })
                            setHasChanges(true)
                          }}
                          className="w-24"
                        />
                        <span className="text-muted-foreground">to</span>
                        <Input
                          type="time"
                          value={hours.close}
                          onChange={(e) => {
                            setBusinessSettings({
                              ...businessSettings,
                              businessHours: {
                                ...businessSettings.businessHours,
                                [day]: { ...hours, close: e.target.value }
                              }
                            })
                            setHasChanges(true)
                          }}
                          className="w-24"
                        />
                      </div>
                    )}
                    {hours.closed && (
                      <Badge variant="secondary">Closed</Badge>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
