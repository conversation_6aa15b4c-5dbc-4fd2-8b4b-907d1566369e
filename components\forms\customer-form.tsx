"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { useStore } from "@/lib/store"
import type { Customer } from "@/lib/types"
import { generateCustomerIdPreview } from "@/lib/utils/client-id-generator"
import {
  User, Phone, Mail, MapPin, CreditCard, Heart,
  Calendar as CalendarIcon, Users, Shield, Star,
  Gift, MessageCircle, Settings, FileText
} from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"

interface CustomerFormProps {
  customer?: Customer
  onSubmit: () => void
  onCancel: () => void
}

export function CustomerForm({ customer, onSubmit, onCancel }: CustomerFormProps) {
  const { addCustomer, updateCustomer } = useStore()
  const [previewCustomerCode, setPreviewCustomerCode] = useState('')
  const [formData, setFormData] = useState({
    // Basic Information
    customerType: "individual",
    title: "",
    firstName: "",
    lastName: "",
    businessName: "",
    phone: "",
    alternatePhone: "",
    email: "",

    // Personal Details
    dateOfBirth: undefined as Date | undefined,
    anniversaryDate: undefined as Date | undefined,
    gender: "",

    // Address Information
    addressLine1: "",
    addressLine2: "",
    city: "",
    state: "",
    postalCode: "",
    country: "India",

    // KYC & Compliance
    gstNumber: "",
    panNumber: "",
    aadharNumber: "",

    // Business Terms
    creditLimit: "",
    creditDays: "",
    customerCategory: "regular",

    // Preferences & Communication
    preferredLanguage: "english",
    preferredContact: "phone",
    communicationPreferences: [] as string[],

    // Special Occasions & Preferences
    specialOccasions: [] as Array<{occasion: string, date: string}>,
    jewelryPreferences: [] as string[],
    metalPreferences: [] as string[],
    budgetRange: "",

    // Loyalty & Relationship
    loyaltyTier: "bronze",
    referredBy: "",
    notes: "",

    // Marketing & Communication
    marketingConsent: false,
    whatsappConsent: false,
    emailConsent: false,
    smsConsent: false,
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [activeTab, setActiveTab] = useState("basic")
  const [newOccasion, setNewOccasion] = useState({ occasion: "", date: "" })

  // Generate preview customer code for new customers
  useEffect(() => {
    if (!customer) {
      // Show preview format for new customers
      setPreviewCustomerCode(generateCustomerIdPreview())
    }
  }, [customer])

  useEffect(() => {
    if (customer) {
      setFormData({
        // Basic Information
        customerType: customer.customer_type || "individual",
        title: customer.title || "",
        firstName: customer.first_name || customer.name?.split(' ')[0] || "",
        lastName: customer.last_name || customer.name?.split(' ').slice(1).join(' ') || "",
        businessName: customer.business_name || "",
        phone: customer.phone || "",
        alternatePhone: customer.alternate_phone || "",
        email: customer.email || "",

        // Personal Details
        dateOfBirth: customer.date_of_birth ? new Date(customer.date_of_birth) : undefined,
        anniversaryDate: customer.anniversary_date ? new Date(customer.anniversary_date) : undefined,
        gender: customer.gender || "",

        // Address Information
        addressLine1: customer.address_line1 || customer.address || "",
        addressLine2: customer.address_line2 || "",
        city: customer.city || "",
        state: customer.state || "",
        postalCode: customer.postal_code || "",
        country: customer.country || "India",

        // KYC & Compliance
        gstNumber: customer.gst_number || customer.gstNumber || "",
        panNumber: customer.pan_number || "",
        aadharNumber: customer.aadhar_number || "",

        // Business Terms
        creditLimit: customer.credit_limit?.toString() || "",
        creditDays: customer.credit_days?.toString() || "",
        customerCategory: customer.customer_category || "regular",

        // Preferences & Communication
        preferredLanguage: customer.preferred_language || "english",
        preferredContact: customer.preferred_contact || "phone",
        communicationPreferences: customer.communication_preferences || [],

        // Special Occasions & Preferences
        specialOccasions: customer.special_occasions || [],
        jewelryPreferences: customer.jewelry_preferences || [],
        metalPreferences: customer.metal_preferences || [],
        budgetRange: customer.budget_range || "",

        // Loyalty & Relationship
        loyaltyTier: customer.loyalty_tier || "bronze",
        referredBy: customer.referred_by || "",
        notes: customer.notes || "",

        // Marketing & Communication
        marketingConsent: customer.marketing_consent || false,
        whatsappConsent: customer.whatsapp_consent || false,
        emailConsent: customer.email_consent || false,
        smsConsent: customer.sms_consent || false,
      })
    }
  }, [customer])

  // Helper functions
  const handleInputChange = (field: string, value: string | boolean | string[] | Date | undefined) => {
    setFormData({ ...formData, [field]: value })
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors({ ...errors, [field]: "" })
    }
  }

  const addSpecialOccasion = () => {
    if (newOccasion.occasion && newOccasion.date) {
      handleInputChange("specialOccasions", [...formData.specialOccasions, { ...newOccasion }])
      setNewOccasion({ occasion: "", date: "" })
    }
  }

  const removeSpecialOccasion = (index: number) => {
    const updated = formData.specialOccasions.filter((_, i) => i !== index)
    handleInputChange("specialOccasions", updated)
  }

  const togglePreference = (field: "communicationPreferences" | "jewelryPreferences" | "metalPreferences", value: string) => {
    const current = formData[field]
    const updated = current.includes(value)
      ? current.filter(item => item !== value)
      : [...current, value]
    handleInputChange(field, updated)
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    // Basic Information Validation
    if (formData.customerType === "individual") {
      if (!formData.firstName.trim()) {
        newErrors.firstName = "First name is required"
      }
    } else {
      if (!formData.businessName.trim()) {
        newErrors.businessName = "Business name is required"
      }
    }

    if (!formData.phone.trim()) {
      newErrors.phone = "Phone number is required"
    } else if (!/^[+]?[0-9\s-()]{10,15}$/.test(formData.phone.replace(/\s/g, ''))) {
      newErrors.phone = "Please enter a valid phone number"
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address"
    }

    if (!formData.addressLine1.trim()) {
      newErrors.addressLine1 = "Address is required"
    }

    // KYC Validation
    if (formData.gstNumber && !/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/.test(formData.gstNumber)) {
      newErrors.gstNumber = "Please enter a valid GST number (e.g., 27**********1Z5)"
    }

    if (formData.panNumber && !/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/.test(formData.panNumber)) {
      newErrors.panNumber = "Please enter a valid PAN number (e.g., **********)"
    }

    if (formData.aadharNumber && !/^[0-9]{12}$/.test(formData.aadharNumber.replace(/\s/g, ''))) {
      newErrors.aadharNumber = "Please enter a valid Aadhar number (12 digits)"
    }

    // Business Terms Validation
    if (formData.creditLimit && Number.parseFloat(formData.creditLimit) < 0) {
      newErrors.creditLimit = "Credit limit cannot be negative"
    }

    if (formData.creditDays && Number.parseInt(formData.creditDays) < 0) {
      newErrors.creditDays = "Credit days cannot be negative"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)

    try {
      const customerData = {
        // Basic Information
        customer_type: formData.customerType,
        title: formData.title || null,
        first_name: formData.firstName.trim(),
        last_name: formData.lastName.trim() || null,
        business_name: formData.businessName.trim() || null,
        phone: formData.phone.trim(),
        alternate_phone: formData.alternatePhone.trim() || null,
        email: formData.email.trim() || null,

        // Personal Details
        date_of_birth: formData.dateOfBirth ? formData.dateOfBirth.toISOString().split('T')[0] : null,
        anniversary_date: formData.anniversaryDate ? formData.anniversaryDate.toISOString().split('T')[0] : null,
        gender: formData.gender || null,

        // Address Information
        address_line1: formData.addressLine1.trim(),
        address_line2: formData.addressLine2.trim() || null,
        city: formData.city.trim() || null,
        state: formData.state.trim() || null,
        postal_code: formData.postalCode.trim() || null,
        country: formData.country,

        // KYC & Compliance
        gst_number: formData.gstNumber.trim() || null,
        pan_number: formData.panNumber.trim() || null,
        aadhar_number: formData.aadharNumber.trim() || null,

        // Business Terms
        credit_limit: Number.parseFloat(formData.creditLimit) || 0,
        credit_days: Number.parseInt(formData.creditDays) || 0,
        customer_category: formData.customerCategory,

        // Preferences & Communication
        preferred_language: formData.preferredLanguage,
        preferred_contact: formData.preferredContact,
        communication_preferences: formData.communicationPreferences,

        // Special Occasions & Preferences
        special_occasions: formData.specialOccasions,
        jewelry_preferences: formData.jewelryPreferences,
        metal_preferences: formData.metalPreferences,
        budget_range: formData.budgetRange || null,

        // Loyalty & Relationship
        loyalty_tier: formData.loyaltyTier,
        referred_by: formData.referredBy.trim() || null,
        notes: formData.notes.trim() || null,

        // Marketing & Communication
        marketing_consent: formData.marketingConsent,
        whatsapp_consent: formData.whatsappConsent,
        email_consent: formData.emailConsent,
        sms_consent: formData.smsConsent,

        // Legacy fields for backward compatibility
        name: `${formData.firstName} ${formData.lastName}`.trim(),
        address: formData.addressLine1,
        gstNumber: formData.gstNumber,
        totalPurchases: customer?.totalPurchases || customer?.total_purchases || 0,
        lastVisit: customer?.lastVisit || customer?.last_purchase_date || new Date().toISOString().split("T")[0],
      }

      if (customer) {
        await updateCustomer(customer.id, customerData)
      } else {
        await addCustomer(customerData)
      }

      onSubmit()
    } catch (error) {
      console.error("Error saving customer:", error)
      setErrors({ submit: "Failed to save customer. Please try again." })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Helper functions for options
  const getIndianStates = () => {
    return [
      "Andhra Pradesh", "Arunachal Pradesh", "Assam", "Bihar", "Chhattisgarh", "Goa", "Gujarat",
      "Haryana", "Himachal Pradesh", "Jharkhand", "Karnataka", "Kerala", "Madhya Pradesh",
      "Maharashtra", "Manipur", "Meghalaya", "Mizoram", "Nagaland", "Odisha", "Punjab",
      "Rajasthan", "Sikkim", "Tamil Nadu", "Telangana", "Tripura", "Uttar Pradesh",
      "Uttarakhand", "West Bengal", "Delhi", "Jammu and Kashmir", "Ladakh", "Puducherry"
    ]
  }

  const getJewelryPreferences = () => {
    return [
      "Traditional", "Modern", "Antique", "Temple Jewelry", "Kundan", "Meenakari",
      "Polki", "Diamond", "Gold", "Silver", "Platinum", "Gemstone", "Pearl",
      "Bridal Sets", "Daily Wear", "Party Wear", "Festival Jewelry", "Religious Items"
    ]
  }

  const getMetalPreferences = () => {
    return ["Gold", "Silver", "Platinum", "Rose Gold", "White Gold", "Mixed Metals"]
  }

  const getBudgetRanges = () => {
    return [
      "Under ₹25,000", "₹25,000 - ₹50,000", "₹50,000 - ₹1,00,000",
      "₹1,00,000 - ₹2,50,000", "₹2,50,000 - ₹5,00,000", "Above ₹5,00,000"
    ]
  }

  const getSpecialOccasions = () => {
    return [
      "Birthday", "Anniversary", "Wedding", "Engagement", "Diwali", "Dhanteras",
      "Akshaya Tritiya", "Karva Chauth", "Raksha Bandhan", "Navratri", "Dussehra",
      "Christmas", "New Year", "Valentine's Day", "Mother's Day", "Father's Day"
    ]
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="basic" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            Basic
          </TabsTrigger>
          <TabsTrigger value="personal" className="flex items-center gap-2">
            <Heart className="h-4 w-4" />
            Personal
          </TabsTrigger>
          <TabsTrigger value="kyc" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            KYC
          </TabsTrigger>
          <TabsTrigger value="preferences" className="flex items-center gap-2">
            <Star className="h-4 w-4" />
            Preferences
          </TabsTrigger>
          <TabsTrigger value="relationship" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Relationship
          </TabsTrigger>
        </TabsList>

        {/* Basic Information Tab */}
        <TabsContent value="basic" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Basic Information
              </CardTitle>
              <CardDescription>
                Essential customer details and contact information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Customer Type */}
              <div className="space-y-2">
                <Label>Customer Type</Label>
                <Select value={formData.customerType} onValueChange={(value) => handleInputChange("customerType", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select customer type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="individual">Individual</SelectItem>
                    <SelectItem value="business">Business</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {formData.customerType === "individual" ? (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="title">Title</Label>
                    <Select value={formData.title} onValueChange={(value) => handleInputChange("title", value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select title" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Mr">Mr.</SelectItem>
                        <SelectItem value="Mrs">Mrs.</SelectItem>
                        <SelectItem value="Ms">Ms.</SelectItem>
                        <SelectItem value="Dr">Dr.</SelectItem>
                        <SelectItem value="Prof">Prof.</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="firstName">First Name *</Label>
                    <Input
                      id="firstName"
                      value={formData.firstName}
                      onChange={(e) => handleInputChange("firstName", e.target.value)}
                      placeholder="Enter first name"
                      required
                      className={errors.firstName ? "border-red-500" : ""}
                    />
                    {errors.firstName && <p className="text-sm text-red-500">{errors.firstName}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="lastName">Last Name</Label>
                    <Input
                      id="lastName"
                      value={formData.lastName}
                      onChange={(e) => handleInputChange("lastName", e.target.value)}
                      placeholder="Enter last name"
                    />
                  </div>
                </div>
              ) : (
                <div className="space-y-2">
                  <Label htmlFor="businessName">Business Name *</Label>
                  <Input
                    id="businessName"
                    value={formData.businessName}
                    onChange={(e) => handleInputChange("businessName", e.target.value)}
                    placeholder="Enter business name"
                    required
                    className={errors.businessName ? "border-red-500" : ""}
                  />
                  {errors.businessName && <p className="text-sm text-red-500">{errors.businessName}</p>}
                </div>
              )}

              {/* Contact Information */}
              <Separator />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number *</Label>
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => handleInputChange("phone", e.target.value)}
                    placeholder="+91 98765 43210"
                    required
                    className={errors.phone ? "border-red-500" : ""}
                  />
                  {errors.phone && <p className="text-sm text-red-500">{errors.phone}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="alternatePhone">Alternate Phone</Label>
                  <Input
                    id="alternatePhone"
                    value={formData.alternatePhone}
                    onChange={(e) => handleInputChange("alternatePhone", e.target.value)}
                    placeholder="+91 98765 43211"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  placeholder="<EMAIL>"
                  className={errors.email ? "border-red-500" : ""}
                />
                {errors.email && <p className="text-sm text-red-500">{errors.email}</p>}
              </div>

              {/* Address Information */}
              <Separator />
              <div className="space-y-4">
                <h4 className="font-medium">Address Information</h4>

                <div className="space-y-2">
                  <Label htmlFor="addressLine1">Address Line 1 *</Label>
                  <Input
                    id="addressLine1"
                    value={formData.addressLine1}
                    onChange={(e) => handleInputChange("addressLine1", e.target.value)}
                    placeholder="Street address, building name"
                    required
                    className={errors.addressLine1 ? "border-red-500" : ""}
                  />
                  {errors.addressLine1 && <p className="text-sm text-red-500">{errors.addressLine1}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="addressLine2">Address Line 2</Label>
                  <Input
                    id="addressLine2"
                    value={formData.addressLine2}
                    onChange={(e) => handleInputChange("addressLine2", e.target.value)}
                    placeholder="Apartment, suite, unit, etc."
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="city">City</Label>
                    <Input
                      id="city"
                      value={formData.city}
                      onChange={(e) => handleInputChange("city", e.target.value)}
                      placeholder="Enter city"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="state">State</Label>
                    <Select value={formData.state} onValueChange={(value) => handleInputChange("state", value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select state" />
                      </SelectTrigger>
                      <SelectContent>
                        {getIndianStates().map((state) => (
                          <SelectItem key={state} value={state}>
                            {state}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="postalCode">Postal Code</Label>
                    <Input
                      id="postalCode"
                      value={formData.postalCode}
                      onChange={(e) => handleInputChange("postalCode", e.target.value)}
                      placeholder="400001"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Personal Details Tab */}
        <TabsContent value="personal" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Heart className="h-5 w-5" />
                Personal Details
              </CardTitle>
              <CardDescription>
                Personal information for better customer service and relationship management
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Date of Birth</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !formData.dateOfBirth && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.dateOfBirth ? format(formData.dateOfBirth, "PPP") : "Pick a date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={formData.dateOfBirth}
                        onSelect={(date) => handleInputChange("dateOfBirth", date)}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="space-y-2">
                  <Label>Anniversary Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !formData.anniversaryDate && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.anniversaryDate ? format(formData.anniversaryDate, "PPP") : "Pick a date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={formData.anniversaryDate}
                        onSelect={(date) => handleInputChange("anniversaryDate", date)}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Gender</Label>
                <Select value={formData.gender} onValueChange={(value) => handleInputChange("gender", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select gender" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="male">Male</SelectItem>
                    <SelectItem value="female">Female</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Special Occasions */}
              <Separator />
              <div className="space-y-4">
                <h4 className="font-medium">Special Occasions</h4>

                <div className="flex flex-wrap gap-2 mb-2">
                  {formData.specialOccasions.map((occasion, index) => (
                    <Badge key={index} variant="secondary" className="cursor-pointer" onClick={() => removeSpecialOccasion(index)}>
                      {occasion.occasion} - {occasion.date} ×
                    </Badge>
                  ))}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                  <Select value={newOccasion.occasion} onValueChange={(value) => setNewOccasion({...newOccasion, occasion: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select occasion" />
                    </SelectTrigger>
                    <SelectContent>
                      {getSpecialOccasions().map((occasion) => (
                        <SelectItem key={occasion} value={occasion}>
                          {occasion}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <Input
                    type="date"
                    value={newOccasion.date}
                    onChange={(e) => setNewOccasion({...newOccasion, date: e.target.value})}
                    placeholder="Select date"
                  />

                  <Button type="button" onClick={addSpecialOccasion} variant="outline">
                    <Gift className="h-4 w-4 mr-2" />
                    Add Occasion
                  </Button>
                </div>
              </div>

              {/* Communication Preferences */}
              <Separator />
              <div className="space-y-4">
                <h4 className="font-medium">Communication Preferences</h4>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Preferred Language</Label>
                    <Select value={formData.preferredLanguage} onValueChange={(value) => handleInputChange("preferredLanguage", value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select language" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="english">English</SelectItem>
                        <SelectItem value="hindi">Hindi</SelectItem>
                        <SelectItem value="tamil">Tamil</SelectItem>
                        <SelectItem value="telugu">Telugu</SelectItem>
                        <SelectItem value="kannada">Kannada</SelectItem>
                        <SelectItem value="malayalam">Malayalam</SelectItem>
                        <SelectItem value="marathi">Marathi</SelectItem>
                        <SelectItem value="gujarati">Gujarati</SelectItem>
                        <SelectItem value="bengali">Bengali</SelectItem>
                        <SelectItem value="punjabi">Punjabi</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Preferred Contact Method</Label>
                    <Select value={formData.preferredContact} onValueChange={(value) => handleInputChange("preferredContact", value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select contact method" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="phone">Phone Call</SelectItem>
                        <SelectItem value="whatsapp">WhatsApp</SelectItem>
                        <SelectItem value="email">Email</SelectItem>
                        <SelectItem value="sms">SMS</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Communication Preferences</Label>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                    {["New Arrivals", "Offers & Discounts", "Festival Collections", "Birthday Wishes", "Anniversary Reminders", "Scheme Updates"].map((pref) => (
                      <div key={pref} className="flex items-center space-x-2">
                        <Checkbox
                          id={pref}
                          checked={formData.communicationPreferences.includes(pref)}
                          onCheckedChange={() => togglePreference("communicationPreferences", pref)}
                        />
                        <Label htmlFor={pref} className="text-sm">{pref}</Label>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* KYC Tab */}
        <TabsContent value="kyc" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                KYC & Compliance
              </CardTitle>
              <CardDescription>
                Know Your Customer details and business compliance information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="gstNumber">GST Number</Label>
                  <Input
                    id="gstNumber"
                    value={formData.gstNumber}
                    onChange={(e) => handleInputChange("gstNumber", e.target.value.toUpperCase())}
                    placeholder="27**********1Z5"
                    className={errors.gstNumber ? "border-red-500" : ""}
                  />
                  {errors.gstNumber && <p className="text-sm text-red-500">{errors.gstNumber}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="panNumber">PAN Number</Label>
                  <Input
                    id="panNumber"
                    value={formData.panNumber}
                    onChange={(e) => handleInputChange("panNumber", e.target.value.toUpperCase())}
                    placeholder="**********"
                    className={errors.panNumber ? "border-red-500" : ""}
                  />
                  {errors.panNumber && <p className="text-sm text-red-500">{errors.panNumber}</p>}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="aadharNumber">Aadhar Number</Label>
                <Input
                  id="aadharNumber"
                  value={formData.aadharNumber}
                  onChange={(e) => handleInputChange("aadharNumber", e.target.value)}
                  placeholder="1234 5678 9012"
                  className={errors.aadharNumber ? "border-red-500" : ""}
                />
                {errors.aadharNumber && <p className="text-sm text-red-500">{errors.aadharNumber}</p>}
              </div>

              <Separator />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="creditLimit">Credit Limit (₹)</Label>
                  <Input
                    id="creditLimit"
                    type="number"
                    min="0"
                    value={formData.creditLimit}
                    onChange={(e) => handleInputChange("creditLimit", e.target.value)}
                    placeholder="50000"
                    className={errors.creditLimit ? "border-red-500" : ""}
                  />
                  {errors.creditLimit && <p className="text-sm text-red-500">{errors.creditLimit}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="creditDays">Credit Days</Label>
                  <Input
                    id="creditDays"
                    type="number"
                    min="0"
                    value={formData.creditDays}
                    onChange={(e) => handleInputChange("creditDays", e.target.value)}
                    placeholder="30"
                    className={errors.creditDays ? "border-red-500" : ""}
                  />
                  {errors.creditDays && <p className="text-sm text-red-500">{errors.creditDays}</p>}
                </div>
              </div>

              <div className="space-y-2">
                <Label>Customer Category</Label>
                <Select value={formData.customerCategory} onValueChange={(value) => handleInputChange("customerCategory", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="regular">Regular</SelectItem>
                    <SelectItem value="premium">Premium</SelectItem>
                    <SelectItem value="vip">VIP</SelectItem>
                    <SelectItem value="wholesale">Wholesale</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Preferences Tab */}
        <TabsContent value="preferences" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="h-5 w-5" />
                Jewelry Preferences
              </CardTitle>
              <CardDescription>
                Customer preferences for personalized recommendations
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div>
                  <Label className="text-base font-medium">Jewelry Preferences</Label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2">
                    {getJewelryPreferences().map((pref) => (
                      <div key={pref} className="flex items-center space-x-2">
                        <Checkbox
                          id={`jewelry-${pref}`}
                          checked={formData.jewelryPreferences.includes(pref)}
                          onCheckedChange={() => togglePreference("jewelryPreferences", pref)}
                        />
                        <Label htmlFor={`jewelry-${pref}`} className="text-sm">{pref}</Label>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <Label className="text-base font-medium">Metal Preferences</Label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2">
                    {getMetalPreferences().map((pref) => (
                      <div key={pref} className="flex items-center space-x-2">
                        <Checkbox
                          id={`metal-${pref}`}
                          checked={formData.metalPreferences.includes(pref)}
                          onCheckedChange={() => togglePreference("metalPreferences", pref)}
                        />
                        <Label htmlFor={`metal-${pref}`} className="text-sm">{pref}</Label>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Budget Range</Label>
                  <Select value={formData.budgetRange} onValueChange={(value) => handleInputChange("budgetRange", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select budget range" />
                    </SelectTrigger>
                    <SelectContent>
                      {getBudgetRanges().map((range) => (
                        <SelectItem key={range} value={range}>
                          {range}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Relationship Tab */}
        <TabsContent value="relationship" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Relationship Management
              </CardTitle>
              <CardDescription>
                Loyalty, referrals, and customer relationship details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Loyalty Tier</Label>
                  <Select value={formData.loyaltyTier} onValueChange={(value) => handleInputChange("loyaltyTier", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select tier" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="bronze">Bronze</SelectItem>
                      <SelectItem value="silver">Silver</SelectItem>
                      <SelectItem value="gold">Gold</SelectItem>
                      <SelectItem value="platinum">Platinum</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="referredBy">Referred By</Label>
                  <Input
                    id="referredBy"
                    value={formData.referredBy}
                    onChange={(e) => handleInputChange("referredBy", e.target.value)}
                    placeholder="Customer name or reference"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => handleInputChange("notes", e.target.value)}
                  placeholder="Additional notes about the customer..."
                  rows={3}
                />
              </div>

              <Separator />
              <div className="space-y-4">
                <h4 className="font-medium">Marketing Consent</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="marketingConsent"
                      checked={formData.marketingConsent}
                      onCheckedChange={(checked) => handleInputChange("marketingConsent", checked)}
                    />
                    <Label htmlFor="marketingConsent">Marketing Communications</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="whatsappConsent"
                      checked={formData.whatsappConsent}
                      onCheckedChange={(checked) => handleInputChange("whatsappConsent", checked)}
                    />
                    <Label htmlFor="whatsappConsent">WhatsApp Messages</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="emailConsent"
                      checked={formData.emailConsent}
                      onCheckedChange={(checked) => handleInputChange("emailConsent", checked)}
                    />
                    <Label htmlFor="emailConsent">Email Newsletters</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="smsConsent"
                      checked={formData.smsConsent}
                      onCheckedChange={(checked) => handleInputChange("smsConsent", checked)}
                    />
                    <Label htmlFor="smsConsent">SMS Notifications</Label>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Customer Preview */}
      {(formData.firstName || formData.businessName) && (
        <Card className="bg-blue-50 border-blue-200">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg text-blue-900">Customer Preview</CardTitle>
            <CardDescription>Summary of customer information</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <div>
                  <p className="text-blue-700 font-medium">Customer Name</p>
                  <p className="text-blue-900 bg-white px-3 py-2 rounded border">
                    {formData.customerType === "individual"
                      ? `${formData.title} ${formData.firstName} ${formData.lastName}`.trim()
                      : formData.businessName
                    }
                  </p>
                </div>
                <div>
                  <p className="text-blue-700 font-medium">Contact</p>
                  <p className="text-blue-900 bg-white px-3 py-2 rounded border">
                    {formData.phone} {formData.email && `• ${formData.email}`}
                  </p>
                </div>
                <div>
                  <p className="text-blue-700 font-medium">Category</p>
                  <Badge variant="outline" className="bg-white">
                    {formData.customerCategory.charAt(0).toUpperCase() + formData.customerCategory.slice(1)}
                  </Badge>
                </div>
              </div>
              <div className="space-y-3">
                <div>
                  <p className="text-blue-700 font-medium">Loyalty Tier</p>
                  <Badge variant="secondary" className="bg-white">
                    {formData.loyaltyTier.charAt(0).toUpperCase() + formData.loyaltyTier.slice(1)}
                  </Badge>
                </div>
                <div>
                  <p className="text-blue-700 font-medium">Preferences</p>
                  <div className="flex flex-wrap gap-1">
                    {formData.jewelryPreferences.slice(0, 3).map((pref) => (
                      <Badge key={pref} variant="outline" className="text-xs bg-white">
                        {pref}
                      </Badge>
                    ))}
                    {formData.jewelryPreferences.length > 3 && (
                      <Badge variant="outline" className="text-xs bg-white">
                        +{formData.jewelryPreferences.length - 3} more
                      </Badge>
                    )}
                  </div>
                </div>
                <div>
                  <p className="text-blue-700 font-medium">Communication</p>
                  <p className="text-blue-900 bg-white px-3 py-2 rounded border text-sm">
                    {formData.preferredContact} • {formData.preferredLanguage}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Error Display */}
      {errors.submit && (
        <Card className="bg-red-50 border-red-200">
          <CardContent className="pt-6">
            <p className="text-sm text-red-600">{errors.submit}</p>
          </CardContent>
        </Card>
      )}

      {/* Form Controls */}
      <div className="flex justify-between items-center pt-6 border-t">
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <span>Tab {["basic", "personal", "kyc", "preferences", "relationship"].indexOf(activeTab) + 1} of 5</span>
        </div>

        <div className="flex gap-3">
          <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
            Cancel
          </Button>

          {activeTab !== "relationship" && (
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                const tabs = ["basic", "personal", "kyc", "preferences", "relationship"]
                const currentIndex = tabs.indexOf(activeTab)
                if (currentIndex < tabs.length - 1) {
                  setActiveTab(tabs[currentIndex + 1])
                }
              }}
            >
              Next Tab
            </Button>
          )}

          <Button type="submit" disabled={isSubmitting} className="min-w-[120px]">
            {isSubmitting ? "Saving..." : customer ? "Update Customer" : "Add Customer"}
          </Button>
        </div>
      </div>
    </form>
  )
}
