# 💎 Shree Jewellers Management System

A comprehensive, production-ready jewelry business management system built with Next.js, TypeScript, and modern web technologies.

## 🚀 System Overview

This is a complete jewelry business management solution designed specifically for Indian jewelry businesses, featuring:

- **Inventory Management** - Complete item tracking with Indian jewelry specifications
- **Customer Management** - CRM with KYC compliance and loyalty tracking
- **Sales Management** - GST-compliant sales processing with exchange handling
- **Repair Management** - Job tracking with quality control
- **Gold Scheme Management** - Complete scheme management with rate integration
- **Purchase Management** - Supplier management with procurement intelligence
- **Reports & Analytics** - Executive dashboard and business intelligence
- **Settings & Configuration** - Complete business configuration with user management

## 🏗️ Architecture

### Technology Stack
- **Frontend**: Next.js 14, React 18, TypeScript
- **UI Components**: Tailwind CSS, Radix UI, Lucide Icons
- **State Management**: Zustand
- **Database**: MySQL with connection pooling
- **Authentication**: JWT-based authentication
- **Notifications**: Sonner toast notifications

### Key Features
- **GST Compliance** - Automated GST calculations and invoice generation
- **KYC Compliance** - Customer verification and documentation
- **Multi-user Support** - Role-based access control
- **Real-time Analytics** - Business intelligence and reporting
- **Mobile Responsive** - Optimized for all device sizes
- **Production Ready** - Cleaned and optimized for deployment

## 📦 Installation

### Prerequisites
- Node.js 18+ 
- MySQL 8.0+
- npm or yarn

### Setup Steps

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd jewellers-software
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment variables**
   ```bash
   cp .env.example .env.local
   ```
   
   Update `.env.local` with your configuration:
   ```env
   # Database Configuration
   DB_HOST=localhost
   DB_USER=your_db_user
   DB_PASSWORD=your_db_password
   DB_NAME=jjjewellers_db
   
   # JWT Secret
   JWT_SECRET=your_jwt_secret_key
   
   # Application Settings
   NEXT_PUBLIC_APP_NAME=Shree Jewellers
   NEXT_PUBLIC_APP_URL=http://localhost:3000
   ```

4. **Setup database**
   ```bash
   # Create database and tables
   mysql -u root -p < lib/database/schema.sql
   
   # Insert initial data
   mysql -u root -p < lib/database/initial-data.sql
   ```

5. **Build and start**
   ```bash
   # Development mode
   npm run dev
   
   # Production build
   npm run build
   npm start
   ```

## 🔐 Default Login Credentials

**Admin Account:**
- Email: `<EMAIL>`
- Password: `admin123`

**Manager Account:**
- Email: `<EMAIL>`
- Password: `manager123`

**Staff Account:**
- Email: `<EMAIL>`
- Password: `staff123`

> ⚠️ **Important**: Change these default passwords immediately after first login!

## 📊 System Modules

### 1. Inventory Management
- Complete item tracking with weight, purity, and pricing
- HSN code management for GST compliance
- Stock level monitoring and alerts
- Barcode generation and scanning
- Multi-category support (Gold, Silver, Diamond, etc.)

### 2. Customer Management
- Complete customer profiles with KYC details
- Purchase history and loyalty tracking
- Special occasion reminders
- Customer segmentation and analytics

### 3. Sales Management
- GST-compliant invoice generation
- Exchange transaction handling
- Multiple payment method support
- Discount and scheme integration
- Real-time inventory updates

### 4. Repair Management
- Job card creation and tracking
- Technician assignment and management
- Cost estimation and billing
- Quality control checkpoints
- Customer communication

### 5. Gold Scheme Management
- Multiple scheme types (Monthly, Advance, etc.)
- Gold rate integration and calculations
- Payment tracking and reminders
- Maturity calculations and notifications

### 6. Purchase Management
- Supplier management and profiles
- Purchase order creation and tracking
- Invoice matching and payments
- Inventory integration

### 7. Reports & Analytics
- Executive dashboard with KPIs
- Financial reports (P&L, Balance Sheet, Cash Flow)
- Business intelligence and forecasting
- Customer and product analytics
- Export capabilities (PDF, Excel)

### 8. Settings & Configuration
- Business profile and legal information
- User management with role-based access
- System configuration and security
- Backup and restore functionality

## 🛡️ Security Features

- **Authentication**: JWT-based secure authentication
- **Authorization**: Role-based access control (RBAC)
- **Data Encryption**: Sensitive data encryption
- **Audit Logging**: Complete activity tracking
- **Session Management**: Secure session handling
- **Input Validation**: Comprehensive data validation

## 🔧 Configuration

### Business Settings
- Company information and legal details
- GST rates and tax configuration
- Metal rates and margin settings
- Operating hours and preferences

### User Roles
- **Super Admin**: Full system access
- **Manager**: Management operations
- **Sales Staff**: Sales and customer operations
- **Accountant**: Financial and reporting access

### System Settings
- Backup configuration
- Notification preferences
- Integration settings
- Security policies

## 📈 Performance

- **Optimized Build**: Production-ready with code splitting
- **Database Indexing**: Optimized queries and indexes
- **Caching**: Efficient data caching strategies
- **Responsive Design**: Fast loading on all devices

## 🚀 Deployment

### Production Deployment

1. **Build the application**
   ```bash
   npm run build
   ```

2. **Start production server**
   ```bash
   npm start
   ```

3. **Configure reverse proxy** (Nginx example)
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;
       
       location / {
           proxy_pass http://localhost:3000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
   }
   ```

### Environment Setup
- Ensure MySQL is properly configured
- Set up SSL certificates for HTTPS
- Configure backup schedules
- Set up monitoring and logging

## 📞 Support

For technical support or business inquiries:
- **Email**: <EMAIL>
- **Phone**: +91 98765 43210

## 📄 License

This software is proprietary and confidential. All rights reserved.

---

**© 2024 Shree Jewellers Management System. All rights reserved.**
