"use client"

import { useState, useMemo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { useStore } from "@/lib/store"
import { 
  Download, Filter, Coins, TrendingUp, TrendingDown, 
  Users, Calendar, Target, Award, BarChart3,
  PieChart, Activity, Clock, Gift, Bell
} from "lucide-react"

export function SchemeAnalytics() {
  const { schemes, customers } = useStore()
  const [activeTab, setActiveTab] = useState("overview")
  const [dateRange, setDateRange] = useState("month")
  const [schemeTypeFilter, setSchemeTypeFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [customerFilter, setCustomerFilter] = useState("all")

  // Enhanced scheme analytics
  const analytics = useMemo(() => {
    // Filter schemes based on selected criteria
    const filteredSchemes = schemes.filter((scheme) => {
      // Date filtering
      const schemeDate = new Date(scheme.startDate)
      const now = new Date()
      
      if (dateRange === "today") {
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
        if (schemeDate < today) return false
      } else if (dateRange === "week") {
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        if (schemeDate < weekAgo) return false
      } else if (dateRange === "month") {
        const monthAgo = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate())
        if (schemeDate < monthAgo) return false
      } else if (dateRange === "year") {
        const yearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate())
        if (schemeDate < yearAgo) return false
      }
      
      // Scheme type filtering
      if (schemeTypeFilter !== "all" && scheme.schemeType !== schemeTypeFilter) return false
      
      // Status filtering
      if (statusFilter !== "all" && scheme.status !== statusFilter) return false
      
      // Customer filtering
      if (customerFilter !== "all" && scheme.customer.id !== customerFilter) return false
      
      return true
    })

    // Basic metrics
    const totalSchemes = filteredSchemes.length
    const totalCommitment = filteredSchemes.reduce((sum, scheme) => sum + (scheme.totalAmount || (scheme.monthlyAmount * scheme.duration)), 0)
    const totalCollected = filteredSchemes.reduce((sum, scheme) => sum + (scheme.paidAmount || 0), 0)
    const avgSchemeValue = totalSchemes > 0 ? totalCommitment / totalSchemes : 0
    
    // Status distribution
    const statusDistribution = filteredSchemes.reduce((acc, scheme) => {
      const status = scheme.status || "active"
      acc[status] = (acc[status] || 0) + 1
      return acc
    }, {} as Record<string, number>)
    
    // Scheme type distribution
    const schemeTypeDistribution = filteredSchemes.reduce((acc, scheme) => {
      const type = scheme.schemeType || "monthly_gold"
      acc[type] = (acc[type] || 0) + 1
      return acc
    }, {} as Record<string, number>)
    
    // Customer enrollment analysis
    const customerEnrollment = filteredSchemes.reduce((acc, scheme) => {
      const customerId = scheme.customer.id
      if (!acc[customerId]) {
        acc[customerId] = {
          customer: scheme.customer,
          totalSchemes: 0,
          totalCommitment: 0,
          totalPaid: 0,
          activeSchemes: 0
        }
      }
      
      acc[customerId].totalSchemes += 1
      acc[customerId].totalCommitment += scheme.totalAmount || (scheme.monthlyAmount * scheme.duration)
      acc[customerId].totalPaid += scheme.paidAmount || 0
      
      if (scheme.status === "active") {
        acc[customerId].activeSchemes += 1
      }
      
      return acc
    }, {} as Record<string, any>)
    
    const topCustomers = Object.values(customerEnrollment)
      .sort((a: any, b: any) => b.totalCommitment - a.totalCommitment)
      .slice(0, 10)
    
    // Maturity analysis
    const maturingSchemes = filteredSchemes.filter(scheme => {
      if (!scheme.maturityDate) return false
      const maturityDate = new Date(scheme.maturityDate)
      const now = new Date()
      const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)
      return maturityDate <= thirtyDaysFromNow && maturityDate >= now
    }).length
    
    // Collection efficiency
    const collectionRate = totalCommitment > 0 ? (totalCollected / totalCommitment) * 100 : 0
    
    // Gold accumulation (for gold schemes)
    const goldSchemes = filteredSchemes.filter(scheme => scheme.schemeType?.includes("gold"))
    const totalGoldAccumulated = goldSchemes.reduce((sum, scheme) => {
      const goldRate = scheme.goldRate || 6500
      const paidAmount = scheme.paidAmount || 0
      return sum + (paidAmount / goldRate)
    }, 0)
    
    // Monthly performance
    const monthlyPerformance = filteredSchemes.reduce((acc, scheme) => {
      const month = new Date(scheme.startDate).toISOString().slice(0, 7) // YYYY-MM
      if (!acc[month]) {
        acc[month] = { schemes: 0, commitment: 0, collected: 0 }
      }
      acc[month].schemes += 1
      acc[month].commitment += scheme.totalAmount || (scheme.monthlyAmount * scheme.duration)
      acc[month].collected += scheme.paidAmount || 0
      return acc
    }, {} as Record<string, any>)
    
    // Due payments analysis
    const duePayments = filteredSchemes.filter(scheme => {
      if (scheme.status !== "active") return false
      // Simplified logic - in real implementation, would check payment schedule
      return (scheme.paidAmount || 0) < (scheme.totalAmount || (scheme.monthlyAmount * scheme.duration))
    }).length
    
    // Growth calculations
    const previousPeriodSchemes = totalSchemes * 0.85 // Mock 15% growth
    const schemeGrowth = previousPeriodSchemes > 0 ? ((totalSchemes - previousPeriodSchemes) / previousPeriodSchemes) * 100 : 0
    
    return {
      totalSchemes,
      totalCommitment,
      totalCollected,
      avgSchemeValue,
      statusDistribution,
      schemeTypeDistribution,
      topCustomers,
      maturingSchemes,
      collectionRate,
      totalGoldAccumulated,
      monthlyPerformance,
      duePayments,
      schemeGrowth,
      activeSchemes: statusDistribution.active || 0,
      completedSchemes: statusDistribution.completed || 0,
      cancelledSchemes: statusDistribution.cancelled || 0,
      customerRetentionRate: 78.5, // Mock data
      avgSchemeCompletion: 11.2, // Mock data - months
    }
  }, [schemes, dateRange, schemeTypeFilter, statusFilter, customerFilter])

  const exportReport = () => {
    const reportData = {
      summary: analytics,
      dateRange,
      filters: { schemeTypeFilter, statusFilter, customerFilter },
      generatedAt: new Date().toISOString()
    }
    
    const dataStr = JSON.stringify(reportData, null, 2)
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)
    
    const exportFileDefaultName = `scheme-analytics-${new Date().toISOString().split('T')[0]}.json`
    
    const linkElement = document.createElement('a')
    linkElement.setAttribute('href', dataUri)
    linkElement.setAttribute('download', exportFileDefaultName)
    linkElement.click()
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-2xl font-bold tracking-tight">Scheme Analytics</h3>
          <p className="text-muted-foreground">Comprehensive gold scheme and installment plan analytics</p>
        </div>
        <Button onClick={exportReport}>
          <Download className="h-4 w-4 mr-2" />
          Export Report
        </Button>
      </div>

      {/* Advanced Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Analytics Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="space-y-2">
              <Label>Date Range</Label>
              <Select value={dateRange} onValueChange={setDateRange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="week">This Week</SelectItem>
                  <SelectItem value="month">This Month</SelectItem>
                  <SelectItem value="year">This Year</SelectItem>
                  <SelectItem value="all">All Time</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label>Scheme Type</Label>
              <Select value={schemeTypeFilter} onValueChange={setSchemeTypeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="monthly_gold">Monthly Gold</SelectItem>
                  <SelectItem value="monthly_amount">Monthly Amount</SelectItem>
                  <SelectItem value="quarterly">Quarterly</SelectItem>
                  <SelectItem value="yearly">Yearly</SelectItem>
                  <SelectItem value="flexible">Flexible</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Status</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                  <SelectItem value="on_hold">On Hold</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Customer</Label>
              <Select value={customerFilter} onValueChange={setCustomerFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Customers" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Customers</SelectItem>
                  {customers.slice(0, 10).map((customer) => (
                    <SelectItem key={customer.id} value={customer.id}>
                      {customer.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>&nbsp;</Label>
              <Button 
                variant="outline" 
                onClick={() => {
                  setDateRange("month")
                  setSchemeTypeFilter("all")
                  setStatusFilter("all")
                  setCustomerFilter("all")
                }}
                className="w-full"
              >
                Reset Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="performance" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Performance
          </TabsTrigger>
          <TabsTrigger value="customers" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Customers
          </TabsTrigger>
          <TabsTrigger value="maturity" className="flex items-center gap-2">
            <Gift className="h-4 w-4" />
            Maturity
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Schemes</CardTitle>
                <Coins className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.totalSchemes}</div>
                <p className="text-xs text-muted-foreground">
                  {analytics.schemeGrowth > 0 ? "+" : ""}{analytics.schemeGrowth.toFixed(1)}% from last period
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Commitment</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">₹{analytics.totalCommitment.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  Avg: ₹{analytics.avgSchemeValue.toLocaleString()}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Amount Collected</CardTitle>
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">₹{analytics.totalCollected.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  {analytics.collectionRate.toFixed(1)}% collection rate
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Gold Accumulated</CardTitle>
                <Award className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">{analytics.totalGoldAccumulated.toFixed(1)}g</div>
                <p className="text-xs text-muted-foreground">
                  From gold schemes
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Status and Type Distribution */}
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Scheme Status Distribution</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {Object.entries(analytics.statusDistribution).map(([status, count]) => (
                  <div key={status}>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="capitalize">{status.replace('_', ' ')}</span>
                      <span>{count} schemes</span>
                    </div>
                    <Progress value={(count / analytics.totalSchemes) * 100} className="h-2" />
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Scheme Type Distribution</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {Object.entries(analytics.schemeTypeDistribution).map(([type, count]) => (
                  <div key={type}>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="capitalize">{type.replace('_', ' ')}</span>
                      <span>{count} schemes</span>
                    </div>
                    <Progress value={(count / analytics.totalSchemes) * 100} className="h-2" />
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Business Intelligence Cards */}
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Scheme Health
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Collection Rate</span>
                    <span>{analytics.collectionRate.toFixed(1)}%</span>
                  </div>
                  <Progress value={analytics.collectionRate} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Customer Retention</span>
                    <span>{analytics.customerRetentionRate}%</span>
                  </div>
                  <Progress value={analytics.customerRetentionRate} className="h-2" />
                </div>
                <div className="pt-2 border-t">
                  <p className="text-sm text-muted-foreground">
                    {analytics.duePayments} schemes have due payments
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {analytics.maturingSchemes} schemes maturing soon
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Timing Metrics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {analytics.avgSchemeCompletion}
                    </div>
                    <p className="text-sm text-muted-foreground">Avg Completion (Months)</p>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Active Schemes</span>
                      <span className="font-medium">{analytics.activeSchemes}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Completed Schemes</span>
                      <span className="font-medium">{analytics.completedSchemes}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Cancelled Schemes</span>
                      <span className="font-medium text-red-600">{analytics.cancelledSchemes}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bell className="h-5 w-5" />
                  Alerts & Notifications
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-2 bg-yellow-50 border border-yellow-200 rounded">
                    <span className="text-sm text-yellow-800">Due Payments</span>
                    <Badge variant="secondary">{analytics.duePayments}</Badge>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-green-50 border border-green-200 rounded">
                    <span className="text-sm text-green-800">Maturing Soon</span>
                    <Badge variant="secondary">{analytics.maturingSchemes}</Badge>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-blue-50 border border-blue-200 rounded">
                    <span className="text-sm text-blue-800">New Enrollments</span>
                    <Badge variant="secondary">
                      {Math.floor(analytics.totalSchemes * 0.15)}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Collection Performance</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Overall Collection Rate</span>
                    <span>{analytics.collectionRate.toFixed(1)}%</span>
                  </div>
                  <Progress value={analytics.collectionRate} className="h-2" />
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Total Commitment</span>
                    <span className="font-medium">₹{analytics.totalCommitment.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Amount Collected</span>
                    <span className="font-medium text-green-600">₹{analytics.totalCollected.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Outstanding Amount</span>
                    <span className="font-medium text-red-600">
                      ₹{(analytics.totalCommitment - analytics.totalCollected).toLocaleString()}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Gold Accumulation</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-yellow-600">
                      {analytics.totalGoldAccumulated.toFixed(1)}g
                    </div>
                    <p className="text-sm text-muted-foreground">Total Gold Accumulated</p>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Current Gold Value</span>
                      <span className="font-medium">
                        ₹{(analytics.totalGoldAccumulated * 6500).toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Gold Schemes</span>
                      <span className="font-medium">
                        {analytics.schemeTypeDistribution.monthly_gold || 0}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Customers Tab */}
        <TabsContent value="customers" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Top Scheme Customers</CardTitle>
              <CardDescription>Customers with highest scheme commitments</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Customer</TableHead>
                    <TableHead>Total Schemes</TableHead>
                    <TableHead>Total Commitment</TableHead>
                    <TableHead>Amount Paid</TableHead>
                    <TableHead>Active Schemes</TableHead>
                    <TableHead>Payment Rate</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {analytics.topCustomers.map((customer: any) => (
                    <TableRow key={customer.customer.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{customer.customer.name}</div>
                          <div className="text-sm text-muted-foreground">{customer.customer.phone}</div>
                        </div>
                      </TableCell>
                      <TableCell>{customer.totalSchemes}</TableCell>
                      <TableCell className="font-medium">
                        ₹{customer.totalCommitment.toLocaleString()}
                      </TableCell>
                      <TableCell className="font-medium text-green-600">
                        ₹{customer.totalPaid.toLocaleString()}
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{customer.activeSchemes}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Progress
                            value={(customer.totalPaid / customer.totalCommitment) * 100}
                            className="h-2 w-16"
                          />
                          <span className="text-sm">
                            {((customer.totalPaid / customer.totalCommitment) * 100).toFixed(1)}%
                          </span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Maturity Tab */}
        <TabsContent value="maturity" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Maturity Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded">
                    <div>
                      <p className="font-medium text-green-900">Maturing This Month</p>
                      <p className="text-sm text-green-700">Schemes ready for completion</p>
                    </div>
                    <div className="text-2xl font-bold text-green-600">
                      {analytics.maturingSchemes}
                    </div>
                  </div>

                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Completed Schemes</span>
                      <span className="font-medium">{analytics.completedSchemes}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Success Rate</span>
                      <span className="font-medium">
                        {analytics.totalSchemes > 0 ?
                          ((analytics.completedSchemes / analytics.totalSchemes) * 100).toFixed(1) : 0}%
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Bonus & Benefits</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">
                      ₹{(analytics.totalCollected * 0.05).toLocaleString()}
                    </div>
                    <p className="text-sm text-muted-foreground">Estimated Bonus Payout</p>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Average Bonus Rate</span>
                      <span className="font-medium">5.0%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Gold Rate Benefit</span>
                      <span className="font-medium text-yellow-600">
                        ₹{(analytics.totalGoldAccumulated * 500).toLocaleString()}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
