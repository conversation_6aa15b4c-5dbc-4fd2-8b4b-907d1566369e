#!/bin/bash

# JEW<PERSON>RY MANAGEMENT SYSTEM - DATABASE SETUP SCRIPT
# Version: 4.0.0
# Date: January 31, 2025

echo "============================================================================"
echo "JEWELRY MANAGEMENT SYSTEM - DATABASE SETUP"
echo "Version: 4.0.0"
echo "============================================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Database configuration
DB_NAME="jewellers_db"
DB_USER="root"
DB_HOST="localhost"
DB_PORT="3306"

echo -e "${BLUE}Setting up comprehensive jewelry management database...${NC}"
echo ""

# Check if MySQL is running
echo -e "${YELLOW}Checking MySQL service...${NC}"
if ! command -v mysql &> /dev/null; then
    echo -e "${RED}Error: MySQL is not installed or not in PATH${NC}"
    exit 1
fi

# Get MySQL password
echo -e "${YELLOW}Please enter MySQL root password:${NC}"
read -s DB_PASSWORD
echo ""

# Test MySQL connection
echo -e "${YELLOW}Testing MySQL connection...${NC}"
mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASSWORD -e "SELECT 1;" &> /dev/null
if [ $? -ne 0 ]; then
    echo -e "${RED}Error: Cannot connect to MySQL. Please check credentials.${NC}"
    exit 1
fi
echo -e "${GREEN}✓ MySQL connection successful${NC}"

# Warning about data loss
echo ""
echo -e "${RED}WARNING: This will completely reset the database!${NC}"
echo -e "${RED}All existing data in '$DB_NAME' will be lost.${NC}"
echo ""
echo -e "${YELLOW}Do you want to continue? (yes/no):${NC}"
read -r CONFIRM

if [ "$CONFIRM" != "yes" ]; then
    echo -e "${YELLOW}Database setup cancelled.${NC}"
    exit 0
fi

echo ""
echo -e "${BLUE}Starting database setup...${NC}"

# Step 1: Create database and schema
echo -e "${YELLOW}Step 1: Creating database and schema...${NC}"
mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASSWORD < updated-comprehensive-schema.sql
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Database schema created successfully${NC}"
else
    echo -e "${RED}✗ Error creating database schema${NC}"
    exit 1
fi

# Step 2: Insert sample data
echo -e "${YELLOW}Step 2: Inserting sample data...${NC}"
mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASSWORD < seed-sample-data.sql
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Sample data inserted successfully${NC}"
else
    echo -e "${RED}✗ Error inserting sample data${NC}"
    exit 1
fi

# Step 3: Verify setup
echo -e "${YELLOW}Step 3: Verifying database setup...${NC}"
TABLE_COUNT=$(mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASSWORD -D$DB_NAME -se "SELECT COUNT(*) FROM information_schema.TABLES WHERE TABLE_SCHEMA = '$DB_NAME';")
USER_COUNT=$(mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASSWORD -D$DB_NAME -se "SELECT COUNT(*) FROM users;")
CUSTOMER_COUNT=$(mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASSWORD -D$DB_NAME -se "SELECT COUNT(*) FROM customers;")
INVENTORY_COUNT=$(mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASSWORD -D$DB_NAME -se "SELECT COUNT(*) FROM inventory;")

echo -e "${GREEN}✓ Database verification completed${NC}"
echo ""

# Display setup summary
echo "============================================================================"
echo -e "${GREEN}DATABASE SETUP COMPLETED SUCCESSFULLY!${NC}"
echo "============================================================================"
echo ""
echo -e "${BLUE}Setup Summary:${NC}"
echo "• Database Name: $DB_NAME"
echo "• Total Tables: $TABLE_COUNT"
echo "• Sample Users: $USER_COUNT"
echo "• Sample Customers: $CUSTOMER_COUNT"
echo "• Sample Inventory: $INVENTORY_COUNT"
echo ""

echo -e "${BLUE}Default Login Credentials:${NC}"
echo "• Email: <EMAIL>"
echo "• Password: admin123"
echo ""
echo -e "${RED}⚠️  IMPORTANT: Change the default password immediately!${NC}"
echo ""

echo -e "${BLUE}Database Connection Details:${NC}"
echo "• Host: $DB_HOST"
echo "• Port: $DB_PORT"
echo "• Database: $DB_NAME"
echo "• User: $DB_USER"
echo ""

echo -e "${BLUE}Sample Data Included:${NC}"
echo "• 5 Users (different roles)"
echo "• 5 Customers (different types)"
echo "• 5 Inventory items (various categories)"
echo "• 3 Sales transactions"
echo "• 3 Gold schemes"
echo "• 3 Repair orders"
echo "• 3 Suppliers"
echo "• 3 Purchase orders"
echo "• 2 Exchange transactions"
echo ""

echo -e "${BLUE}Next Steps:${NC}"
echo "1. Update your application's database configuration"
echo "2. Start your application server"
echo "3. Access the system and change default credentials"
echo "4. Configure business settings as needed"
echo "5. Test all features with sample data"
echo ""

echo -e "${GREEN}Database setup completed! You can now start using the jewelry management system.${NC}"
echo "============================================================================"
