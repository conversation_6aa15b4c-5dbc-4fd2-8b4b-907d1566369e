"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"
import { useStore } from "@/lib/store"
import type { InventoryItem } from "@/lib/types"
import { Calculator, Camera, FileText, Gem, Scale, Tag, Zap } from "lucide-react"

// Client-side constants (no server imports)
const JEWELRY_CATEGORIES = {
  'Rings': 'RG', 'Necklaces': 'NK', 'Earrings': 'ER', 'Bracelets': 'BR',
  'Pendants': 'PD', 'Chains': 'CH', 'Bangles': 'BG', 'Sets': 'ST',
  'Diamonds': 'DM', 'Pearls': 'PR'
}

const METAL_CODES = {
  'gold': 'GD', 'silver': 'SL', 'platinum': 'PT', 'diamond': 'DM'
}

const PURITY_CODES = {
  '24K': '24', '22K': '22', '18K': '18', '14K': '14', '999': '999', '925': '925'
}

interface InventoryFormProps {
  item?: InventoryItem
  onSubmit: () => void
  onCancel: () => void
}

export function InventoryForm({ item, onSubmit, onCancel }: InventoryFormProps) {
  const { addInventoryItem, updateInventoryItem, getMetalRate, categories } = useStore()
  const [formData, setFormData] = useState({
    // Basic Information
    name: "",
    category: "",
    subcategory: "",
    metalType: "",
    purity: "",
    description: "",

    // Weight Information
    grossWeight: "",
    stoneWeight: "",
    netWeight: "",
    diamondWeight: "",
    wastageWeight: "",
    chargeableWeight: "",

    // Stone Details
    stoneDetails: "",
    stoneAmount: "",
    stoneType: "",
    stoneClarity: "",
    stoneColor: "",
    stoneCut: "",
    stoneCount: "",

    // Pricing Information
    purchaseRate: "",
    metalAmount: "",
    makingCharges: "",
    makingChargeType: "percentage", // percentage, fixed, per_gram
    stoneCharges: "",
    otherCharges: "",
    wastagePercentage: "",
    marginPercentage: "",
    sellingPrice: "",
    mrp: "",

    // Stock Information
    stock: "",
    minStockLevel: "",
    maxStockLevel: "",
    location: "",

    // Additional Details
    size: "",
    gender: "unisex",
    occasion: "",
    designNumber: "",

    // Certification & Compliance
    hsnCode: "",
    bisHallmark: false,
    hallmarkNumber: "",
    purityCertificate: false,
    certificateNumber: "",

    // Business Specific
    tags: [] as string[],
    isCustomMade: false,
    estimatedDelivery: "",
    specialInstructions: "",
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [estimatedValue, setEstimatedValue] = useState(0)
  const [isEditMode, setIsEditMode] = useState(false)
  const [activeTab, setActiveTab] = useState("basic")
  const [newTag, setNewTag] = useState("")

  // Enhanced calculations
  const [calculations, setCalculations] = useState({
    metalValue: 0,
    stoneValue: 0,
    makingValue: 0,
    wastageValue: 0,
    totalCost: 0,
    profitMargin: 0,
    finalPrice: 0
  })

  useEffect(() => {
    if (item) {
      // Edit mode - populate form with existing item data
      setIsEditMode(true)
      setFormData({
        // Basic Information
        name: item.name || "",
        category: item.category || "",
        subcategory: item.subcategory || "",
        metalType: item.metalType || "",
        purity: item.purity || "",
        description: item.description || "",

        // Weight Information
        grossWeight: item.grossWeight?.toString() || "",
        stoneWeight: item.stoneWeight?.toString() || "",
        netWeight: item.netWeight?.toString() || "",
        diamondWeight: item.diamondWeight?.toString() || "",
        wastageWeight: item.wastageWeight?.toString() || "",
        chargeableWeight: item.chargeableWeight?.toString() || "",

        // Stone Details
        stoneDetails: item.stoneDetails || "",
        stoneAmount: item.stoneAmount?.toString() || "",
        stoneType: item.stoneType || "",
        stoneClarity: item.stoneClarity || "",
        stoneColor: item.stoneColor || "",
        stoneCut: item.stoneCut || "",
        stoneCount: item.stoneCount?.toString() || "",

        // Pricing Information
        purchaseRate: item.purchaseRate?.toString() || "",
        metalAmount: item.metalAmount?.toString() || "",
        makingCharges: item.makingCharges?.toString() || "",
        makingChargeType: item.makingChargeType || "percentage",
        stoneCharges: item.stoneCharges?.toString() || "",
        otherCharges: item.otherCharges?.toString() || "",
        wastagePercentage: item.wastagePercentage?.toString() || "",
        marginPercentage: item.marginPercentage?.toString() || "",
        sellingPrice: item.sellingPrice?.toString() || "",
        mrp: item.mrp?.toString() || "",

        // Stock Information
        stock: item.stock?.toString() || "",
        minStockLevel: item.minStockLevel?.toString() || "",
        maxStockLevel: item.maxStockLevel?.toString() || "",
        location: item.location || "",

        // Additional Details
        size: item.size || "",
        gender: item.gender || "unisex",
        occasion: item.occasion || "",
        designNumber: item.designNumber || "",

        // Certification & Compliance
        hsnCode: item.hsnCode || "",
        bisHallmark: item.bisHallmark || false,
        hallmarkNumber: item.hallmarkNumber || "",
        purityCertificate: item.purityCertificate || false,
        certificateNumber: item.certificateNumber || "",

        // Business Specific
        tags: item.tags || [],
        isCustomMade: item.isCustomMade || false,
        estimatedDelivery: item.estimatedDelivery || "",
        specialInstructions: item.specialInstructions || "",
      })
    }
  }, [item])

  // Enhanced weight calculations
  useEffect(() => {
    const gross = Number.parseFloat(formData.grossWeight) || 0
    const stone = Number.parseFloat(formData.stoneWeight) || 0
    const diamond = Number.parseFloat(formData.diamondWeight) || 0
    const wastagePercentage = Number.parseFloat(formData.wastagePercentage) || 0

    // Calculate net weight
    const net = Math.max(0, gross - stone - diamond)

    // Calculate wastage weight
    const wastage = (net * wastagePercentage) / 100

    // Calculate chargeable weight (net + wastage)
    const chargeable = net + wastage

    setFormData((prev) => ({
      ...prev,
      netWeight: net.toFixed(3),
      wastageWeight: wastage.toFixed(3),
      chargeableWeight: chargeable.toFixed(3)
    }))
  }, [formData.grossWeight, formData.stoneWeight, formData.diamondWeight, formData.wastagePercentage])

  // Enhanced pricing calculations
  useEffect(() => {
    const chargeableWeight = Number.parseFloat(formData.chargeableWeight) || 0
    const purchaseRate = Number.parseFloat(formData.purchaseRate) || 0
    const stoneAmount = Number.parseFloat(formData.stoneAmount) || 0
    const stoneCharges = Number.parseFloat(formData.stoneCharges) || 0
    const otherCharges = Number.parseFloat(formData.otherCharges) || 0
    const makingChargeType = formData.makingChargeType
    const makingChargeValue = Number.parseFloat(formData.makingCharges) || 0
    const marginPercentage = Number.parseFloat(formData.marginPercentage) || 0

    // Get current metal rate
    const metalRate = getMetalRate(formData.metalType, formData.purity)

    // Calculate metal value
    const metalValue = chargeableWeight * (purchaseRate || metalRate)

    // Calculate making charges based on type
    let makingValue = 0
    if (makingChargeType === "percentage") {
      makingValue = (metalValue * makingChargeValue) / 100
    } else if (makingChargeType === "per_gram") {
      makingValue = chargeableWeight * makingChargeValue
    } else {
      makingValue = makingChargeValue // fixed amount
    }

    // Calculate total cost
    const totalCost = metalValue + makingValue + stoneAmount + stoneCharges + otherCharges

    // Calculate selling price with margin
    const sellingPrice = totalCost + (totalCost * marginPercentage / 100)

    const newCalculations = {
      metalValue,
      stoneValue: stoneAmount + stoneCharges,
      makingValue,
      wastageValue: 0, // Already included in chargeable weight
      totalCost,
      profitMargin: sellingPrice - totalCost,
      finalPrice: sellingPrice
    }

    setCalculations(newCalculations)
    setEstimatedValue(sellingPrice)

    // Auto-update selling price if not manually set
    if (!formData.sellingPrice || formData.sellingPrice === "0") {
      setFormData(prev => ({ ...prev, sellingPrice: sellingPrice.toFixed(2) }))
    }
  }, [
    formData.chargeableWeight,
    formData.purchaseRate,
    formData.stoneAmount,
    formData.stoneCharges,
    formData.otherCharges,
    formData.makingCharges,
    formData.makingChargeType,
    formData.marginPercentage,
    formData.metalType,
    formData.purity,
    getMetalRate,
  ])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    // Basic Information Validation
    if (!formData.name.trim()) {
      newErrors.name = "Item name is required"
    }

    if (!formData.category) {
      newErrors.category = "Category is required"
    }

    if (!formData.metalType) {
      newErrors.metalType = "Metal type is required"
    }

    if (!formData.purity) {
      newErrors.purity = "Purity is required"
    }

    // Weight Validation
    if (!formData.grossWeight || Number.parseFloat(formData.grossWeight) <= 0) {
      newErrors.grossWeight = "Gross weight must be greater than 0"
    }

    const grossWeight = Number.parseFloat(formData.grossWeight) || 0
    const stoneWeight = Number.parseFloat(formData.stoneWeight) || 0
    const diamondWeight = Number.parseFloat(formData.diamondWeight) || 0

    if (stoneWeight < 0) {
      newErrors.stoneWeight = "Stone weight cannot be negative"
    }

    if (diamondWeight < 0) {
      newErrors.diamondWeight = "Diamond weight cannot be negative"
    }

    if ((stoneWeight + diamondWeight) >= grossWeight) {
      newErrors.stoneWeight = "Combined stone and diamond weight must be less than gross weight"
    }

    // Stock Validation
    if (!formData.stock || Number.parseInt(formData.stock) < 0) {
      newErrors.stock = "Stock must be 0 or greater"
    }

    // Pricing Validation
    if (Number.parseFloat(formData.stoneAmount) < 0) {
      newErrors.stoneAmount = "Stone amount cannot be negative"
    }

    if (Number.parseFloat(formData.makingCharges) < 0) {
      newErrors.makingCharges = "Making charges cannot be negative"
    }

    if (Number.parseFloat(formData.purchaseRate) < 0) {
      newErrors.purchaseRate = "Purchase rate cannot be negative"
    }

    if (Number.parseFloat(formData.marginPercentage) < 0) {
      newErrors.marginPercentage = "Margin percentage cannot be negative"
    }

    // Certification Validation
    if (formData.bisHallmark && !formData.hallmarkNumber.trim()) {
      newErrors.hallmarkNumber = "Hallmark number is required when BIS hallmark is selected"
    }

    if (formData.purityCertificate && !formData.certificateNumber.trim()) {
      newErrors.certificateNumber = "Certificate number is required when purity certificate is selected"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)

    try {
      const itemData = {
        // Basic Information
        name: formData.name.trim(),
        category: formData.category,
        subcategory: formData.subcategory || null,
        metalType: formData.metalType,
        purity: formData.purity,
        description: formData.description.trim(),

        // Weight Information
        grossWeight: Number.parseFloat(formData.grossWeight),
        stoneWeight: Number.parseFloat(formData.stoneWeight) || 0,
        netWeight: Number.parseFloat(formData.netWeight),
        diamondWeight: Number.parseFloat(formData.diamondWeight) || 0,
        wastageWeight: Number.parseFloat(formData.wastageWeight) || 0,
        chargeableWeight: Number.parseFloat(formData.chargeableWeight) || 0,

        // Stone Details
        stoneDetails: formData.stoneDetails.trim(),
        stoneAmount: Number.parseFloat(formData.stoneAmount) || 0,
        stoneType: formData.stoneType || null,
        stoneClarity: formData.stoneClarity || null,
        stoneColor: formData.stoneColor || null,
        stoneCut: formData.stoneCut || null,
        stoneCount: Number.parseInt(formData.stoneCount) || 0,

        // Pricing Information
        purchaseRate: Number.parseFloat(formData.purchaseRate) || 0,
        metalAmount: calculations.metalValue,
        makingCharges: Number.parseFloat(formData.makingCharges) || 0,
        makingChargeType: formData.makingChargeType,
        stoneCharges: Number.parseFloat(formData.stoneCharges) || 0,
        otherCharges: Number.parseFloat(formData.otherCharges) || 0,
        wastagePercentage: Number.parseFloat(formData.wastagePercentage) || 0,
        marginPercentage: Number.parseFloat(formData.marginPercentage) || 0,
        sellingPrice: Number.parseFloat(formData.sellingPrice) || 0,
        mrp: Number.parseFloat(formData.mrp) || 0,
        currentValue: estimatedValue,
        totalCost: calculations.totalCost,

        // Stock Information
        stock: Number.parseInt(formData.stock),
        minStockLevel: Number.parseInt(formData.minStockLevel) || 0,
        maxStockLevel: Number.parseInt(formData.maxStockLevel) || 100,
        location: formData.location || null,

        // Additional Details
        size: formData.size || null,
        gender: formData.gender,
        occasion: formData.occasion || null,
        designNumber: formData.designNumber || null,

        // Certification & Compliance
        hsnCode: formData.hsnCode || getHSNCode(formData.category, formData.metalType),
        bisHallmark: formData.bisHallmark,
        hallmarkNumber: formData.hallmarkNumber || null,
        purityCertificate: formData.purityCertificate,
        certificateNumber: formData.certificateNumber || null,

        // Business Specific
        tags: formData.tags,
        isCustomMade: formData.isCustomMade,
        estimatedDelivery: formData.estimatedDelivery || null,
        specialInstructions: formData.specialInstructions.trim() || null,
      }

      if (item) {
        await updateInventoryItem(item.id, itemData)
      } else {
        await addInventoryItem(itemData)
      }

      // Reset form
      resetForm()
      onSubmit()
    } catch (error) {
      // Error handling - keep for debugging in development
      setErrors({ submit: "Failed to save item. Please try again." })
    } finally {
      setIsSubmitting(false)
    }
  }

  const resetForm = () => {
    setFormData({
      // Basic Information
      name: "",
      category: "",
      subcategory: "",
      metalType: "",
      purity: "",
      description: "",

      // Weight Information
      grossWeight: "",
      stoneWeight: "",
      netWeight: "",
      diamondWeight: "",
      wastageWeight: "",
      chargeableWeight: "",

      // Stone Details
      stoneDetails: "",
      stoneAmount: "",
      stoneType: "",
      stoneClarity: "",
      stoneColor: "",
      stoneCut: "",
      stoneCount: "",

      // Pricing Information
      purchaseRate: "",
      metalAmount: "",
      makingCharges: "",
      makingChargeType: "percentage",
      stoneCharges: "",
      otherCharges: "",
      wastagePercentage: "",
      marginPercentage: "",
      sellingPrice: "",
      mrp: "",

      // Stock Information
      stock: "",
      minStockLevel: "",
      maxStockLevel: "",
      location: "",

      // Additional Details
      size: "",
      gender: "unisex",
      occasion: "",
      designNumber: "",

      // Certification & Compliance
      hsnCode: "",
      bisHallmark: false,
      hallmarkNumber: "",
      purityCertificate: false,
      certificateNumber: "",

      // Business Specific
      tags: [],
      isCustomMade: false,
      estimatedDelivery: "",
      specialInstructions: "",
    })
    setErrors({})
    setIsEditMode(false)
    setActiveTab("basic")
  }

  const handleInputChange = (field: string, value: string | boolean | string[]) => {
    setFormData({ ...formData, [field]: value })
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors({ ...errors, [field]: "" })
    }
  }

  const getPurityOptions = () => {
    if (formData.metalType === "gold") {
      return ["24K", "22K", "18K", "14K", "10K"]
    } else if (formData.metalType === "silver") {
      return ["999", "925", "900", "800"]
    } else if (formData.metalType === "platinum") {
      return ["950", "900", "850"]
    }
    return []
  }

  const getIndianJewelryCategories = () => {
    return [
      // Traditional Categories
      "Rings", "Necklaces", "Earrings", "Bangles", "Bracelets", "Chains", "Pendants",

      // Indian Traditional
      "Temple Jewelry", "Kundan Jewelry", "Meenakari Jewelry", "Polki Jewelry",
      "Jadau Jewelry", "Thewa Jewelry", "Filigree Jewelry",

      // Regional Specialties
      "Kolhapuri Jewelry", "Rajasthani Jewelry", "South Indian Jewelry",
      "Bengali Jewelry", "Punjabi Jewelry", "Gujarati Jewelry",

      // Specific Items
      "Mangalsutra", "Nose Rings", "Toe Rings", "Anklets", "Armlets",
      "Waist Chains", "Hair Ornaments", "Maang Tikka", "Nath",

      // Modern Categories
      "Fashion Jewelry", "Bridal Sets", "Party Wear", "Daily Wear",
      "Office Wear", "Casual Jewelry",

      // Special Categories
      "Antique Jewelry", "Vintage Jewelry", "Designer Jewelry",
      "Custom Jewelry", "Repair Items", "Exchange Items"
    ]
  }

  const getStoneTypes = () => {
    return [
      // Precious Stones
      "Diamond", "Ruby", "Emerald", "Sapphire",

      // Semi-Precious Stones
      "Amethyst", "Citrine", "Garnet", "Peridot", "Topaz", "Turquoise",
      "Onyx", "Agate", "Carnelian", "Jasper", "Quartz",

      // Indian Traditional Stones
      "Coral", "Pearl", "Cat's Eye", "Hessonite", "Yellow Sapphire",
      "Blue Sapphire", "Red Coral", "White Pearl", "Black Pearl",

      // Others
      "Cubic Zirconia", "Moissanite", "Glass", "Crystal", "Synthetic"
    ]
  }

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      handleInputChange("tags", [...formData.tags, newTag.trim()])
      setNewTag("")
    }
  }

  const removeTag = (tagToRemove: string) => {
    handleInputChange("tags", formData.tags.filter(tag => tag !== tagToRemove))
  }

  // Generate preview ID and HSN code
  const [previewId, setPreviewId] = useState('')
  const [previewBarcode, setPreviewBarcode] = useState('')
  const [previewHSN, setPreviewHSN] = useState('')

  // Generate preview when category, metal, or purity changes (client-side only)
  useEffect(() => {
    if (formData.category && formData.metalType && formData.purity) {
      // Generate preview IDs using inline logic (no imports)
      const categoryCode = JEWELRY_CATEGORIES[formData.category as keyof typeof JEWELRY_CATEGORIES] || 'GN'
      const metalCode = METAL_CODES[formData.metalType.toLowerCase() as keyof typeof METAL_CODES] || 'OT'
      const purityCode = PURITY_CODES[formData.purity as keyof typeof PURITY_CODES] || formData.purity.replace(/[^0-9]/g, '').slice(0, 3)
      const year = new Date().getFullYear()

      const itemCodePreview = `${categoryCode}${metalCode}${purityCode}-${year}-XXXX`
      const barcodePreview = `SJ01${purityCode}XXXXXX`
      const hsnCode = '71131900' // Default HSN code

      setPreviewId(itemCodePreview)
      setPreviewBarcode(barcodePreview)
      setPreviewHSN(hsnCode)
    } else {
      setPreviewId('')
      setPreviewBarcode('')
      setPreviewHSN('')
    }
  }, [formData.category, formData.metalType, formData.purity])

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="basic" className="flex items-center gap-2">
            <Tag className="h-4 w-4" />
            Basic
          </TabsTrigger>
          <TabsTrigger value="weights" className="flex items-center gap-2">
            <Scale className="h-4 w-4" />
            Weights
          </TabsTrigger>
          <TabsTrigger value="stones" className="flex items-center gap-2">
            <Gem className="h-4 w-4" />
            Stones
          </TabsTrigger>
          <TabsTrigger value="pricing" className="flex items-center gap-2">
            <Calculator className="h-4 w-4" />
            Pricing
          </TabsTrigger>
          <TabsTrigger value="certification" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Certification
          </TabsTrigger>
          <TabsTrigger value="additional" className="flex items-center gap-2">
            <Zap className="h-4 w-4" />
            Additional
          </TabsTrigger>
        </TabsList>

        {/* Basic Information Tab */}
        <TabsContent value="basic" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Tag className="h-5 w-5" />
                Basic Information
              </CardTitle>
              <CardDescription>
                Enter the fundamental details of the jewelry item
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Item Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    placeholder="e.g., Gold Diamond Ring"
                    required
                    className={errors.name ? "border-red-500" : ""}
                  />
                  {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="designNumber">Design Number</Label>
                  <Input
                    id="designNumber"
                    value={formData.designNumber}
                    onChange={(e) => handleInputChange("designNumber", e.target.value)}
                    placeholder="e.g., RG-2024-001"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="category">Category *</Label>
                  <Select value={formData.category || undefined} onValueChange={(value) => handleInputChange("category", value)}>
                    <SelectTrigger className={errors.category ? "border-red-500" : ""}>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {getIndianJewelryCategories().map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.category && <p className="text-sm text-red-500">{errors.category}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="subcategory">Subcategory</Label>
                  <Input
                    id="subcategory"
                    value={formData.subcategory}
                    onChange={(e) => handleInputChange("subcategory", e.target.value)}
                    placeholder="e.g., Wedding Ring, Fashion Ring"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="metalType">Metal Type *</Label>
                  <Select
                    value={formData.metalType || undefined}
                    onValueChange={(value) => {
                      handleInputChange("metalType", value)
                      handleInputChange("purity", "") // Reset purity when metal type changes
                    }}
                  >
                    <SelectTrigger className={errors.metalType ? "border-red-500" : ""}>
                      <SelectValue placeholder="Select metal" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="gold">Gold</SelectItem>
                      <SelectItem value="silver">Silver</SelectItem>
                      <SelectItem value="platinum">Platinum</SelectItem>
                      <SelectItem value="diamond">Diamond</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.metalType && <p className="text-sm text-red-500">{errors.metalType}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="purity">Purity *</Label>
                  <Select value={formData.purity || undefined} onValueChange={(value) => handleInputChange("purity", value)}>
                    <SelectTrigger className={errors.purity ? "border-red-500" : ""}>
                      <SelectValue placeholder="Select purity" />
                    </SelectTrigger>
                    <SelectContent>
                      {getPurityOptions().map((purity) => (
                        <SelectItem key={purity} value={purity}>
                          {purity}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.purity && <p className="text-sm text-red-500">{errors.purity}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="gender">Gender</Label>
                  <Select value={formData.gender} onValueChange={(value) => handleInputChange("gender", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select gender" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="male">Male</SelectItem>
                      <SelectItem value="female">Female</SelectItem>
                      <SelectItem value="unisex">Unisex</SelectItem>
                      <SelectItem value="kids">Kids</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="size">Size</Label>
                  <Input
                    id="size"
                    value={formData.size}
                    onChange={(e) => handleInputChange("size", e.target.value)}
                    placeholder="e.g., 16, M, L, 7.5"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="occasion">Occasion</Label>
                  <Input
                    id="occasion"
                    value={formData.occasion}
                    onChange={(e) => handleInputChange("occasion", e.target.value)}
                    placeholder="e.g., Wedding, Festival, Daily Wear"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange("description", e.target.value)}
                  placeholder="Detailed description of the jewelry item..."
                  rows={3}
                />
              </div>

              {/* Tags Section */}
              <div className="space-y-2">
                <Label>Tags</Label>
                <div className="flex flex-wrap gap-2 mb-2">
                  {formData.tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="cursor-pointer" onClick={() => removeTag(tag)}>
                      {tag} ×
                    </Badge>
                  ))}
                </div>
                <div className="flex gap-2">
                  <Input
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    placeholder="Add a tag..."
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                  />
                  <Button type="button" onClick={addTag} variant="outline">
                    Add Tag
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Weights Tab */}
        <TabsContent value="weights" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Scale className="h-5 w-5" />
                Weight Measurements
              </CardTitle>
              <CardDescription>
                Enter precise weight measurements for accurate pricing
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="grossWeight">Gross Weight (g) *</Label>
                  <Input
                    id="grossWeight"
                    type="number"
                    step="0.001"
                    min="0"
                    value={formData.grossWeight}
                    onChange={(e) => handleInputChange("grossWeight", e.target.value)}
                    placeholder="25.500"
                    required
                    className={errors.grossWeight ? "border-red-500" : ""}
                  />
                  {errors.grossWeight && <p className="text-sm text-red-500">{errors.grossWeight}</p>}
                  <p className="text-xs text-muted-foreground">Total weight including stones</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="stoneWeight">Stone Weight (g)</Label>
                  <Input
                    id="stoneWeight"
                    type="number"
                    step="0.001"
                    min="0"
                    value={formData.stoneWeight}
                    onChange={(e) => handleInputChange("stoneWeight", e.target.value)}
                    placeholder="2.500"
                    className={errors.stoneWeight ? "border-red-500" : ""}
                  />
                  {errors.stoneWeight && <p className="text-sm text-red-500">{errors.stoneWeight}</p>}
                  <p className="text-xs text-muted-foreground">Weight of all stones</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="diamondWeight">Diamond Weight (g)</Label>
                  <Input
                    id="diamondWeight"
                    type="number"
                    step="0.001"
                    min="0"
                    value={formData.diamondWeight}
                    onChange={(e) => handleInputChange("diamondWeight", e.target.value)}
                    placeholder="0.500"
                    className={errors.diamondWeight ? "border-red-500" : ""}
                  />
                  {errors.diamondWeight && <p className="text-sm text-red-500">{errors.diamondWeight}</p>}
                  <p className="text-xs text-muted-foreground">Weight of diamonds only</p>
                </div>
              </div>

              <Separator />

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="netWeight">Net Weight (g)</Label>
                  <Input
                    id="netWeight"
                    type="number"
                    step="0.001"
                    value={formData.netWeight}
                    readOnly
                    className="bg-gray-50"
                    placeholder="Auto-calculated"
                  />
                  <p className="text-xs text-muted-foreground">Gross - Stone - Diamond</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="wastagePercentage">Wastage %</Label>
                  <Input
                    id="wastagePercentage"
                    type="number"
                    step="0.01"
                    min="0"
                    max="100"
                    value={formData.wastagePercentage}
                    onChange={(e) => handleInputChange("wastagePercentage", e.target.value)}
                    placeholder="2.00"
                  />
                  <p className="text-xs text-muted-foreground">Manufacturing wastage</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="chargeableWeight">Chargeable Weight (g)</Label>
                  <Input
                    id="chargeableWeight"
                    type="number"
                    step="0.001"
                    value={formData.chargeableWeight}
                    readOnly
                    className="bg-green-50"
                    placeholder="Auto-calculated"
                  />
                  <p className="text-xs text-muted-foreground">Net + Wastage</p>
                </div>
              </div>

              {/* Weight Summary Card */}
              <Card className="bg-blue-50 border-blue-200">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg text-blue-900">Weight Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <p className="text-blue-700 font-medium">Gross Weight</p>
                      <p className="text-blue-900 font-semibold">{formData.grossWeight || "0.000"}g</p>
                    </div>
                    <div>
                      <p className="text-blue-700 font-medium">Net Weight</p>
                      <p className="text-blue-900 font-semibold">{formData.netWeight || "0.000"}g</p>
                    </div>
                    <div>
                      <p className="text-blue-700 font-medium">Wastage</p>
                      <p className="text-blue-900 font-semibold">{formData.wastageWeight || "0.000"}g</p>
                    </div>
                    <div>
                      <p className="text-blue-700 font-medium">Chargeable</p>
                      <p className="text-blue-900 font-semibold">{formData.chargeableWeight || "0.000"}g</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Stones Tab */}
        <TabsContent value="stones" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Gem className="h-5 w-5" />
                Stone Details
              </CardTitle>
              <CardDescription>
                Detailed information about stones and diamonds
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="stoneType">Stone Type</Label>
                  <Select value={formData.stoneType || undefined} onValueChange={(value) => handleInputChange("stoneType", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select stone type" />
                    </SelectTrigger>
                    <SelectContent>
                      {getStoneTypes().map((stone) => (
                        <SelectItem key={stone} value={stone}>
                          {stone}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="stoneCount">Stone Count</Label>
                  <Input
                    id="stoneCount"
                    type="number"
                    min="0"
                    value={formData.stoneCount}
                    onChange={(e) => handleInputChange("stoneCount", e.target.value)}
                    placeholder="12"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="stoneClarity">Stone Clarity</Label>
                  <Select value={formData.stoneClarity || undefined} onValueChange={(value) => handleInputChange("stoneClarity", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select clarity" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="FL">FL (Flawless)</SelectItem>
                      <SelectItem value="IF">IF (Internally Flawless)</SelectItem>
                      <SelectItem value="VVS1">VVS1 (Very Very Slightly Included)</SelectItem>
                      <SelectItem value="VVS2">VVS2</SelectItem>
                      <SelectItem value="VS1">VS1 (Very Slightly Included)</SelectItem>
                      <SelectItem value="VS2">VS2</SelectItem>
                      <SelectItem value="SI1">SI1 (Slightly Included)</SelectItem>
                      <SelectItem value="SI2">SI2</SelectItem>
                      <SelectItem value="I1">I1 (Included)</SelectItem>
                      <SelectItem value="I2">I2</SelectItem>
                      <SelectItem value="I3">I3</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="stoneColor">Stone Color</Label>
                  <Select value={formData.stoneColor || undefined} onValueChange={(value) => handleInputChange("stoneColor", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select color" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="D">D (Colorless)</SelectItem>
                      <SelectItem value="E">E</SelectItem>
                      <SelectItem value="F">F</SelectItem>
                      <SelectItem value="G">G (Near Colorless)</SelectItem>
                      <SelectItem value="H">H</SelectItem>
                      <SelectItem value="I">I</SelectItem>
                      <SelectItem value="J">J</SelectItem>
                      <SelectItem value="K">K (Faint Yellow)</SelectItem>
                      <SelectItem value="L">L</SelectItem>
                      <SelectItem value="M">M</SelectItem>
                      <SelectItem value="Fancy">Fancy Color</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="stoneCut">Stone Cut</Label>
                  <Select value={formData.stoneCut || undefined} onValueChange={(value) => handleInputChange("stoneCut", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select cut" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Round">Round</SelectItem>
                      <SelectItem value="Princess">Princess</SelectItem>
                      <SelectItem value="Emerald">Emerald</SelectItem>
                      <SelectItem value="Asscher">Asscher</SelectItem>
                      <SelectItem value="Oval">Oval</SelectItem>
                      <SelectItem value="Radiant">Radiant</SelectItem>
                      <SelectItem value="Cushion">Cushion</SelectItem>
                      <SelectItem value="Heart">Heart</SelectItem>
                      <SelectItem value="Pear">Pear</SelectItem>
                      <SelectItem value="Marquise">Marquise</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="stoneAmount">Stone Value (₹)</Label>
                  <Input
                    id="stoneAmount"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.stoneAmount}
                    onChange={(e) => handleInputChange("stoneAmount", e.target.value)}
                    placeholder="15000"
                    className={errors.stoneAmount ? "border-red-500" : ""}
                  />
                  {errors.stoneAmount && <p className="text-sm text-red-500">{errors.stoneAmount}</p>}
                  <p className="text-xs text-muted-foreground">Total value of all stones</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="stoneCharges">Stone Setting Charges (₹)</Label>
                  <Input
                    id="stoneCharges"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.stoneCharges}
                    onChange={(e) => handleInputChange("stoneCharges", e.target.value)}
                    placeholder="2000"
                  />
                  <p className="text-xs text-muted-foreground">Labor charges for stone setting</p>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="stoneDetails">Detailed Stone Description</Label>
                <Textarea
                  id="stoneDetails"
                  value={formData.stoneDetails}
                  onChange={(e) => handleInputChange("stoneDetails", e.target.value)}
                  placeholder="Detailed description of stones, including certificates, origin, special characteristics..."
                  rows={3}
                />
              </div>

              {/* Stone Summary */}
              {(formData.stoneType || formData.stoneCount || formData.stoneAmount) && (
                <Card className="bg-purple-50 border-purple-200">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg text-purple-900">Stone Summary</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <p className="text-purple-700 font-medium">Type</p>
                        <p className="text-purple-900 font-semibold">{formData.stoneType || "Not specified"}</p>
                      </div>
                      <div>
                        <p className="text-purple-700 font-medium">Count</p>
                        <p className="text-purple-900 font-semibold">{formData.stoneCount || "0"}</p>
                      </div>
                      <div>
                        <p className="text-purple-700 font-medium">Weight</p>
                        <p className="text-purple-900 font-semibold">{formData.stoneWeight || "0.000"}g</p>
                      </div>
                      <div>
                        <p className="text-purple-700 font-medium">Value</p>
                        <p className="text-purple-900 font-semibold">₹{Number(formData.stoneAmount || 0).toLocaleString()}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Pricing Tab */}
        <TabsContent value="pricing" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calculator className="h-5 w-5" />
                Pricing Information
              </CardTitle>
              <CardDescription>
                Configure pricing, margins, and cost calculations
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="purchaseRate">Purchase Rate (₹/g)</Label>
                  <Input
                    id="purchaseRate"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.purchaseRate}
                    onChange={(e) => handleInputChange("purchaseRate", e.target.value)}
                    placeholder="6500"
                    className={errors.purchaseRate ? "border-red-500" : ""}
                  />
                  {errors.purchaseRate && <p className="text-sm text-red-500">{errors.purchaseRate}</p>}
                  <p className="text-xs text-muted-foreground">Rate per gram for metal</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="makingChargeType">Making Charge Type</Label>
                  <Select value={formData.makingChargeType} onValueChange={(value) => handleInputChange("makingChargeType", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="percentage">Percentage (%)</SelectItem>
                      <SelectItem value="per_gram">Per Gram (₹/g)</SelectItem>
                      <SelectItem value="fixed">Fixed Amount (₹)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="makingCharges">
                    Making Charges
                    {formData.makingChargeType === "percentage" && " (%)"}
                    {formData.makingChargeType === "per_gram" && " (₹/g)"}
                    {formData.makingChargeType === "fixed" && " (₹)"}
                  </Label>
                  <Input
                    id="makingCharges"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.makingCharges}
                    onChange={(e) => handleInputChange("makingCharges", e.target.value)}
                    placeholder={
                      formData.makingChargeType === "percentage" ? "15" :
                      formData.makingChargeType === "per_gram" ? "500" : "8500"
                    }
                    className={errors.makingCharges ? "border-red-500" : ""}
                  />
                  {errors.makingCharges && <p className="text-sm text-red-500">{errors.makingCharges}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="otherCharges">Other Charges (₹)</Label>
                  <Input
                    id="otherCharges"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.otherCharges}
                    onChange={(e) => handleInputChange("otherCharges", e.target.value)}
                    placeholder="1000"
                  />
                  <p className="text-xs text-muted-foreground">Packaging, certification, etc.</p>
                </div>
              </div>

              <Separator />

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="marginPercentage">Profit Margin (%)</Label>
                  <Input
                    id="marginPercentage"
                    type="number"
                    step="0.01"
                    min="0"
                    max="100"
                    value={formData.marginPercentage}
                    onChange={(e) => handleInputChange("marginPercentage", e.target.value)}
                    placeholder="20"
                    className={errors.marginPercentage ? "border-red-500" : ""}
                  />
                  {errors.marginPercentage && <p className="text-sm text-red-500">{errors.marginPercentage}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="sellingPrice">Selling Price (₹)</Label>
                  <Input
                    id="sellingPrice"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.sellingPrice}
                    onChange={(e) => handleInputChange("sellingPrice", e.target.value)}
                    placeholder="Auto-calculated"
                    className="bg-green-50"
                  />
                  <p className="text-xs text-muted-foreground">Final selling price</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="mrp">MRP (₹)</Label>
                  <Input
                    id="mrp"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.mrp}
                    onChange={(e) => handleInputChange("mrp", e.target.value)}
                    placeholder="Optional"
                  />
                  <p className="text-xs text-muted-foreground">Maximum retail price</p>
                </div>
              </div>

              {/* Pricing Breakdown */}
              <Card className="bg-green-50 border-green-200">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg text-green-900">Pricing Breakdown</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <p className="text-green-700 font-medium">Metal Value</p>
                      <p className="text-green-900 font-semibold">₹{calculations.metalValue.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-green-700 font-medium">Making Charges</p>
                      <p className="text-green-900 font-semibold">₹{calculations.makingValue.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-green-700 font-medium">Stone Value</p>
                      <p className="text-green-900 font-semibold">₹{calculations.stoneValue.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-green-700 font-medium">Total Cost</p>
                      <p className="text-green-900 font-semibold">₹{calculations.totalCost.toLocaleString()}</p>
                    </div>
                  </div>
                  <Separator className="my-3" />
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-green-700 font-medium">Profit Margin</p>
                      <p className="text-green-900 font-semibold">₹{calculations.profitMargin.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-green-700 font-medium">Final Price</p>
                      <p className="text-green-900 font-bold text-lg">₹{calculations.finalPrice.toLocaleString()}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Certification Tab */}
        <TabsContent value="certification" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Certification & Compliance
              </CardTitle>
              <CardDescription>
                BIS hallmarking, purity certificates, and compliance information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="hsnCode">HSN Code</Label>
                  <Input
                    id="hsnCode"
                    value={formData.hsnCode || previewHSN}
                    onChange={(e) => handleInputChange("hsnCode", e.target.value)}
                    placeholder="Auto-generated"
                    className="bg-gray-50"
                  />
                  <p className="text-xs text-muted-foreground">Harmonized System of Nomenclature code</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="location">Storage Location</Label>
                  <Input
                    id="location"
                    value={formData.location}
                    onChange={(e) => handleInputChange("location", e.target.value)}
                    placeholder="e.g., Showcase A, Vault 1, Counter 2"
                  />
                </div>
              </div>

              <Separator />

              {/* BIS Hallmarking */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="bisHallmark"
                    checked={formData.bisHallmark}
                    onCheckedChange={(checked) => handleInputChange("bisHallmark", checked)}
                  />
                  <Label htmlFor="bisHallmark" className="text-sm font-medium">
                    BIS Hallmarked
                  </Label>
                </div>

                {formData.bisHallmark && (
                  <div className="space-y-2">
                    <Label htmlFor="hallmarkNumber">Hallmark Number *</Label>
                    <Input
                      id="hallmarkNumber"
                      value={formData.hallmarkNumber}
                      onChange={(e) => handleInputChange("hallmarkNumber", e.target.value)}
                      placeholder="e.g., BIS-H-123456"
                      className={errors.hallmarkNumber ? "border-red-500" : ""}
                    />
                    {errors.hallmarkNumber && <p className="text-sm text-red-500">{errors.hallmarkNumber}</p>}
                  </div>
                )}
              </div>

              <Separator />

              {/* Purity Certificate */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="purityCertificate"
                    checked={formData.purityCertificate}
                    onCheckedChange={(checked) => handleInputChange("purityCertificate", checked)}
                  />
                  <Label htmlFor="purityCertificate" className="text-sm font-medium">
                    Purity Certificate Available
                  </Label>
                </div>

                {formData.purityCertificate && (
                  <div className="space-y-2">
                    <Label htmlFor="certificateNumber">Certificate Number *</Label>
                    <Input
                      id="certificateNumber"
                      value={formData.certificateNumber}
                      onChange={(e) => handleInputChange("certificateNumber", e.target.value)}
                      placeholder="e.g., PC-2024-001234"
                      className={errors.certificateNumber ? "border-red-500" : ""}
                    />
                    {errors.certificateNumber && <p className="text-sm text-red-500">{errors.certificateNumber}</p>}
                  </div>
                )}
              </div>

              {/* Compliance Summary */}
              {(formData.bisHallmark || formData.purityCertificate) && (
                <Card className="bg-yellow-50 border-yellow-200">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg text-yellow-900">Compliance Status</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {formData.bisHallmark && (
                        <div className="flex items-center gap-2">
                          <Badge variant="default" className="bg-green-600">✓ BIS Hallmarked</Badge>
                          <span className="text-sm text-yellow-800">{formData.hallmarkNumber}</span>
                        </div>
                      )}
                      {formData.purityCertificate && (
                        <div className="flex items-center gap-2">
                          <Badge variant="default" className="bg-blue-600">✓ Purity Certified</Badge>
                          <span className="text-sm text-yellow-800">{formData.certificateNumber}</span>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Additional Tab */}
        <TabsContent value="additional" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Additional Information
              </CardTitle>
              <CardDescription>
                Stock management, custom orders, and special instructions
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="stock">Stock Quantity *</Label>
                  <Input
                    id="stock"
                    type="number"
                    min="0"
                    value={formData.stock}
                    onChange={(e) => handleInputChange("stock", e.target.value)}
                    placeholder="1"
                    required
                    className={errors.stock ? "border-red-500" : ""}
                  />
                  {errors.stock && <p className="text-sm text-red-500">{errors.stock}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="minStockLevel">Min Stock Level</Label>
                  <Input
                    id="minStockLevel"
                    type="number"
                    min="0"
                    value={formData.minStockLevel}
                    onChange={(e) => handleInputChange("minStockLevel", e.target.value)}
                    placeholder="1"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxStockLevel">Max Stock Level</Label>
                  <Input
                    id="maxStockLevel"
                    type="number"
                    min="0"
                    value={formData.maxStockLevel}
                    onChange={(e) => handleInputChange("maxStockLevel", e.target.value)}
                    placeholder="10"
                  />
                </div>
              </div>

              <Separator />

              {/* Custom Order Section */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isCustomMade"
                    checked={formData.isCustomMade}
                    onCheckedChange={(checked) => handleInputChange("isCustomMade", checked)}
                  />
                  <Label htmlFor="isCustomMade" className="text-sm font-medium">
                    Custom Made Item
                  </Label>
                </div>

                {formData.isCustomMade && (
                  <div className="space-y-2">
                    <Label htmlFor="estimatedDelivery">Estimated Delivery Date</Label>
                    <Input
                      id="estimatedDelivery"
                      type="date"
                      value={formData.estimatedDelivery}
                      onChange={(e) => handleInputChange("estimatedDelivery", e.target.value)}
                    />
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="specialInstructions">Special Instructions</Label>
                <Textarea
                  id="specialInstructions"
                  value={formData.specialInstructions}
                  onChange={(e) => handleInputChange("specialInstructions", e.target.value)}
                  placeholder="Any special handling instructions, customer preferences, or notes..."
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Item Preview */}
      {previewId && (
        <Card className="bg-blue-50 border-blue-200">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg text-blue-900">Item Preview</CardTitle>
            <CardDescription>Generated item details and summary</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <div>
                  <p className="text-blue-700 font-medium">Item ID</p>
                  <p className="font-mono text-blue-900 bg-white px-3 py-2 rounded border">{previewId}</p>
                </div>
                <div>
                  <p className="text-blue-700 font-medium">HSN Code</p>
                  <p className="font-mono text-blue-900 bg-white px-3 py-2 rounded border">{previewHSN}</p>
                </div>
                <div>
                  <p className="text-blue-700 font-medium">Category</p>
                  <p className="text-blue-900 bg-white px-3 py-2 rounded border">{formData.category}</p>
                </div>
              </div>
              <div className="space-y-3">
                <div>
                  <p className="text-blue-700 font-medium">Metal & Purity</p>
                  <p className="text-blue-900 bg-white px-3 py-2 rounded border">
                    {formData.metalType} {formData.purity}
                  </p>
                </div>
                <div>
                  <p className="text-blue-700 font-medium">Chargeable Weight</p>
                  <p className="text-blue-900 bg-white px-3 py-2 rounded border">{formData.chargeableWeight}g</p>
                </div>
                <div>
                  <p className="text-blue-700 font-medium">Estimated Value</p>
                  <p className="text-blue-900 font-bold text-lg bg-white px-3 py-2 rounded border">
                    ₹{estimatedValue.toLocaleString()}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Error Display */}
      {errors.submit && (
        <Card className="bg-red-50 border-red-200">
          <CardContent className="pt-6">
            <p className="text-sm text-red-600">{errors.submit}</p>
          </CardContent>
        </Card>
      )}

      {/* Form Controls */}
      <div className="flex justify-between items-center pt-6 border-t">
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <span>Tab {["basic", "weights", "stones", "pricing", "certification", "additional"].indexOf(activeTab) + 1} of 6</span>
        </div>

        <div className="flex gap-3">
          <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
            Cancel
          </Button>

          {activeTab !== "additional" && (
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                const tabs = ["basic", "weights", "stones", "pricing", "certification", "additional"]
                const currentIndex = tabs.indexOf(activeTab)
                if (currentIndex < tabs.length - 1) {
                  setActiveTab(tabs[currentIndex + 1])
                }
              }}
            >
              Next Tab
            </Button>
          )}

          <Button type="submit" disabled={isSubmitting} className="min-w-[120px]">
            {isSubmitting ? "Saving..." : item ? "Update Item" : "Add Item"}
          </Button>
        </div>
      </div>
    </form>
  )
}
