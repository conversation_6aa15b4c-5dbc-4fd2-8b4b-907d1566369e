/**
 * Verification Script: Professional ID System
 * Verifies that all professional IDs are correctly formatted and sequences are valid
 */

import mysql from 'mysql2/promise'
import {
  validateCustomerId,
  validateInvoiceNumber,
  validateRepairJobNumber,
  validateInventoryItemCode,
  parseInventoryItemCode
} from '../lib/utils/professional-id-generator'

// Database configuration
const DB_CONFIG = {
  host: 'localhost',
  user: 'root',
  password: '', // Set your password
  database: 'jewellers_db'
}

interface VerificationResult {
  entity: string
  totalRecords: number
  validIds: number
  invalidIds: number
  issues: string[]
}

class ProfessionalIdVerifier {
  private connection: mysql.Connection | null = null
  private results: VerificationResult[] = []

  async connect(): Promise<void> {
    this.connection = await mysql.createConnection(DB_CONFIG)
    console.log('✅ Connected to database')
  }

  async disconnect(): Promise<void> {
    if (this.connection) {
      await this.connection.end()
      console.log('✅ Disconnected from database')
    }
  }

  async verifyCustomerIds(): Promise<void> {
    if (!this.connection) throw new Error('No database connection')

    console.log('\n🔍 Verifying customer IDs...')

    const result: VerificationResult = {
      entity: 'customers',
      totalRecords: 0,
      validIds: 0,
      invalidIds: 0,
      issues: []
    }

    try {
      const [customers] = await this.connection.execute(
        'SELECT customer_code, name FROM customers ORDER BY customer_code'
      )

      result.totalRecords = (customers as any[]).length

      for (const customer of customers as any[]) {
        if (validateCustomerId(customer.customer_code)) {
          result.validIds++
          console.log(`   ✅ ${customer.customer_code} - ${customer.name}`)
        } else {
          result.invalidIds++
          result.issues.push(`Invalid customer ID: ${customer.customer_code} (${customer.name})`)
          console.log(`   ❌ ${customer.customer_code} - ${customer.name}`)
        }
      }

      console.log(`   Summary: ${result.validIds}/${result.totalRecords} valid`)

    } catch (error) {
      result.issues.push(`Verification failed: ${error}`)
      console.error(`❌ Customer ID verification failed: ${error}`)
    }

    this.results.push(result)
  }

  async verifyInventoryItemCodes(): Promise<void> {
    if (!this.connection) throw new Error('No database connection')

    console.log('\n🔍 Verifying inventory item codes...')

    const result: VerificationResult = {
      entity: 'inventory',
      totalRecords: 0,
      validIds: 0,
      invalidIds: 0,
      issues: []
    }

    try {
      const [items] = await this.connection.execute(
        'SELECT id, barcode, name FROM inventory ORDER BY id'
      )

      result.totalRecords = (items as any[]).length

      for (const item of items as any[]) {
        const isValidItemCode = validateInventoryItemCode(item.id)
        const parsed = parseInventoryItemCode(item.id)

        if (isValidItemCode && parsed.isValid) {
          result.validIds++
          console.log(`   ✅ ${item.id} | ${item.barcode} - ${item.name}`)
          console.log(`      Category: ${parsed.category}, Metal: ${parsed.metal}, Purity: ${parsed.purity}`)
        } else {
          result.invalidIds++
          result.issues.push(`Invalid item code: ${item.id} (${item.name})`)
          console.log(`   ❌ ${item.id} | ${item.barcode} - ${item.name}`)
        }
      }

      console.log(`   Summary: ${result.validIds}/${result.totalRecords} valid`)

    } catch (error) {
      result.issues.push(`Verification failed: ${error}`)
      console.error(`❌ Inventory verification failed: ${error}`)
    }

    this.results.push(result)
  }

  async verifySalesAndInvoices(): Promise<void> {
    if (!this.connection) throw new Error('No database connection')

    console.log('\n🔍 Verifying sales and invoice numbers...')

    const result: VerificationResult = {
      entity: 'sales',
      totalRecords: 0,
      validIds: 0,
      invalidIds: 0,
      issues: []
    }

    try {
      const [sales] = await this.connection.execute(
        'SELECT sale_number, invoice_number, total_amount FROM sales ORDER BY sale_number'
      )

      result.totalRecords = (sales as any[]).length

      for (const sale of sales as any[]) {
        const isValidSale = sale.sale_number && /^SALE-\d{4}-\d{6}$/.test(sale.sale_number)
        const isValidInvoice = validateInvoiceNumber(sale.invoice_number)

        if (isValidSale && isValidInvoice) {
          result.validIds++
          console.log(`   ✅ ${sale.sale_number} | ${sale.invoice_number} - ₹${sale.total_amount}`)
        } else {
          result.invalidIds++
          const issues = []
          if (!isValidSale) issues.push(`Invalid sale number: ${sale.sale_number}`)
          if (!isValidInvoice) issues.push(`Invalid invoice number: ${sale.invoice_number}`)
          result.issues.push(...issues)
          console.log(`   ❌ ${sale.sale_number} | ${sale.invoice_number} - Issues: ${issues.join(', ')}`)
        }
      }

      console.log(`   Summary: ${result.validIds}/${result.totalRecords} valid`)

    } catch (error) {
      result.issues.push(`Verification failed: ${error}`)
      console.error(`❌ Sales verification failed: ${error}`)
    }

    this.results.push(result)
  }

  async verifySequences(): Promise<void> {
    if (!this.connection) throw new Error('No database connection')

    console.log('\n🔍 Verifying sequence integrity...')

    try {
      const [sequences] = await this.connection.execute(
        'SELECT entity_type, current_sequence, financial_year FROM id_sequences ORDER BY entity_type'
      )

      console.log('   Current Sequences:')
      for (const seq of sequences as any[]) {
        console.log(`   📊 ${seq.entity_type}: ${seq.current_sequence} (FY: ${seq.financial_year})`)
      }

      // Check for gaps or inconsistencies
      const [customers] = await this.connection.execute(
        'SELECT COUNT(*) as count FROM customers WHERE customer_code REGEXP "^CUST-[0-9]{4}-[0-9]{6}$"'
      )
      const [inventory] = await this.connection.execute(
        'SELECT COUNT(*) as count FROM inventory WHERE id REGEXP "^[A-Z]{2}[A-Z]{2}[0-9]{2,3}-[0-9]{4}-[0-9]{4}$"'
      )
      const [sales] = await this.connection.execute(
        'SELECT COUNT(*) as count FROM sales WHERE invoice_number REGEXP "^INV-[0-9]{4}-[0-9]{2}-[0-9]{6}$"'
      )

      console.log('\n   Record Counts:')
      console.log(`   👥 Customers with valid IDs: ${(customers as any[])[0].count}`)
      console.log(`   📦 Inventory with valid codes: ${(inventory as any[])[0].count}`)
      console.log(`   🧾 Sales with valid invoices: ${(sales as any[])[0].count}`)

    } catch (error) {
      console.error(`❌ Sequence verification failed: ${error}`)
    }
  }

  async generateVerificationReport(): Promise<void> {
    console.log('\n' + '='.repeat(80))
    console.log('📊 PROFESSIONAL ID VERIFICATION REPORT')
    console.log('='.repeat(80))

    let totalRecords = 0
    let totalValid = 0
    let totalInvalid = 0
    let totalIssues = 0

    for (const result of this.results) {
      console.log(`\n${result.entity.toUpperCase()}:`)
      console.log(`  Total Records: ${result.totalRecords}`)
      console.log(`  Valid IDs: ${result.validIds} (${((result.validIds / result.totalRecords) * 100).toFixed(1)}%)`)
      console.log(`  Invalid IDs: ${result.invalidIds}`)
      console.log(`  Issues: ${result.issues.length}`)
      
      if (result.issues.length > 0) {
        console.log(`  Issue Details:`)
        result.issues.forEach(issue => console.log(`    - ${issue}`))
      }

      totalRecords += result.totalRecords
      totalValid += result.validIds
      totalInvalid += result.invalidIds
      totalIssues += result.issues.length
    }

    console.log('\n' + '-'.repeat(40))
    console.log(`OVERALL SUMMARY:`)
    console.log(`  Total Records: ${totalRecords}`)
    console.log(`  Valid IDs: ${totalValid} (${((totalValid / totalRecords) * 100).toFixed(1)}%)`)
    console.log(`  Invalid IDs: ${totalInvalid}`)
    console.log(`  Total Issues: ${totalIssues}`)
    console.log(`  System Status: ${totalIssues === 0 ? '✅ ALL GOOD' : '⚠️ NEEDS ATTENTION'}`)
    console.log('='.repeat(80))

    if (totalIssues === 0) {
      console.log('\n🎉 Professional ID system is working perfectly!')
      console.log('All IDs follow the correct business-standard formats.')
    } else {
      console.log('\n⚠️ Some issues found. Please review and fix the invalid IDs.')
      console.log('Consider running the migration script if needed.')
    }
  }

  async run(): Promise<void> {
    try {
      console.log('🔍 Starting Professional ID Verification...')
      
      await this.connect()
      
      // Run verifications
      await this.verifyCustomerIds()
      await this.verifyInventoryItemCodes()
      await this.verifySalesAndInvoices()
      await this.verifySequences()
      
      await this.generateVerificationReport()
      
    } catch (error) {
      console.error('💥 Verification failed:', error)
    } finally {
      await this.disconnect()
    }
  }
}

// Run verification if this file is executed directly
if (require.main === module) {
  const verifier = new ProfessionalIdVerifier()
  verifier.run().catch(console.error)
}

export { ProfessionalIdVerifier }
