/**
 * Sequence Service for Professional ID Generation
 * Manages sequence numbers for all business entities
 */

import { BaseService } from '../base-service'

interface SequenceRecord {
  id: string
  entity_type: string
  current_sequence: number
  prefix: string
  financial_year: string
  created_at: string
  updated_at: string
}

export class SequenceService extends BaseService<SequenceRecord> {
  protected tableName = 'id_sequences'

  /**
   * Get next sequence number for an entity type
   */
  async getNextSequence(entityType: string, financialYear?: string): Promise<number> {
    const fy = financialYear || this.getCurrentFinancialYear()
    
    try {
      // Try to get existing sequence
      const existing = await this.executeQuery(
        `SELECT current_sequence FROM ${this.tableName} WHERE entity_type = ? AND financial_year = ?`,
        [entityType, fy]
      )

      if (existing.length > 0) {
        // Update existing sequence
        const nextSequence = existing[0].current_sequence + 1
        await this.executeUpdate(
          `UPDATE ${this.tableName} SET current_sequence = ?, updated_at = ? WHERE entity_type = ? AND financial_year = ?`,
          [nextSequence, new Date().toISOString(), entityType, fy]
        )
        return nextSequence
      } else {
        // Create new sequence starting from 1
        const id = this.generateId()
        const now = new Date().toISOString()
        
        await this.executeUpdate(
          `INSERT INTO ${this.tableName} (id, entity_type, current_sequence, financial_year, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)`,
          [id, entityType, 1, fy, now, now]
        )
        return 1
      }
    } catch (error) {
      console.error('Error getting next sequence:', error)
      // Fallback to timestamp-based sequence
      return Math.floor(Date.now() / 1000) % 999999
    }
  }

  /**
   * Get current sequence without incrementing
   */
  async getCurrentSequence(entityType: string, financialYear?: string): Promise<number> {
    const fy = financialYear || this.getCurrentFinancialYear()
    
    const result = await this.executeQuery(
      `SELECT current_sequence FROM ${this.tableName} WHERE entity_type = ? AND financial_year = ?`,
      [entityType, fy]
    )

    return result.length > 0 ? result[0].current_sequence : 0
  }

  /**
   * Reset sequence for an entity type (use with caution)
   */
  async resetSequence(entityType: string, startFrom: number = 1, financialYear?: string): Promise<void> {
    const fy = financialYear || this.getCurrentFinancialYear()
    
    await this.executeUpdate(
      `UPDATE ${this.tableName} SET current_sequence = ?, updated_at = ? WHERE entity_type = ? AND financial_year = ?`,
      [startFrom - 1, new Date().toISOString(), entityType, fy]
    )
  }

  /**
   * Initialize sequences for a new financial year
   */
  async initializeFinancialYear(financialYear: string): Promise<void> {
    const entityTypes = [
      'customer',
      'inventory',
      'sale',
      'invoice',
      'repair',
      'scheme',
      'purchase',
      'supplier',
      'exchange',
      'receipt',
      'estimate'
    ]

    const now = new Date().toISOString()

    for (const entityType of entityTypes) {
      // Check if already exists
      const existing = await this.executeQuery(
        `SELECT id FROM ${this.tableName} WHERE entity_type = ? AND financial_year = ?`,
        [entityType, financialYear]
      )

      if (existing.length === 0) {
        const id = this.generateId()
        await this.executeUpdate(
          `INSERT INTO ${this.tableName} (id, entity_type, current_sequence, financial_year, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)`,
          [id, entityType, 0, financialYear, now, now]
        )
      }
    }
  }

  /**
   * Get all sequences for current financial year
   */
  async getAllSequences(financialYear?: string): Promise<Record<string, number>> {
    const fy = financialYear || this.getCurrentFinancialYear()
    
    const sequences = await this.executeQuery(
      `SELECT entity_type, current_sequence FROM ${this.tableName} WHERE financial_year = ?`,
      [fy]
    )

    const result: Record<string, number> = {}
    sequences.forEach(seq => {
      result[seq.entity_type] = seq.current_sequence
    })

    return result
  }

  /**
   * Get current financial year
   */
  private getCurrentFinancialYear(): string {
    const now = new Date()
    const currentYear = now.getFullYear()
    const currentMonth = now.getMonth() + 1 // JavaScript months are 0-indexed
    
    // Indian financial year starts from April (month 4)
    if (currentMonth >= 4) {
      return `${currentYear}-${String(currentYear + 1).slice(-2)}`
    } else {
      return `${currentYear - 1}-${String(currentYear).slice(-2)}`
    }
  }

  /**
   * Bulk update sequences (for migration)
   */
  async bulkUpdateSequences(updates: Array<{
    entityType: string
    sequence: number
    financialYear?: string
  }>): Promise<void> {
    const fy = this.getCurrentFinancialYear()
    
    for (const update of updates) {
      const financialYear = update.financialYear || fy
      
      await this.executeUpdate(
        `INSERT INTO ${this.tableName} (id, entity_type, current_sequence, financial_year, created_at, updated_at) 
         VALUES (?, ?, ?, ?, ?, ?) 
         ON DUPLICATE KEY UPDATE current_sequence = ?, updated_at = ?`,
        [
          this.generateId(),
          update.entityType,
          update.sequence,
          financialYear,
          new Date().toISOString(),
          new Date().toISOString(),
          update.sequence,
          new Date().toISOString()
        ]
      )
    }
  }

  /**
   * Get sequence statistics
   */
  async getSequenceStats(): Promise<Array<{
    entityType: string
    financialYear: string
    currentSequence: number
    lastUpdated: string
  }>> {
    const stats = await this.executeQuery(
      `SELECT entity_type, financial_year, current_sequence, updated_at 
       FROM ${this.tableName} 
       ORDER BY financial_year DESC, entity_type ASC`
    )

    return stats.map(stat => ({
      entityType: stat.entity_type,
      financialYear: stat.financial_year,
      currentSequence: stat.current_sequence,
      lastUpdated: stat.updated_at
    }))
  }

  /**
   * Validate sequence integrity
   */
  async validateSequenceIntegrity(entityType: string, financialYear?: string): Promise<{
    isValid: boolean
    issues: string[]
    maxUsedSequence: number
    currentSequence: number
  }> {
    const fy = financialYear || this.getCurrentFinancialYear()
    const issues: string[] = []
    
    // Get current sequence
    const currentSequence = await this.getCurrentSequence(entityType, fy)
    
    // Check actual usage based on entity type
    let maxUsedSequence = 0
    let tableName = ''
    let sequenceColumn = ''
    
    switch (entityType) {
      case 'customer':
        tableName = 'customers'
        sequenceColumn = 'customer_code'
        break
      case 'invoice':
        tableName = 'sales'
        sequenceColumn = 'invoice_number'
        break
      case 'repair':
        tableName = 'repairs'
        sequenceColumn = 'repair_number'
        break
      // Add more cases as needed
    }
    
    if (tableName && sequenceColumn) {
      try {
        const result = await this.executeQuery(
          `SELECT MAX(CAST(SUBSTRING_INDEX(${sequenceColumn}, '-', -1) AS UNSIGNED)) as max_sequence 
           FROM ${tableName} 
           WHERE ${sequenceColumn} LIKE ?`,
          [`%-${fy}-%`]
        )
        
        maxUsedSequence = result[0]?.max_sequence || 0
        
        if (maxUsedSequence > currentSequence) {
          issues.push(`Current sequence (${currentSequence}) is behind max used sequence (${maxUsedSequence})`)
        }
      } catch (error) {
        issues.push(`Could not validate against ${tableName} table: ${error}`)
      }
    }
    
    return {
      isValid: issues.length === 0,
      issues,
      maxUsedSequence,
      currentSequence
    }
  }
}
