/**
 * API Route for Professional ID Validation
 * Handles server-side ID validation and parsing
 */

import { NextRequest, NextResponse } from 'next/server'
import {
  validateCustomerId,
  validateInvoiceNumber,
  validateRepairJobNumber,
  parseInventoryItemCode
} from '@/lib/utils/professional-id-generator'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { type, id } = body

    if (!type || !id) {
      return NextResponse.json(
        { success: false, error: 'Missing required parameters: type, id' },
        { status: 400 }
      )
    }

    let isValid = false
    let parsed: any = null

    switch (type) {
      case 'customer':
        isValid = validateCustomerId(id)
        if (isValid) {
          const match = id.match(/^CUST-(\d{4})-(\d{6})$/)
          parsed = {
            year: match?.[1],
            sequence: match?.[2]
          }
        }
        break

      case 'inventory':
        parsed = parseInventoryItemCode(id)
        isValid = parsed.isValid
        break

      case 'invoice':
        isValid = validateInvoiceNumber(id)
        if (isValid) {
          const match = id.match(/^INV-(\d{4})-(\d{2})-(\d{6})$/)
          parsed = {
            year: match?.[1],
            yearSuffix: match?.[2],
            sequence: match?.[3],
            financialYear: `${match?.[1]}-${match?.[2]}`
          }
        }
        break

      case 'repair':
        isValid = validateRepairJobNumber(id)
        if (isValid) {
          const match = id.match(/^REP-(\d{4})-(\d{2})-(\d{6})$/)
          parsed = {
            year: match?.[1],
            yearSuffix: match?.[2],
            sequence: match?.[3],
            financialYear: `${match?.[1]}-${match?.[2]}`
          }
        }
        break

      default:
        return NextResponse.json(
          { success: false, error: `Unknown ID type: ${type}` },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      isValid,
      parsed
    })

  } catch (error) {
    console.error('Professional ID validation error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
