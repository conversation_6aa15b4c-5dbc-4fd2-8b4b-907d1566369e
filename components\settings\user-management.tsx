"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Checkbox } from "@/components/ui/checkbox"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { toast } from "sonner"
import { 
  <PERSON>, Plus, Edit, Trash2, Shield, Key, Eye, EyeOff,
  <PERSON>r<PERSON>heck, UserX, Settings, Lock, Unlock, Crown,
  CheckCircle, XCircle, AlertCircle
} from "lucide-react"

interface User {
  id: string
  name: string
  email: string
  phone: string
  role: string
  permissions: string[]
  isActive: boolean
  lastLogin: string
  createdAt: string
}

interface Role {
  id: string
  name: string
  description: string
  permissions: string[]
  isSystem: boolean
}

export function UserManagement() {
  const [users, setUsers] = useState<User[]>([])
  const [roles, setRoles] = useState<Role[]>([])
  const [activeTab, setActiveTab] = useState("users")
  const [isAddUserOpen, setIsAddUserOpen] = useState(false)
  const [isAddRoleOpen, setIsAddRoleOpen] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [editingRole, setEditingRole] = useState<Role | null>(null)

  // New User Form State
  const [newUser, setNewUser] = useState({
    name: "",
    email: "",
    phone: "",
    password: "",
    confirmPassword: "",
    role: "",
    isActive: true
  })

  // New Role Form State
  const [newRole, setNewRole] = useState({
    name: "",
    description: "",
    permissions: [] as string[]
  })

  // Available Permissions
  const availablePermissions = [
    { id: "sales_view", name: "View Sales", category: "Sales" },
    { id: "sales_create", name: "Create Sales", category: "Sales" },
    { id: "sales_edit", name: "Edit Sales", category: "Sales" },
    { id: "sales_delete", name: "Delete Sales", category: "Sales" },
    { id: "inventory_view", name: "View Inventory", category: "Inventory" },
    { id: "inventory_create", name: "Add Inventory", category: "Inventory" },
    { id: "inventory_edit", name: "Edit Inventory", category: "Inventory" },
    { id: "inventory_delete", name: "Delete Inventory", category: "Inventory" },
    { id: "customers_view", name: "View Customers", category: "Customers" },
    { id: "customers_create", name: "Add Customers", category: "Customers" },
    { id: "customers_edit", name: "Edit Customers", category: "Customers" },
    { id: "customers_delete", name: "Delete Customers", category: "Customers" },
    { id: "repairs_view", name: "View Repairs", category: "Repairs" },
    { id: "repairs_create", name: "Create Repairs", category: "Repairs" },
    { id: "repairs_edit", name: "Edit Repairs", category: "Repairs" },
    { id: "repairs_delete", name: "Delete Repairs", category: "Repairs" },
    { id: "schemes_view", name: "View Schemes", category: "Schemes" },
    { id: "schemes_create", name: "Create Schemes", category: "Schemes" },
    { id: "schemes_edit", name: "Edit Schemes", category: "Schemes" },
    { id: "schemes_delete", name: "Delete Schemes", category: "Schemes" },
    { id: "purchases_view", name: "View Purchases", category: "Purchases" },
    { id: "purchases_create", name: "Create Purchases", category: "Purchases" },
    { id: "purchases_edit", name: "Edit Purchases", category: "Purchases" },
    { id: "purchases_delete", name: "Delete Purchases", category: "Purchases" },
    { id: "reports_view", name: "View Reports", category: "Reports" },
    { id: "reports_export", name: "Export Reports", category: "Reports" },
    { id: "settings_view", name: "View Settings", category: "Settings" },
    { id: "settings_edit", name: "Edit Settings", category: "Settings" },
    { id: "users_view", name: "View Users", category: "User Management" },
    { id: "users_create", name: "Create Users", category: "User Management" },
    { id: "users_edit", name: "Edit Users", category: "User Management" },
    { id: "users_delete", name: "Delete Users", category: "User Management" },
    { id: "system_admin", name: "System Administration", category: "System" }
  ]

  // Initialize with default system roles
  useEffect(() => {
    // Load users from API in production
    setUsers([])

    // Initialize with default system roles
    setRoles([
      {
        id: "1",
        name: "Super Admin",
        description: "Full system access with all permissions",
        permissions: ["system_admin"],
        isSystem: true
      },
      {
        id: "2",
        name: "Manager",
        description: "Management level access to most features",
        permissions: ["sales_view", "sales_create", "sales_edit", "inventory_view", "inventory_create", "customers_view", "customers_create", "reports_view"],
        isSystem: false
      },
      {
        id: "3",
        name: "Sales Staff",
        description: "Sales focused permissions for daily operations",
        permissions: ["sales_view", "sales_create", "customers_view", "customers_create", "inventory_view"],
        isSystem: false
      },
      {
        id: "4",
        name: "Accountant",
        description: "Financial and reporting access",
        permissions: ["sales_view", "purchases_view", "reports_view", "reports_export"],
        isSystem: false
      }
    ])
  }, [])

  const handleAddUser = () => {
    if (!newUser.name || !newUser.email || !newUser.password || !newUser.role) {
      toast.error("Please fill in all required fields")
      return
    }

    if (newUser.password !== newUser.confirmPassword) {
      toast.error("Passwords do not match")
      return
    }

    const selectedRole = roles.find(role => role.name === newUser.role)
    const user: User = {
      id: Date.now().toString(),
      name: newUser.name,
      email: newUser.email,
      phone: newUser.phone,
      role: newUser.role,
      permissions: selectedRole?.permissions || [],
      isActive: newUser.isActive,
      lastLogin: "",
      createdAt: new Date().toISOString()
    }

    setUsers([...users, user])
    setNewUser({
      name: "",
      email: "",
      phone: "",
      password: "",
      confirmPassword: "",
      role: "",
      isActive: true
    })
    setIsAddUserOpen(false)
    toast.success("User added successfully!")
  }

  const handleEditUser = (user: User) => {
    setEditingUser(user)
    setNewUser({
      name: user.name,
      email: user.email,
      phone: user.phone,
      password: "",
      confirmPassword: "",
      role: user.role,
      isActive: user.isActive
    })
    setIsAddUserOpen(true)
  }

  const handleDeleteUser = (userId: string) => {
    setUsers(users.filter(user => user.id !== userId))
    toast.success("User deleted successfully!")
  }

  const handleToggleUserStatus = (userId: string) => {
    setUsers(users.map(user => 
      user.id === userId ? { ...user, isActive: !user.isActive } : user
    ))
    toast.success("User status updated!")
  }

  const handleAddRole = () => {
    if (!newRole.name || !newRole.description) {
      toast.error("Please fill in all required fields")
      return
    }

    const role: Role = {
      id: Date.now().toString(),
      name: newRole.name,
      description: newRole.description,
      permissions: newRole.permissions,
      isSystem: false
    }

    setRoles([...roles, role])
    setNewRole({
      name: "",
      description: "",
      permissions: []
    })
    setIsAddRoleOpen(false)
    toast.success("Role added successfully!")
  }

  const handlePermissionToggle = (permissionId: string, checked: boolean) => {
    if (checked) {
      setNewRole({
        ...newRole,
        permissions: [...newRole.permissions, permissionId]
      })
    } else {
      setNewRole({
        ...newRole,
        permissions: newRole.permissions.filter(p => p !== permissionId)
      })
    }
  }

  const getPermissionsByCategory = () => {
    const categories = {}
    availablePermissions.forEach(permission => {
      if (!categories[permission.category]) {
        categories[permission.category] = []
      }
      categories[permission.category].push(permission)
    })
    return categories
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">User Management</h2>
          <p className="text-muted-foreground">Manage users, roles, and permissions</p>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="users" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Users
          </TabsTrigger>
          <TabsTrigger value="roles" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Roles & Permissions
          </TabsTrigger>
        </TabsList>

        {/* Users Tab */}
        <TabsContent value="users" className="space-y-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  System Users
                </CardTitle>
                <CardDescription>Manage user accounts and access</CardDescription>
              </div>
              <Dialog open={isAddUserOpen} onOpenChange={setIsAddUserOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Add User
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-md">
                  <DialogHeader>
                    <DialogTitle>{editingUser ? "Edit User" : "Add New User"}</DialogTitle>
                    <DialogDescription>
                      {editingUser ? "Update user information" : "Create a new user account"}
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="userName">Full Name *</Label>
                      <Input
                        id="userName"
                        value={newUser.name}
                        onChange={(e) => setNewUser({...newUser, name: e.target.value})}
                        placeholder="John Doe"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="userEmail">Email Address *</Label>
                      <Input
                        id="userEmail"
                        type="email"
                        value={newUser.email}
                        onChange={(e) => setNewUser({...newUser, email: e.target.value})}
                        placeholder="<EMAIL>"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="userPhone">Phone Number</Label>
                      <Input
                        id="userPhone"
                        value={newUser.phone}
                        onChange={(e) => setNewUser({...newUser, phone: e.target.value})}
                        placeholder="+91 98765 43210"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="userRole">Role *</Label>
                      <Select value={newUser.role} onValueChange={(value) => setNewUser({...newUser, role: value})}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select role" />
                        </SelectTrigger>
                        <SelectContent>
                          {roles.map((role) => (
                            <SelectItem key={role.id} value={role.name}>
                              {role.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    {!editingUser && (
                      <>
                        <div className="space-y-2">
                          <Label htmlFor="userPassword">Password *</Label>
                          <Input
                            id="userPassword"
                            type="password"
                            value={newUser.password}
                            onChange={(e) => setNewUser({...newUser, password: e.target.value})}
                            placeholder="Enter password"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="confirmPassword">Confirm Password *</Label>
                          <Input
                            id="confirmPassword"
                            type="password"
                            value={newUser.confirmPassword}
                            onChange={(e) => setNewUser({...newUser, confirmPassword: e.target.value})}
                            placeholder="Confirm password"
                          />
                        </div>
                      </>
                    )}
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="userActive"
                        checked={newUser.isActive}
                        onCheckedChange={(checked) => setNewUser({...newUser, isActive: checked as boolean})}
                      />
                      <Label htmlFor="userActive">Active User</Label>
                    </div>
                    <div className="flex justify-end space-x-2">
                      <Button variant="outline" onClick={() => setIsAddUserOpen(false)}>
                        Cancel
                      </Button>
                      <Button onClick={handleAddUser}>
                        {editingUser ? "Update User" : "Add User"}
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Last Login</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{user.name}</div>
                          <div className="text-sm text-muted-foreground">{user.email}</div>
                          {user.phone && (
                            <div className="text-sm text-muted-foreground">{user.phone}</div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={user.role === "Super Admin" ? "default" : "secondary"}>
                          {user.role}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {user.isActive ? (
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          ) : (
                            <XCircle className="h-4 w-4 text-red-500" />
                          )}
                          <span className={user.isActive ? "text-green-600" : "text-red-600"}>
                            {user.isActive ? "Active" : "Inactive"}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {user.lastLogin ? (
                          <div className="text-sm">
                            {new Date(user.lastLogin).toLocaleDateString()}
                            <br />
                            <span className="text-muted-foreground">
                              {new Date(user.lastLogin).toLocaleTimeString()}
                            </span>
                          </div>
                        ) : (
                          <span className="text-muted-foreground">Never</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditUser(user)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleToggleUserStatus(user.id)}
                          >
                            {user.isActive ? (
                              <UserX className="h-4 w-4" />
                            ) : (
                              <UserCheck className="h-4 w-4" />
                            )}
                          </Button>
                          {user.role !== "Super Admin" && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteUser(user.id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Roles Tab */}
        <TabsContent value="roles" className="space-y-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Roles & Permissions
                </CardTitle>
                <CardDescription>Manage user roles and their permissions</CardDescription>
              </div>
              <Dialog open={isAddRoleOpen} onOpenChange={setIsAddRoleOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Role
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>Add New Role</DialogTitle>
                    <DialogDescription>
                      Create a new role with specific permissions
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="roleName">Role Name *</Label>
                      <Input
                        id="roleName"
                        value={newRole.name}
                        onChange={(e) => setNewRole({...newRole, name: e.target.value})}
                        placeholder="Sales Manager"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="roleDescription">Description *</Label>
                      <Input
                        id="roleDescription"
                        value={newRole.description}
                        onChange={(e) => setNewRole({...newRole, description: e.target.value})}
                        placeholder="Manages sales operations and staff"
                      />
                    </div>
                    <div className="space-y-4">
                      <Label>Permissions</Label>
                      {Object.entries(getPermissionsByCategory()).map(([category, permissions]) => (
                        <div key={category} className="space-y-2">
                          <h4 className="font-medium text-sm">{category}</h4>
                          <div className="grid grid-cols-2 gap-2 pl-4">
                            {permissions.map((permission) => (
                              <div key={permission.id} className="flex items-center space-x-2">
                                <Checkbox
                                  id={permission.id}
                                  checked={newRole.permissions.includes(permission.id)}
                                  onCheckedChange={(checked) => handlePermissionToggle(permission.id, checked as boolean)}
                                />
                                <Label htmlFor={permission.id} className="text-sm">
                                  {permission.name}
                                </Label>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="flex justify-end space-x-2">
                      <Button variant="outline" onClick={() => setIsAddRoleOpen(false)}>
                        Cancel
                      </Button>
                      <Button onClick={handleAddRole}>
                        Add Role
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                {roles.map((role) => (
                  <Card key={role.id} className="relative">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle className="flex items-center gap-2">
                          {role.isSystem ? (
                            <Crown className="h-4 w-4 text-yellow-500" />
                          ) : (
                            <Shield className="h-4 w-4" />
                          )}
                          {role.name}
                        </CardTitle>
                        {role.isSystem && (
                          <Badge variant="outline">System Role</Badge>
                        )}
                      </div>
                      <CardDescription>{role.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <Label className="text-sm font-medium">Permissions ({role.permissions.length})</Label>
                        <div className="flex flex-wrap gap-1">
                          {role.permissions.slice(0, 6).map((permissionId) => {
                            const permission = availablePermissions.find(p => p.id === permissionId)
                            return permission ? (
                              <Badge key={permissionId} variant="secondary" className="text-xs">
                                {permission.name}
                              </Badge>
                            ) : null
                          })}
                          {role.permissions.length > 6 && (
                            <Badge variant="outline" className="text-xs">
                              +{role.permissions.length - 6} more
                            </Badge>
                          )}
                        </div>
                      </div>
                      {!role.isSystem && (
                        <div className="flex justify-end mt-4 space-x-2">
                          <Button variant="ghost" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
