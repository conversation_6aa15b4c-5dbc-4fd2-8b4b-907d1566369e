-- MYSQL 8.0+ COMPATIBLE PROFESSIONAL SAMPLE DATA
-- Version: 5.0.0 - Professional ID Standards
-- Date: January 31, 2025
-- Description: Sample data with professional business IDs (MySQL 8.0+ compatible)

USE jewellers_db;

-- Disable foreign key checks temporarily
SET FOREIGN_KEY_CHECKS = 0;

-- ============================================================================
-- 1. INITIALIZE ID SEQUENCES
-- ============================================================================

INSERT INTO id_sequences (id, entity_type, current_sequence, financial_year, created_at, updated_at) VALUES
('seq_001', 'customer', 5, '2024-25', NOW(), NOW()),
('seq_002', 'inventory', 5, '2024-25', NOW(), NOW()),
('seq_003', 'sale', 3, '2024-25', NOW(), NOW()),
('seq_004', 'invoice', 3, '2024-25', NOW(), NOW()),
('seq_005', 'repair', 3, '2024-25', NOW(), NOW()),
('seq_006', 'scheme', 3, '2024-25', NOW(), NOW()),
('seq_007', 'purchase', 3, '2024-25', NOW(), NOW()),
('seq_008', 'supplier', 3, '2024-25', NOW(), NOW()),
('seq_009', 'exchange', 2, '2024-25', NOW(), NOW()),
('seq_010', 'receipt', 10, '2024-25', NOW(), NOW()),
('seq_011', 'estimate', 5, '2024-25', NOW(), NOW());

-- ============================================================================
-- 2. BUSINESS SETTINGS
-- ============================================================================

INSERT INTO business_settings (
  business_name, business_type, address, city, state, pincode, country,
  phone, email, website, gst_number, pan_number, established_year,
  cgst_rate, sgst_rate, igst_rate, currency_symbol, currency_code
) VALUES (
  'Shree Jewellers', 'jewelry_store', 
  '123 Main Street, Commercial Complex, Zaveri Bazaar', 'Mumbai', 'Maharashtra', '400002', 'India',
  '+91 98765 43210', '<EMAIL>', 'www.shreejewellers.com',
  '27**********1Z5', '**********', 2020,
  1.50, 1.50, 3.00, '₹', 'INR'
);

-- ============================================================================
-- 3. USERS - Professional user codes
-- ============================================================================

INSERT INTO users (id, user_code, name, email, phone, password_hash, role, is_active) VALUES
('user_001', 'USER-2024-0001', 'Admin User', '<EMAIL>', '+91 98765 43210', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'super_admin', true),
('user_002', 'USER-2024-0002', 'Manager User', '<EMAIL>', '+91 98765 43211', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'manager', true),
('user_003', 'USER-2024-0003', 'Sales Staff', '<EMAIL>', '+91 98765 43212', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'sales_staff', true);

-- ============================================================================
-- 4. METAL RATES
-- ============================================================================

INSERT INTO metal_rates (id, metal_type, purity, rate_per_gram, margin_percentage, effective_date, is_active) VALUES
('rate_001', 'gold', '24K', 7200.00, 5.00, CURDATE(), true),
('rate_002', 'gold', '22K', 6600.00, 5.00, CURDATE(), true),
('rate_003', 'gold', '18K', 5400.00, 5.00, CURDATE(), true),
('rate_004', 'silver', '999', 85.00, 8.00, CURDATE(), true),
('rate_005', 'silver', '925', 78.00, 8.00, CURDATE(), true),
('rate_006', 'platinum', '950', 3200.00, 3.00, CURDATE(), true);

-- ============================================================================
-- 5. INVENTORY CATEGORIES
-- ============================================================================

INSERT INTO inventory_categories (id, name, description, hsn_code, tax_rate, is_active) VALUES
('cat_001', 'Rings', 'Gold, Silver, and Diamond Rings', '71131900', 3.00, true),
('cat_002', 'Necklaces', 'Traditional and Modern Necklaces', '71131900', 3.00, true),
('cat_003', 'Earrings', 'Studs, Hoops, and Danglers', '71131900', 3.00, true),
('cat_004', 'Bracelets', 'Gold and Silver Bracelets', '71131900', 3.00, true),
('cat_005', 'Chains', 'Gold and Silver Chains', '71131900', 3.00, true),
('cat_006', 'Bangles', 'Traditional Indian Bangles', '71131900', 3.00, true),
('cat_007', 'Sets', 'Complete Jewelry Sets', '71131900', 3.00, true),
('cat_008', 'Diamonds', 'Loose Diamonds and Diamond Jewelry', '71023100', 0.25, true);

-- ============================================================================
-- 6. CUSTOMERS - Professional customer codes
-- ============================================================================

INSERT INTO customers (
  id, customer_code, name, phone, alternate_phone, email, date_of_birth, anniversary_date, gender,
  address, city, state, pincode, country, aadhar_number, pan_number,
  customer_type, is_active, total_purchases, loyalty_points
) VALUES
('cust_001', 'CUST-2024-000001', 'Rajesh Kumar', '9876543210', '9876543211', '<EMAIL>', '1985-05-15', '2010-12-01', 'male',
 '123 Residential Complex, Andheri', 'Mumbai', 'Maharashtra', '400058', 'India', '123456789012', '**********',
 'premium', true, 250000.00, 2500),

('cust_002', 'CUST-2024-000002', 'Priya Sharma', '9876543220', NULL, '<EMAIL>', '1990-08-22', '2015-02-14', 'female',
 '456 Housing Society, Bandra', 'Mumbai', 'Maharashtra', '400050', 'India', '123456789013', '**********',
 'regular', true, 150000.00, 1500),

('cust_003', 'CUST-2024-000003', 'Amit Patel', '9876543230', '9876543231', '<EMAIL>', '1982-03-10', '2008-11-25', 'male',
 '789 Commercial Street, Borivali', 'Mumbai', 'Maharashtra', '400092', 'India', '123456789014', '**********',
 'vip', true, 500000.00, 5000),

('cust_004', 'CUST-2024-000004', 'Sunita Joshi', '9876543240', NULL, '<EMAIL>', '1988-07-18', '2012-04-05', 'female',
 '321 Apartment Complex, Thane', 'Thane', 'Maharashtra', '400601', 'India', '123456789015', '**********',
 'regular', true, 75000.00, 750),

('cust_005', 'CUST-2024-000005', 'Vikram Singh', '9876543250', '9876543251', '<EMAIL>', '1975-12-05', '2000-06-15', 'male',
 '654 Villa Complex, Juhu', 'Mumbai', 'Maharashtra', '400049', 'India', '123456789016', '**********',
 'vip', true, 800000.00, 8000);

-- ============================================================================
-- 7. SUPPLIERS - Professional supplier codes
-- ============================================================================

INSERT INTO suppliers (
  id, supplier_code, name, contact_person, phone, email,
  address, city, state, country, gst_number, is_active
) VALUES
('supp_001', 'SUPP-2024-0001', 'Rajesh Gold Suppliers', 'Rajesh Kumar', '+91 98765 43210', '<EMAIL>',
 '123 Jewelry Market, Zaveri Bazaar', 'Mumbai', 'Maharashtra', 'India', '27**********1Z5', true),

('supp_002', 'SUPP-2024-0002', 'Diamond House Pvt Ltd', 'Suresh Patel', '+91 98765 43220', '<EMAIL>',
 '456 Diamond District, Varachha', 'Surat', 'Gujarat', 'India', '24**********2L6', true),

('supp_003', 'SUPP-2024-0003', 'Silver Craft Industries', 'Amit Sharma', '+91 98765 43230', '<EMAIL>',
 '789 Silver Market, Johari Bazaar', 'Jaipur', 'Rajasthan', 'India', '08MNOPQ9012R3S7', true);

-- ============================================================================
-- 8. INVENTORY - Professional item codes and barcodes
-- ============================================================================

INSERT INTO inventory (
  id, barcode, name, description, category_id, metal_type, purity,
  gross_weight, stone_weight, stone_details, stone_amount,
  making_charges, other_charges, current_value, selling_price, mrp,
  stock, status, hsn_code
) VALUES
('RGGD22-2024-0001', 'SJ01224000001', 'Gold Ring with Diamond', 'Beautiful 22K gold ring with solitaire diamond', 'cat_001', 'gold', '22K',
 8.500, 0.250, '1 Solitaire Diamond, 0.25ct, VS1, F Color', 25000.00,
 8500.00, 500.00, 81200.00, 85000.00, 90000.00,
 1, 'active', '71131900'),

('CHGD22-2024-0002', 'SJ06224000002', '22K Gold Chain', 'Traditional 22K gold chain for daily wear', 'cat_005', 'gold', '22K',
 25.000, 0.000, NULL, 0.00,
 12500.00, 1000.00, 178500.00, 185000.00, 195000.00,
 2, 'active', '71131900'),

('BGSL925-2024-0003', 'SJ07925000003', 'Silver Bangles Set', 'Set of 4 traditional silver bangles', 'cat_006', 'silver', '925',
 120.000, 0.000, NULL, 0.00,
 2400.00, 200.00, 11960.00, 12500.00, 13500.00,
 3, 'active', '71131100'),

('ERDM18-2024-0004', 'SJ03118000004', 'Diamond Earrings', 'Elegant diamond stud earrings in 18K gold', 'cat_003', 'gold', '18K',
 6.500, 1.200, '2 Diamonds, 0.6ct each, VVS1, D Color', 120000.00,
 15000.00, 2000.00, 172100.00, 180000.00, 195000.00,
 1, 'active', '71023100'),

('STGD22-2024-0005', 'SJ08224000005', 'Gold Necklace Set', 'Complete necklace set with matching earrings', 'cat_007', 'gold', '22K',
 45.000, 2.500, 'Ruby and Emerald stones', 35000.00,
 22500.00, 3000.00, 358000.00, 375000.00, 395000.00,
 1, 'active', '71131900');

-- ============================================================================
-- 9. SALES - Professional sale and invoice numbers
-- ============================================================================

INSERT INTO sales (
  id, sale_number, invoice_number, customer_id, sale_date,
  subtotal, discount_amount, cgst_amount, sgst_amount,
  total_amount, paid_amount, payment_method, status, created_by
) VALUES
('sale_001', 'SALE-2024-000001', 'INV-2024-25-000001', 'cust_001', '2024-01-15',
 82000.00, 2000.00, 1200.00, 1200.00,
 82400.00, 82400.00, 'cash', 'completed', 'user_002'),

('sale_002', 'SALE-2024-000002', 'INV-2024-25-000002', 'cust_002', '2024-01-20',
 180000.00, 5000.00, 2625.00, 2625.00,
 180250.00, 180250.00, 'card', 'completed', 'user_003'),

('sale_003', 'SALE-2024-000003', 'INV-2024-25-000003', 'cust_003', '2024-01-25',
 175000.00, 0.00, 2625.00, 2625.00,
 180250.00, 150000.00, 'mixed', 'completed', 'user_002');

-- ============================================================================
-- 10. SCHEMES - Professional scheme numbers
-- ============================================================================

INSERT INTO schemes (
  id, scheme_number, customer_id, scheme_name, scheme_type,
  total_amount, monthly_amount, paid_amount, duration_months,
  start_date, maturity_date, status
) VALUES
('scheme_001', 'SCH-2024-25-000001', 'cust_001', 'Gold Savings Scheme', 'monthly',
 120000.00, 10000.00, 30000.00, 12,
 '2024-01-01', '2024-12-31', 'active'),

('scheme_002', 'SCH-2024-25-000002', 'cust_002', 'Festival Special Scheme', 'advance',
 60000.00, 0.00, 60000.00, 6,
 '2024-02-01', '2024-07-31', 'active'),

('scheme_003', 'SCH-2024-25-000003', 'cust_004', 'Monthly Gold Plan', 'monthly',
 84000.00, 7000.00, 14000.00, 12,
 '2024-01-15', '2025-01-14', 'active');

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- ============================================================================
-- VERIFICATION
-- ============================================================================

SELECT '✅ Professional sample data imported successfully!' as import_status;

-- Show professional ID examples
SELECT 'Professional Customer IDs' as entity_type, customer_code as professional_id, name 
FROM customers ORDER BY customer_code LIMIT 5;

SELECT 'Professional Inventory Codes' as entity_type, id as professional_id, barcode, name 
FROM inventory ORDER BY id LIMIT 5;

SELECT 'Professional Sales & Invoices' as entity_type, CONCAT(sale_number, ' | ', invoice_number) as professional_id, total_amount 
FROM sales ORDER BY sale_number LIMIT 3;

SELECT 'Current Sequences' as info, entity_type, current_sequence, financial_year 
FROM id_sequences ORDER BY entity_type;

SELECT 
  '🎉 PROFESSIONAL ID SYSTEM READY!' as message,
  'Login: <EMAIL> / admin123' as credentials,
  'All IDs follow jewelry business standards' as note;
