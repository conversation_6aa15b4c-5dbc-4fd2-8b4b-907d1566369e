"use client"

import { useState, useMemo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useStore } from "@/lib/store"
import { 
  Download, TrendingUp, TrendingDown, Target, Award,
  BarChart3, PieChart, Activity, Users, ShoppingCart,
  Package, Wrench, Coins, Building, AlertTriangle
} from "lucide-react"

export function BusinessIntelligence() {
  const { sales, customers, inventory, repairs, schemes, purchases } = useStore()
  const [activeTab, setActiveTab] = useState("trends")
  const [analysisType, setAnalysisType] = useState("comprehensive")
  const [timeframe, setTimeframe] = useState("6months")

  // Advanced business intelligence calculations
  const businessIntelligence = useMemo(() => {
    const now = new Date()
    const sixMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 6, 1)
    const threeMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 3, 1)
    const oneMonthAgo = new Date(now.getFullYear(), now.getMonth() - 1, 1)

    // Trend Analysis
    const recentSales = sales.filter(sale => new Date(sale.date) >= sixMonthsAgo)
    const monthlyTrends = {}
    
    for (let i = 5; i >= 0; i--) {
      const monthStart = new Date(now.getFullYear(), now.getMonth() - i, 1)
      const monthEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 0)
      const monthKey = monthStart.toISOString().slice(0, 7)
      
      const monthSales = recentSales.filter(sale => {
        const saleDate = new Date(sale.date)
        return saleDate >= monthStart && saleDate <= monthEnd
      })
      
      monthlyTrends[monthKey] = {
        revenue: monthSales.reduce((sum, sale) => sum + (sale.total || 0), 0),
        transactions: monthSales.length,
        customers: new Set(monthSales.map(sale => sale.customer.id)).size,
        avgTransactionValue: monthSales.length > 0 ? monthSales.reduce((sum, sale) => sum + (sale.total || 0), 0) / monthSales.length : 0
      }
    }

    // Customer Segmentation Analysis
    const customerAnalysis = customers.map(customer => {
      const customerSales = sales.filter(sale => sale.customer.id === customer.id)
      const totalSpent = customerSales.reduce((sum, sale) => sum + (sale.total || 0), 0)
      const transactionCount = customerSales.length
      const avgTransactionValue = transactionCount > 0 ? totalSpent / transactionCount : 0
      const lastPurchase = customerSales.length > 0 ? Math.max(...customerSales.map(sale => new Date(sale.date).getTime())) : 0
      const daysSinceLastPurchase = lastPurchase > 0 ? Math.floor((now.getTime() - lastPurchase) / (1000 * 60 * 60 * 24)) : 999
      
      // Customer Segmentation
      let segment = "New"
      if (totalSpent > 500000) segment = "VIP"
      else if (totalSpent > 200000) segment = "Premium"
      else if (totalSpent > 50000) segment = "Regular"
      else if (transactionCount > 0) segment = "Occasional"
      
      return {
        ...customer,
        totalSpent,
        transactionCount,
        avgTransactionValue,
        daysSinceLastPurchase,
        segment,
        lifetimeValue: totalSpent + (totalSpent * 0.3) // Estimated future value
      }
    })

    const segmentDistribution = customerAnalysis.reduce((acc, customer) => {
      acc[customer.segment] = (acc[customer.segment] || 0) + 1
      return acc
    }, {})

    // Product Performance Analysis
    const productPerformance = {}
    sales.forEach(sale => {
      sale.items.forEach(item => {
        const category = item.item.category || "Unknown"
        if (!productPerformance[category]) {
          productPerformance[category] = {
            revenue: 0,
            quantity: 0,
            transactions: 0,
            avgPrice: 0,
            profitMargin: 0.25 // Assumed 25% margin
          }
        }
        productPerformance[category].revenue += item.amount || 0
        productPerformance[category].quantity += 1
        productPerformance[category].transactions += 1
      })
    })

    Object.keys(productPerformance).forEach(category => {
      const perf = productPerformance[category]
      perf.avgPrice = perf.quantity > 0 ? perf.revenue / perf.quantity : 0
      perf.profit = perf.revenue * perf.profitMargin
    })

    // Seasonal Analysis
    const seasonalAnalysis = {
      Q1: { months: ["01", "02", "03"], revenue: 0, transactions: 0 },
      Q2: { months: ["04", "05", "06"], revenue: 0, transactions: 0 },
      Q3: { months: ["07", "08", "09"], revenue: 0, transactions: 0 },
      Q4: { months: ["10", "11", "12"], revenue: 0, transactions: 0 }
    }

    sales.forEach(sale => {
      const month = new Date(sale.date).getMonth() + 1
      const monthStr = month.toString().padStart(2, '0')
      const quarter = Object.keys(seasonalAnalysis).find(q => 
        seasonalAnalysis[q].months.includes(monthStr)
      )
      if (quarter) {
        seasonalAnalysis[quarter].revenue += sale.total || 0
        seasonalAnalysis[quarter].transactions += 1
      }
    })

    // Predictive Analytics (Simple trend-based forecasting)
    const revenueValues = Object.values(monthlyTrends).map(trend => trend.revenue)
    const avgGrowthRate = revenueValues.length > 1 ? 
      revenueValues.slice(1).reduce((sum, val, idx) => {
        const prevVal = revenueValues[idx]
        return sum + (prevVal > 0 ? (val - prevVal) / prevVal : 0)
      }, 0) / (revenueValues.length - 1) : 0

    const currentRevenue = revenueValues[revenueValues.length - 1] || 0
    const forecastedRevenue = {
      nextMonth: currentRevenue * (1 + avgGrowthRate),
      next3Months: currentRevenue * Math.pow(1 + avgGrowthRate, 3),
      next6Months: currentRevenue * Math.pow(1 + avgGrowthRate, 6)
    }

    // Risk Analysis
    const risks = []
    
    // Inventory risks
    const lowStockItems = inventory.filter(item => (item.stock || 0) <= (item.minStock || 5)).length
    if (lowStockItems > 10) risks.push({ type: "inventory", severity: "high", message: `${lowStockItems} items critically low in stock` })
    
    // Customer concentration risk
    const topCustomerRevenue = Math.max(...customerAnalysis.map(c => c.totalSpent))
    const totalRevenue = sales.reduce((sum, sale) => sum + (sale.total || 0), 0)
    const concentrationRisk = totalRevenue > 0 ? (topCustomerRevenue / totalRevenue) * 100 : 0
    if (concentrationRisk > 30) risks.push({ type: "customer", severity: "medium", message: `Top customer represents ${concentrationRisk.toFixed(1)}% of revenue` })
    
    // Cash flow risk
    const pendingRepairs = repairs.filter(repair => repair.status === "pending" || repair.status === "in_progress").length
    if (pendingRepairs > 20) risks.push({ type: "operations", severity: "medium", message: `${pendingRepairs} pending repairs may affect cash flow` })

    // Opportunities
    const opportunities = []
    
    // High-value inactive customers
    const inactiveVIPs = customerAnalysis.filter(c => c.segment === "VIP" && c.daysSinceLastPurchase > 90).length
    if (inactiveVIPs > 0) opportunities.push({ type: "customer", message: `${inactiveVIPs} VIP customers haven't purchased in 90+ days` })
    
    // Growing categories
    const growingCategories = Object.entries(productPerformance)
      .filter(([category, perf]) => perf.revenue > 100000)
      .sort(([,a], [,b]) => b.revenue - a.revenue)
      .slice(0, 3)
    if (growingCategories.length > 0) {
      opportunities.push({ type: "product", message: `Top performing categories: ${growingCategories.map(([cat]) => cat).join(", ")}` })
    }

    return {
      monthlyTrends,
      customerAnalysis: customerAnalysis.sort((a, b) => b.totalSpent - a.totalSpent).slice(0, 20),
      segmentDistribution,
      productPerformance,
      seasonalAnalysis,
      forecastedRevenue,
      risks,
      opportunities,
      avgGrowthRate: avgGrowthRate * 100,
      totalCustomers: customers.length,
      activeCustomers: customerAnalysis.filter(c => c.daysSinceLastPurchase <= 90).length,
      customerRetentionRate: customers.length > 0 ? (customerAnalysis.filter(c => c.transactionCount > 1).length / customers.length) * 100 : 0
    }
  }, [sales, customers, inventory, repairs, schemes, purchases, timeframe])

  const exportAnalysis = () => {
    const analysisData = {
      analysisType,
      timeframe,
      businessIntelligence,
      generatedAt: new Date().toISOString()
    }
    
    const dataStr = JSON.stringify(analysisData, null, 2)
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)
    
    const exportFileDefaultName = `business-intelligence-${new Date().toISOString().split('T')[0]}.json`
    
    const linkElement = document.createElement('a')
    linkElement.setAttribute('href', dataUri)
    linkElement.setAttribute('download', exportFileDefaultName)
    linkElement.click()
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Business Intelligence</h2>
          <p className="text-muted-foreground">Advanced analytics, trends, and predictive insights</p>
        </div>
        <div className="flex items-center gap-4">
          <Select value={timeframe} onValueChange={setTimeframe}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="3months">3 Months</SelectItem>
              <SelectItem value="6months">6 Months</SelectItem>
              <SelectItem value="12months">12 Months</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={exportAnalysis}>
            <Download className="h-4 w-4 mr-2" />
            Export Analysis
          </Button>
        </div>
      </div>

      {/* Risk & Opportunity Alerts */}
      {(businessIntelligence.risks.length > 0 || businessIntelligence.opportunities.length > 0) && (
        <div className="grid gap-4 md:grid-cols-2">
          {businessIntelligence.risks.length > 0 && (
            <Card className="border-red-200 bg-red-50">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-red-900">
                  <AlertTriangle className="h-5 w-5" />
                  Risk Alerts
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {businessIntelligence.risks.map((risk, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <Badge variant={risk.severity === "high" ? "destructive" : "secondary"}>
                        {risk.severity}
                      </Badge>
                      <span className="text-sm">{risk.message}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {businessIntelligence.opportunities.length > 0 && (
            <Card className="border-green-200 bg-green-50">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-green-900">
                  <Target className="h-5 w-5" />
                  Growth Opportunities
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {businessIntelligence.opportunities.map((opportunity, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <Badge variant="outline" className="border-green-500 text-green-700">
                        {opportunity.type}
                      </Badge>
                      <span className="text-sm">{opportunity.message}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="trends" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Trends
          </TabsTrigger>
          <TabsTrigger value="customers" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Customer Intelligence
          </TabsTrigger>
          <TabsTrigger value="products" className="flex items-center gap-2">
            <Package className="h-4 w-4" />
            Product Analytics
          </TabsTrigger>
          <TabsTrigger value="forecasting" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Forecasting
          </TabsTrigger>
        </TabsList>

        {/* Trends Tab */}
        <TabsContent value="trends" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Revenue Growth</CardTitle>
                <CardDescription>Monthly revenue trend analysis</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center mb-4">
                  <div className="text-3xl font-bold text-green-600">
                    {businessIntelligence.avgGrowthRate > 0 ? "+" : ""}{businessIntelligence.avgGrowthRate.toFixed(1)}%
                  </div>
                  <p className="text-sm text-muted-foreground">Average Monthly Growth</p>
                </div>
                <div className="space-y-2">
                  {Object.entries(businessIntelligence.monthlyTrends).slice(-3).map(([month, data]) => (
                    <div key={month} className="flex justify-between text-sm">
                      <span>{new Date(month + "-01").toLocaleDateString('en-US', { month: 'short', year: 'numeric' })}</span>
                      <span className="font-medium">₹{data.revenue.toLocaleString()}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Customer Metrics</CardTitle>
                <CardDescription>Customer acquisition and retention</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Customer Retention</span>
                      <span>{businessIntelligence.customerRetentionRate.toFixed(1)}%</span>
                    </div>
                    <Progress value={businessIntelligence.customerRetentionRate} className="h-2" />
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Total Customers</span>
                      <span className="font-medium">{businessIntelligence.totalCustomers}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Active Customers</span>
                      <span className="font-medium text-green-600">{businessIntelligence.activeCustomers}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Seasonal Performance</CardTitle>
                <CardDescription>Quarterly revenue distribution</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(businessIntelligence.seasonalAnalysis).map(([quarter, data]) => (
                    <div key={quarter}>
                      <div className="flex justify-between text-sm mb-1">
                        <span>{quarter}</span>
                        <span>₹{data.revenue.toLocaleString()}</span>
                      </div>
                      <Progress
                        value={(data.revenue / Math.max(...Object.values(businessIntelligence.seasonalAnalysis).map(d => d.revenue))) * 100}
                        className="h-2"
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Monthly Performance Trends</CardTitle>
              <CardDescription>Detailed monthly breakdown of key metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Month</TableHead>
                    <TableHead>Revenue</TableHead>
                    <TableHead>Transactions</TableHead>
                    <TableHead>Customers</TableHead>
                    <TableHead>Avg Transaction</TableHead>
                    <TableHead>Growth</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {Object.entries(businessIntelligence.monthlyTrends).map(([month, data], index, array) => {
                    const prevData = index > 0 ? array[index - 1][1] : null
                    const growth = prevData && prevData.revenue > 0 ?
                      ((data.revenue - prevData.revenue) / prevData.revenue) * 100 : 0

                    return (
                      <TableRow key={month}>
                        <TableCell>{new Date(month + "-01").toLocaleDateString('en-US', { month: 'short', year: 'numeric' })}</TableCell>
                        <TableCell className="font-medium">₹{data.revenue.toLocaleString()}</TableCell>
                        <TableCell>{data.transactions}</TableCell>
                        <TableCell>{data.customers}</TableCell>
                        <TableCell>₹{data.avgTransactionValue.toLocaleString()}</TableCell>
                        <TableCell>
                          {index > 0 && (
                            <Badge variant={growth > 0 ? "default" : growth < 0 ? "destructive" : "secondary"}>
                              {growth > 0 ? "+" : ""}{growth.toFixed(1)}%
                            </Badge>
                          )}
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Customer Intelligence Tab */}
        <TabsContent value="customers" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Customer Segmentation</CardTitle>
                <CardDescription>Customer distribution by value segments</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries(businessIntelligence.segmentDistribution).map(([segment, count]) => (
                    <div key={segment}>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="capitalize">{segment}</span>
                        <span>{count} customers</span>
                      </div>
                      <Progress value={(count / businessIntelligence.totalCustomers) * 100} className="h-2" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Customer Lifetime Value</CardTitle>
                <CardDescription>Top customers by total value</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {businessIntelligence.customerAnalysis.slice(0, 5).map((customer, index) => (
                    <div key={customer.id} className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">{customer.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {customer.transactionCount} transactions • {customer.segment}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold">₹{customer.totalSpent.toLocaleString()}</p>
                        <p className="text-xs text-muted-foreground">
                          LTV: ₹{customer.lifetimeValue.toLocaleString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Customer Analysis</CardTitle>
              <CardDescription>Detailed customer behavior and value analysis</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Customer</TableHead>
                    <TableHead>Segment</TableHead>
                    <TableHead>Total Spent</TableHead>
                    <TableHead>Transactions</TableHead>
                    <TableHead>Avg Transaction</TableHead>
                    <TableHead>Last Purchase</TableHead>
                    <TableHead>Lifetime Value</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {businessIntelligence.customerAnalysis.slice(0, 10).map((customer) => (
                    <TableRow key={customer.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{customer.name}</div>
                          <div className="text-sm text-muted-foreground">{customer.phone}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={
                          customer.segment === "VIP" ? "default" :
                          customer.segment === "Premium" ? "secondary" :
                          "outline"
                        }>
                          {customer.segment}
                        </Badge>
                      </TableCell>
                      <TableCell className="font-medium">₹{customer.totalSpent.toLocaleString()}</TableCell>
                      <TableCell>{customer.transactionCount}</TableCell>
                      <TableCell>₹{customer.avgTransactionValue.toLocaleString()}</TableCell>
                      <TableCell>
                        <span className={customer.daysSinceLastPurchase > 90 ? "text-red-600" : "text-green-600"}>
                          {customer.daysSinceLastPurchase} days ago
                        </span>
                      </TableCell>
                      <TableCell className="font-medium text-blue-600">
                        ₹{customer.lifetimeValue.toLocaleString()}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Product Analytics Tab */}
        <TabsContent value="products" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Product Performance Analysis</CardTitle>
              <CardDescription>Revenue and profitability by product categories</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Category</TableHead>
                    <TableHead>Revenue</TableHead>
                    <TableHead>Quantity Sold</TableHead>
                    <TableHead>Avg Price</TableHead>
                    <TableHead>Profit</TableHead>
                    <TableHead>Margin</TableHead>
                    <TableHead>Performance</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {Object.entries(businessIntelligence.productPerformance)
                    .sort(([,a], [,b]) => b.revenue - a.revenue)
                    .map(([category, perf]) => (
                    <TableRow key={category}>
                      <TableCell className="font-medium capitalize">{category}</TableCell>
                      <TableCell>₹{perf.revenue.toLocaleString()}</TableCell>
                      <TableCell>{perf.quantity}</TableCell>
                      <TableCell>₹{perf.avgPrice.toLocaleString()}</TableCell>
                      <TableCell className="text-green-600">₹{perf.profit.toLocaleString()}</TableCell>
                      <TableCell>{(perf.profitMargin * 100).toFixed(1)}%</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Progress
                            value={(perf.revenue / Math.max(...Object.values(businessIntelligence.productPerformance).map(p => p.revenue))) * 100}
                            className="h-2 w-16"
                          />
                          <Badge variant={perf.revenue > 100000 ? "default" : "secondary"}>
                            {perf.revenue > 100000 ? "High" : "Medium"}
                          </Badge>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Forecasting Tab */}
        <TabsContent value="forecasting" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Next Month Forecast</CardTitle>
                <CardDescription>Projected revenue for next month</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600">
                    ₹{businessIntelligence.forecastedRevenue.nextMonth.toLocaleString()}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Based on {businessIntelligence.avgGrowthRate.toFixed(1)}% growth trend
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>3-Month Forecast</CardTitle>
                <CardDescription>Projected revenue for next quarter</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600">
                    ₹{businessIntelligence.forecastedRevenue.next3Months.toLocaleString()}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Quarterly projection
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>6-Month Forecast</CardTitle>
                <CardDescription>Projected revenue for next 6 months</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-600">
                    ₹{businessIntelligence.forecastedRevenue.next6Months.toLocaleString()}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Long-term projection
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Predictive Insights</CardTitle>
              <CardDescription>AI-powered business predictions and recommendations</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 className="font-semibold text-blue-900 mb-2">Revenue Prediction</h4>
                  <p className="text-sm text-blue-800">
                    Based on current trends, your business is projected to grow at {businessIntelligence.avgGrowthRate.toFixed(1)}% monthly.
                    This suggests strong market position and customer satisfaction.
                  </p>
                </div>

                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <h4 className="font-semibold text-green-900 mb-2">Customer Insights</h4>
                  <p className="text-sm text-green-800">
                    Your customer retention rate of {businessIntelligence.customerRetentionRate.toFixed(1)}% indicates strong customer loyalty.
                    Focus on reactivating {businessIntelligence.totalCustomers - businessIntelligence.activeCustomers} inactive customers.
                  </p>
                </div>

                <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                  <h4 className="font-semibold text-purple-900 mb-2">Product Recommendations</h4>
                  <p className="text-sm text-purple-800">
                    {Object.entries(businessIntelligence.productPerformance)
                      .sort(([,a], [,b]) => b.revenue - a.revenue)
                      .slice(0, 2)
                      .map(([category]) => category)
                      .join(" and ")} are your top-performing categories.
                    Consider expanding inventory in these areas.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
