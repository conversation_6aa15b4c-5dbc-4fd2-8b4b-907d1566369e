import { BaseService } from '../base-service'
import { Scheme } from '../../types'
import { RowDataPacket } from 'mysql2'

export class SchemeService extends BaseService<Scheme> {
  protected tableName = 'schemes'

  async findById(id: string): Promise<Scheme | null> {
    const sql = `
      SELECT s.*, c.id as customer_id,
             CONCAT(COALESCE(c.first_name, ''), ' ', COALESCE(c.last_name, '')) as customer_name,
             c.phone as customer_phone, c.email as customer_email,
             CONCAT(COALESCE(c.address_line1, ''), ' ', COALESCE(c.address_line2, '')) as customer_address,
             c.gst_number as customer_gst_number, c.total_purchases as customer_total_purchases,
             c.last_visit as customer_last_visit, c.created_at as customer_created_at,
             c.updated_at as customer_updated_at
      FROM ${this.tableName} s
      LEFT JOIN customers c ON s.customer_id = c.id
      WHERE s.id = ?
    `
    const rows = await this.executeQuery<RowDataPacket[]>(sql, [id])
    
    if (rows.length === 0) return null
    
    return this.transformSchemeRow(rows[0])
  }

  async findAll(conditions: Record<string, any> = {}): Promise<Scheme[]> {
    const { sql: whereClause, params } = this.buildWhereClause(conditions)
    const sql = `
      SELECT s.*, c.id as customer_id,
             CONCAT(COALESCE(c.first_name, ''), ' ', COALESCE(c.last_name, '')) as customer_name,
             c.phone as customer_phone, c.email as customer_email,
             CONCAT(COALESCE(c.address_line1, ''), ' ', COALESCE(c.address_line2, '')) as customer_address,
             c.gst_number as customer_gst_number, c.total_purchases as customer_total_purchases,
             c.last_visit as customer_last_visit, c.created_at as customer_created_at,
             c.updated_at as customer_updated_at
      FROM ${this.tableName} s
      LEFT JOIN customers c ON s.customer_id = c.id
      ${whereClause}
      ORDER BY s.created_at DESC
    `
    const rows = await this.executeQuery<RowDataPacket[]>(sql, params)
    
    return rows.map(row => this.transformSchemeRow(row))
  }

  private transformSchemeRow(row: any): Scheme {
    return {
      id: row.id,
      name: row.scheme_name || row.name || 'Unknown Scheme',
      customer: {
        id: row.customer_id,
        name: (row.customer_name || '').trim() || 'Walk-in Customer',
        phone: row.customer_phone || '',
        email: row.customer_email || '',
        address: (row.customer_address || '').trim() || '',
        gstNumber: row.customer_gst_number || '',
        totalPurchases: parseFloat(row.customer_total_purchases) || 0,
        lastVisit: row.customer_last_visit || '',
        createdAt: row.customer_created_at || '',
        updatedAt: row.customer_updated_at || ''
      },
      totalAmount: parseFloat(row.total_amount) || 0,
      paidAmount: parseFloat(row.paid_amount) || 0,
      monthlyAmount: parseFloat(row.monthly_amount) || 0,
      duration: parseInt(row.duration) || 0,
      startDate: row.start_date || '',
      status: row.status || 'active',
      createdAt: row.created_at || '',
      updatedAt: row.updated_at || ''
    }
  }

  async create(data: Omit<Scheme, 'id' | 'createdAt' | 'updatedAt'>): Promise<Scheme> {
    const id = this.generateId()
    const now = new Date().toISOString()
    
    const schemeData = {
      id,
      name: data.name,
      customer_id: data.customer.id,
      total_amount: data.totalAmount,
      paid_amount: data.paidAmount,
      monthly_amount: data.monthlyAmount,
      duration: data.duration,
      start_date: data.startDate,
      status: data.status,
      created_at: now,
      updated_at: now
    }

    const keys = Object.keys(schemeData)
    const values = Object.values(schemeData)
    const placeholders = keys.map(() => '?').join(', ')
    
    const sql = `INSERT INTO ${this.tableName} (${keys.join(', ')}) VALUES (${placeholders})`
    await this.executeUpdate(sql, values)
    
    return this.findById(id) as Promise<Scheme>
  }

  async addPayment(schemeId: string, amount: number): Promise<boolean> {
    const sql = `UPDATE ${this.tableName} SET paid_amount = paid_amount + ?, updated_at = ? WHERE id = ?`
    const result = await this.executeUpdate(sql, [amount, new Date().toISOString(), schemeId])
    
    // Check if scheme is completed
    if (result.affectedRows > 0) {
      await this.checkAndUpdateStatus(schemeId)
    }
    
    return result.affectedRows > 0
  }

  private async checkAndUpdateStatus(schemeId: string): Promise<void> {
    const scheme = await this.findById(schemeId)
    if (!scheme) return

    if (scheme.paidAmount >= scheme.totalAmount) {
      await this.update(schemeId, { status: 'completed' })
    }
  }

  async getActiveSchemes(): Promise<Scheme[]> {
    return this.findAll({ status: 'active' })
  }

  async getSchemesByCustomer(customerId: string): Promise<Scheme[]> {
    return this.findAll({ customer_id: customerId })
  }

  async getDueSchemes(): Promise<Scheme[]> {
    // Get schemes where next payment is due (simplified logic)
    const sql = `
      SELECT s.*, c.id as customer_id, c.name as customer_name, c.phone as customer_phone, 
             c.email as customer_email, c.address as customer_address, c.gst_number as customer_gst_number,
             c.total_purchases as customer_total_purchases, c.last_visit as customer_last_visit,
             c.created_at as customer_created_at, c.updated_at as customer_updated_at
      FROM ${this.tableName} s
      LEFT JOIN customers c ON s.customer_id = c.id
      WHERE s.status = 'active' AND s.paid_amount < s.total_amount
      ORDER BY s.start_date ASC
    `
    const rows = await this.executeQuery<RowDataPacket[]>(sql)
    
    return rows.map(row => this.transformSchemeRow(row))
  }

  async getSchemeStats(): Promise<{
    totalSchemes: number
    activeSchemes: number
    completedSchemes: number
    totalValue: number
    totalCollected: number
  }> {
    const totalSchemes = await this.count()
    const activeSchemes = await this.count({ status: 'active' })
    const completedSchemes = await this.count({ status: 'completed' })

    const valueResult = await this.executeQuery(
      `SELECT SUM(total_amount) as total_value, SUM(paid_amount) as total_collected FROM ${this.tableName}`
    )

    return {
      totalSchemes,
      activeSchemes,
      completedSchemes,
      totalValue: valueResult[0]?.total_value || 0,
      totalCollected: valueResult[0]?.total_collected || 0
    }
  }
}
