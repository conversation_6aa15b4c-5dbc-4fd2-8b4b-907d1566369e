/**
 * Client-Side Professional ID Generator
 * Generates preview IDs and formats for client-side components
 * Does not require database access - for preview purposes only
 */

// Business configuration
const BUSINESS_CODE = 'SJ' // Shree Jewellers
const CURRENT_YEAR = new Date().getFullYear()
const FINANCIAL_YEAR = CURRENT_YEAR >= 4 ? `${CURRENT_YEAR}-${String(CURRENT_YEAR + 1).slice(-2)}` : `${CURRENT_YEAR - 1}-${String(CURRENT_YEAR).slice(-2)}`

// Enhanced category codes for Indian jewelry
export const JEWELRY_CATEGORIES = {
  // Traditional Categories
  'Rings': 'RG',
  'Necklaces': 'NK',
  'Earrings': 'ER',
  'Bracelets': 'BR',
  'Pendants': 'PD',
  'Chains': 'CH',
  'Bangles': 'BG',
  'Anklets': 'AN',
  'Sets': 'ST',
  
  // Indian Traditional
  'Mangalsutra': 'MS',
  'Nose Rings': 'NR',
  'Toe Rings': 'TR',
  'Armlets': 'AR',
  'Waist Chains': 'WC',
  'Hair Ornaments': 'HO',
  '<PERSON><PERSON>': 'MT',
  'Nath': 'NT',
  'Payal': 'PY',
  'Hasli': 'HS',
  'Kadas': 'KD',
  'Jhumkas': 'JH',
  'Chandbali': 'CB',
  'Kundan': 'KN',
  'Meenakari': 'MK',
  'Temple Jewelry': 'TJ',
  
  // Modern Categories
  'Brooches': 'BC',
  'Cufflinks': 'CF',
  'Tie Pins': 'TP',
  'Watches': 'WT',
  'Watch Straps': 'WS',
  
  // Stones & Materials
  'Diamonds': 'DM',
  'Precious Stones': 'PS',
  'Semi Precious': 'SP',
  'Pearls': 'PR',
  'Coral': 'CR',
  'Rudraksha': 'RD'
} as const

export const METAL_CODES = {
  'gold': 'GD',
  'silver': 'SL',
  'platinum': 'PT',
  'diamond': 'DM',
  'white_gold': 'WG',
  'rose_gold': 'RG',
  'mixed': 'MX',
  'other': 'OT'
} as const

export const PURITY_CODES = {
  '24K': '24',
  '22K': '22',
  '18K': '18',
  '14K': '14',
  '10K': '10',
  '999': '999',
  '925': '925',
  '916': '916',
  '750': '750',
  '585': '585',
  '950': '950'
} as const

// HSN codes for different jewelry categories
export const HSN_CODES = {
  'Rings': '71131900',
  'Necklaces': '71131900',
  'Earrings': '71131900',
  'Bracelets': '71131900',
  'Pendants': '71131900',
  'Chains': '71131900',
  'Bangles': '71131900',
  'Anklets': '71131900',
  'Sets': '71131900',
  'Mangalsutra': '71131900',
  'Nose Rings': '71131900',
  'Toe Rings': '71131900',
  'Armlets': '71131900',
  'Waist Chains': '71131900',
  'Hair Ornaments': '71131900',
  'Maang Tikka': '71131900',
  'Nath': '71131900',
  'Payal': '71131900',
  'Hasli': '71131900',
  'Kadas': '71131900',
  'Jhumkas': '71131900',
  'Chandbali': '71131900',
  'Kundan': '71131900',
  'Meenakari': '71131900',
  'Temple Jewelry': '71131900',
  'Brooches': '71131900',
  'Cufflinks': '71131900',
  'Tie Pins': '71131900',
  'Watches': '91011000',
  'Watch Straps': '91131000',
  'Diamonds': '71023100',
  'Precious Stones': '71031000',
  'Semi Precious': '71039100',
  'Pearls': '71011000',
  'Coral': '71039900',
  'Rudraksha': '71171900'
} as const

/**
 * Generate preview customer ID format
 */
export function generateCustomerIdPreview(): string {
  return `CUST-${CURRENT_YEAR}-XXXXXX`
}

/**
 * Generate preview inventory item code
 */
export function generateInventoryItemCodePreview(
  category: string,
  metalType: string,
  purity: string
): string {
  const categoryCode = JEWELRY_CATEGORIES[category as keyof typeof JEWELRY_CATEGORIES] || 'GN'
  const metalCode = METAL_CODES[metalType.toLowerCase() as keyof typeof METAL_CODES] || 'OT'
  const purityCode = PURITY_CODES[purity as keyof typeof PURITY_CODES] || purity.replace(/[^0-9]/g, '').slice(0, 3)
  
  return `${categoryCode}${metalCode}${purityCode}-${CURRENT_YEAR}-XXXX`
}

/**
 * Generate preview barcode
 */
export function generateBarcodePreview(
  category: string,
  metalType: string,
  purity: string
): string {
  // Category number (01-99)
  const categoryKeys = Object.keys(JEWELRY_CATEGORIES)
  const categoryIndex = categoryKeys.indexOf(category) + 1
  const categoryNum = String(categoryIndex || 99).padStart(2, '0')
  
  // Metal number (1-9)
  const metalKeys = Object.keys(METAL_CODES)
  const metalIndex = metalKeys.indexOf(metalType.toLowerCase()) + 1
  const metalNum = String(metalIndex || 9).slice(-1)
  
  // Purity (2-3 digits)
  const purityCode = PURITY_CODES[purity as keyof typeof PURITY_CODES] || purity.replace(/[^0-9]/g, '').slice(0, 3)
  
  return `${BUSINESS_CODE}${categoryNum}${metalNum}${purityCode}XXXXXX`
}

/**
 * Get HSN code for category
 */
export function getHSNCode(category: string, metalType?: string): string {
  return HSN_CODES[category as keyof typeof HSN_CODES] || '71131900'
}

/**
 * Generate preview invoice number
 */
export function generateInvoiceNumberPreview(): string {
  return `INV-${FINANCIAL_YEAR}-XXXXXX`
}

/**
 * Generate preview sale ID
 */
export function generateSaleIdPreview(): string {
  return `SALE-${CURRENT_YEAR}-XXXXXX`
}

/**
 * Generate preview repair job number
 */
export function generateRepairJobNumberPreview(): string {
  return `REP-${FINANCIAL_YEAR}-XXXXXX`
}

/**
 * Generate preview scheme number
 */
export function generateSchemeNumberPreview(): string {
  return `SCH-${FINANCIAL_YEAR}-XXXXXX`
}

/**
 * Generate preview purchase order number
 */
export function generatePurchaseOrderNumberPreview(): string {
  return `PO-${FINANCIAL_YEAR}-XXXXXX`
}

/**
 * Generate preview supplier code
 */
export function generateSupplierCodePreview(): string {
  return `SUPP-${CURRENT_YEAR}-XXXX`
}

/**
 * Generate preview exchange number
 */
export function generateExchangeNumberPreview(): string {
  return `EXG-${FINANCIAL_YEAR}-XXXXXX`
}

/**
 * Generate preview receipt number
 */
export function generateReceiptNumberPreview(): string {
  return `RCP-${FINANCIAL_YEAR}-XXXXXX`
}

/**
 * Parse inventory item code to extract components
 */
export function parseInventoryItemCode(itemCode: string): {
  category: string
  metal: string
  purity: string
  year: string
  sequence: string
  isValid: boolean
} {
  const match = itemCode.match(/^([A-Z]{2})([A-Z]{2})(\d{2,3})-(\d{4})-(\d{4})$/)
  
  if (!match) {
    return { category: '', metal: '', purity: '', year: '', sequence: '', isValid: false }
  }
  
  const [, categoryCode, metalCode, purityCode, year, sequence] = match
  
  // Reverse lookup
  const category = Object.keys(JEWELRY_CATEGORIES).find(
    key => JEWELRY_CATEGORIES[key as keyof typeof JEWELRY_CATEGORIES] === categoryCode
  ) || 'Unknown'
  
  const metal = Object.keys(METAL_CODES).find(
    key => METAL_CODES[key as keyof typeof METAL_CODES] === metalCode
  ) || 'unknown'
  
  return { category, metal, purity: purityCode, year, sequence, isValid: true }
}

/**
 * Validate ID formats (client-side validation)
 */
export function validateCustomerId(id: string): boolean {
  return /^CUST-\d{4}-\d{6}$/.test(id)
}

export function validateInvoiceNumber(invoice: string): boolean {
  return /^INV-\d{4}-\d{2}-\d{6}$/.test(invoice)
}

export function validateRepairJobNumber(jobNumber: string): boolean {
  return /^REP-\d{4}-\d{2}-\d{6}$/.test(jobNumber)
}

export function validateInventoryItemCode(itemCode: string): boolean {
  return /^[A-Z]{2}[A-Z]{2}\d{2,3}-\d{4}-\d{4}$/.test(itemCode)
}

/**
 * Format ID for display
 */
export function formatIdForDisplay(id: string, type: 'customer' | 'inventory' | 'invoice' | 'repair' | 'scheme'): string {
  switch (type) {
    case 'customer':
      return id.replace(/^CUST-(\d{4})-(\d{6})$/, 'Customer #$2 ($1)')
    case 'inventory':
      const parsed = parseInventoryItemCode(id)
      if (parsed.isValid) {
        return `${parsed.category} - ${parsed.metal.toUpperCase()} ${parsed.purity} - #${parsed.sequence}`
      }
      return id
    case 'invoice':
      return id.replace(/^INV-(\d{4}-\d{2})-(\d{6})$/, 'Invoice #$2 (FY $1)')
    case 'repair':
      return id.replace(/^REP-(\d{4}-\d{2})-(\d{6})$/, 'Repair Job #$2 (FY $1)')
    case 'scheme':
      return id.replace(/^SCH-(\d{4}-\d{2})-(\d{6})$/, 'Scheme #$2 (FY $1)')
    default:
      return id
  }
}

/**
 * Get current financial year
 */
export function getCurrentFinancialYear(): string {
  return FINANCIAL_YEAR
}

/**
 * Get business code
 */
export function getBusinessCode(): string {
  return BUSINESS_CODE
}
