/**
 * Professional ID Generator for Jewelry Business
 * Generates business-standard IDs for all entities in the jewelry management system
 * All IDs follow professional jewelry business conventions
 */

// Business configuration
const BUSINESS_CODE = 'SJ' // Shree Jewellers - can be configured per business
const CURRENT_YEAR = new Date().getFullYear()
const FINANCIAL_YEAR = CURRENT_YEAR >= 4 ? `${CURRENT_YEAR}-${String(CURRENT_YEAR + 1).slice(-2)}` : `${CURRENT_YEAR - 1}-${String(CURRENT_YEAR).slice(-2)}`

// Sequence counters (in production, these should be stored in database)
let sequenceCounters = {
  customer: 1,
  inventory: 1,
  sale: 1,
  repair: 1,
  scheme: 1,
  purchase: 1,
  supplier: 1,
  exchange: 1,
  invoice: 1,
  receipt: 1,
  estimate: 1
}

/**
 * CUSTOMER ID GENERATION
 * Format: CUST-YYYY-NNNNNN
 * Example: CUST-2024-000001, CUST-2024-000002
 */
export function generateCustomerId(sequence?: number): string {
  const seq = sequence || sequenceCounters.customer++
  return `CUST-${CURRENT_YEAR}-${String(seq).padStart(6, '0')}`
}

/**
 * INVENTORY ITEM CODE GENERATION
 * Format: [CATEGORY][METAL][PURITY]-[YYYY]-[NNNN]
 * Example: RGGD22-2024-0001 (Ring, Gold, 22K, 2024, sequence 0001)
 */

// Enhanced category codes for Indian jewelry
export const JEWELRY_CATEGORIES = {
  // Traditional Categories
  'Rings': 'RG',
  'Necklaces': 'NK',
  'Earrings': 'ER',
  'Bracelets': 'BR',
  'Pendants': 'PD',
  'Chains': 'CH',
  'Bangles': 'BG',
  'Anklets': 'AN',
  'Sets': 'ST',
  
  // Indian Traditional
  'Mangalsutra': 'MS',
  'Nose Rings': 'NR',
  'Toe Rings': 'TR',
  'Armlets': 'AR',
  'Waist Chains': 'WC',
  'Hair Ornaments': 'HO',
  'Maang Tikka': 'MT',
  'Nath': 'NT',
  'Payal': 'PY',
  'Hasli': 'HS',
  'Kadas': 'KD',
  'Jhumkas': 'JH',
  'Chandbali': 'CB',
  'Kundan': 'KN',
  'Meenakari': 'MK',
  'Temple Jewelry': 'TJ',
  
  // Modern Categories
  'Brooches': 'BC',
  'Cufflinks': 'CF',
  'Tie Pins': 'TP',
  'Watches': 'WT',
  'Watch Straps': 'WS',
  
  // Stones & Materials
  'Diamonds': 'DM',
  'Precious Stones': 'PS',
  'Semi Precious': 'SP',
  'Pearls': 'PR',
  'Coral': 'CR',
  'Rudraksha': 'RD'
} as const

export const METAL_CODES = {
  'gold': 'GD',
  'silver': 'SL',
  'platinum': 'PT',
  'diamond': 'DM',
  'white_gold': 'WG',
  'rose_gold': 'RG',
  'mixed': 'MX',
  'other': 'OT'
} as const

export const PURITY_CODES = {
  '24K': '24',
  '22K': '22',
  '18K': '18',
  '14K': '14',
  '10K': '10',
  '999': '999',
  '925': '925',
  '916': '916',
  '750': '750',
  '585': '585',
  '950': '950'
} as const

export function generateInventoryItemCode(
  category: string,
  metalType: string,
  purity: string,
  sequence?: number
): string {
  const categoryCode = JEWELRY_CATEGORIES[category as keyof typeof JEWELRY_CATEGORIES] || 'GN'
  const metalCode = METAL_CODES[metalType.toLowerCase() as keyof typeof METAL_CODES] || 'OT'
  const purityCode = PURITY_CODES[purity as keyof typeof PURITY_CODES] || purity.replace(/[^0-9]/g, '').slice(0, 3)
  
  const seq = sequence || sequenceCounters.inventory++
  return `${categoryCode}${metalCode}${purityCode}-${CURRENT_YEAR}-${String(seq).padStart(4, '0')}`
}

/**
 * SALES & INVOICE GENERATION
 * Invoice Format: INV-FY-NNNNNN
 * Example: INV-2024-25-000001
 */
export function generateInvoiceNumber(sequence?: number): string {
  const seq = sequence || sequenceCounters.invoice++
  return `INV-${FINANCIAL_YEAR}-${String(seq).padStart(6, '0')}`
}

export function generateSaleId(sequence?: number): string {
  const seq = sequence || sequenceCounters.sale++
  return `SALE-${CURRENT_YEAR}-${String(seq).padStart(6, '0')}`
}

/**
 * REPAIR JOB GENERATION
 * Format: REP-FY-NNNNNN
 * Example: REP-2024-25-000001
 */
export function generateRepairJobNumber(sequence?: number): string {
  const seq = sequence || sequenceCounters.repair++
  return `REP-${FINANCIAL_YEAR}-${String(seq).padStart(6, '0')}`
}

/**
 * SCHEME GENERATION
 * Format: SCH-FY-NNNNNN
 * Example: SCH-2024-25-000001
 */
export function generateSchemeNumber(sequence?: number): string {
  const seq = sequence || sequenceCounters.scheme++
  return `SCH-${FINANCIAL_YEAR}-${String(seq).padStart(6, '0')}`
}

/**
 * PURCHASE ORDER GENERATION
 * Format: PO-FY-NNNNNN
 * Example: PO-2024-25-000001
 */
export function generatePurchaseOrderNumber(sequence?: number): string {
  const seq = sequence || sequenceCounters.purchase++
  return `PO-${FINANCIAL_YEAR}-${String(seq).padStart(6, '0')}`
}

/**
 * SUPPLIER CODE GENERATION
 * Format: SUPP-YYYY-NNNN
 * Example: SUPP-2024-0001
 */
export function generateSupplierCode(sequence?: number): string {
  const seq = sequence || sequenceCounters.supplier++
  return `SUPP-${CURRENT_YEAR}-${String(seq).padStart(4, '0')}`
}

/**
 * EXCHANGE TRANSACTION GENERATION
 * Format: EXG-FY-NNNNNN
 * Example: EXG-2024-25-000001
 */
export function generateExchangeNumber(sequence?: number): string {
  const seq = sequence || sequenceCounters.exchange++
  return `EXG-${FINANCIAL_YEAR}-${String(seq).padStart(6, '0')}`
}

/**
 * RECEIPT GENERATION
 * Format: RCP-FY-NNNNNN
 * Example: RCP-2024-25-000001
 */
export function generateReceiptNumber(sequence?: number): string {
  const seq = sequence || sequenceCounters.receipt++
  return `RCP-${FINANCIAL_YEAR}-${String(seq).padStart(6, '0')}`
}

/**
 * ESTIMATE GENERATION
 * Format: EST-FY-NNNNNN
 * Example: EST-2024-25-000001
 */
export function generateEstimateNumber(sequence?: number): string {
  const seq = sequence || sequenceCounters.estimate++
  return `EST-${FINANCIAL_YEAR}-${String(seq).padStart(6, '0')}`
}

/**
 * BARCODE GENERATION
 * Format: [BUSINESS_CODE][CATEGORY_NUM][METAL_NUM][PURITY][SEQUENCE]
 * Example: SJ01224000001 (Shree Jewellers, Ring, Gold, 22K, sequence 1)
 */
export function generateBarcode(
  category: string,
  metalType: string,
  purity: string,
  sequence?: number
): string {
  // Category number (01-99)
  const categoryKeys = Object.keys(JEWELRY_CATEGORIES)
  const categoryIndex = categoryKeys.indexOf(category) + 1
  const categoryNum = String(categoryIndex || 99).padStart(2, '0')
  
  // Metal number (1-9)
  const metalKeys = Object.keys(METAL_CODES)
  const metalIndex = metalKeys.indexOf(metalType.toLowerCase()) + 1
  const metalNum = String(metalIndex || 9).slice(-1)
  
  // Purity (2-3 digits)
  const purityCode = PURITY_CODES[purity as keyof typeof PURITY_CODES] || purity.replace(/[^0-9]/g, '').slice(0, 3)
  
  // Sequence (6 digits)
  const seq = sequence || sequenceCounters.inventory++
  const sequenceStr = String(seq).padStart(6, '0')
  
  return `${BUSINESS_CODE}${categoryNum}${metalNum}${purityCode}${sequenceStr}`
}

/**
 * UTILITY FUNCTIONS
 */

// Parse customer ID
export function parseCustomerId(customerId: string): { year: string; sequence: string; isValid: boolean } {
  const match = customerId.match(/^CUST-(\d{4})-(\d{6})$/)
  return {
    year: match?.[1] || '',
    sequence: match?.[2] || '',
    isValid: !!match
  }
}

// Parse inventory item code
export function parseInventoryItemCode(itemCode: string): {
  category: string
  metal: string
  purity: string
  year: string
  sequence: string
  isValid: boolean
} {
  const match = itemCode.match(/^([A-Z]{2})([A-Z]{2})(\d{2,3})-(\d{4})-(\d{4})$/)
  
  if (!match) {
    return { category: '', metal: '', purity: '', year: '', sequence: '', isValid: false }
  }
  
  const [, categoryCode, metalCode, purityCode, year, sequence] = match
  
  // Reverse lookup
  const category = Object.keys(JEWELRY_CATEGORIES).find(
    key => JEWELRY_CATEGORIES[key as keyof typeof JEWELRY_CATEGORIES] === categoryCode
  ) || 'Unknown'
  
  const metal = Object.keys(METAL_CODES).find(
    key => METAL_CODES[key as keyof typeof METAL_CODES] === metalCode
  ) || 'unknown'
  
  return { category, metal, purity: purityCode, year, sequence, isValid: true }
}

// Validate ID formats
export function validateCustomerId(id: string): boolean {
  return /^CUST-\d{4}-\d{6}$/.test(id)
}

export function validateInvoiceNumber(invoice: string): boolean {
  return /^INV-\d{4}-\d{2}-\d{6}$/.test(invoice)
}

export function validateRepairJobNumber(jobNumber: string): boolean {
  return /^REP-\d{4}-\d{2}-\d{6}$/.test(jobNumber)
}

// Set sequence counters (for database integration)
export function setSequenceCounters(counters: Partial<typeof sequenceCounters>): void {
  sequenceCounters = { ...sequenceCounters, ...counters }
}

// Get next sequence number
export function getNextSequence(type: keyof typeof sequenceCounters): number {
  return sequenceCounters[type]
}

// Reset sequence counters (for testing)
export function resetSequenceCounters(): void {
  Object.keys(sequenceCounters).forEach(key => {
    sequenceCounters[key as keyof typeof sequenceCounters] = 1
  })
}
