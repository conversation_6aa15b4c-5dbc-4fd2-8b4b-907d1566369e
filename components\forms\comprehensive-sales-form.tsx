"use client"

import type React from "react"
import { useState, useEffect, useMemo } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"
import { Textarea } from "@/components/ui/textarea"
import { useStore } from "@/lib/store"
import type { Sale, InventoryItem, SaleItem, Customer } from "@/lib/types"
import { 
  Plus, Trash2, Scale, Calculator, CreditCard, Receipt, 
  Percent, Gift, <PERSON>, ShoppingCart, FileText, Printer,
  IndianRupee, Gem, Award, Clock
} from "lucide-react"

interface ComprehensiveSalesFormProps {
  sale?: Sale
  onSubmit: () => void
  onCancel: () => void
}

interface EnhancedSaleItem extends SaleItem {
  discountType: "none" | "percentage" | "fixed"
  discountValue: number
  discountAmount: number
  finalAmount: number
  hsnCode: string
  gstRate: number
  cgstAmount: number
  sgstAmount: number
  igstAmount: number
}

interface PaymentDetails {
  method: "cash" | "card" | "upi" | "bank_transfer" | "cheque" | "mixed"
  cashAmount: number
  cardAmount: number
  upiAmount: number
  bankTransferAmount: number
  chequeAmount: number
  chequeNumber: string
  bankName: string
  transactionId: string
  advanceAmount: number
  balanceAmount: number
  dueDate: string
}

interface ExchangeDetails {
  hasExchange: boolean
  oldGoldWeight: number
  oldGoldPurity: string
  oldGoldRate: number
  oldGoldValue: number
  exchangeDiscount: number
  netExchangeValue: number
  exchangeItems: Array<{
    description: string
    weight: number
    purity: string
    rate: number
    value: number
  }>
}

export function ComprehensiveSalesForm({ sale, onSubmit, onCancel }: ComprehensiveSalesFormProps) {
  const { addSale, updateSale, customers, inventory, settings, getMetalRate } = useStore()
  
  // Basic form data
  const [formData, setFormData] = useState({
    customerId: "",
    date: "",
    invoiceNumber: "",
    status: "paid",
    salesPerson: "",
    notes: "",
    specialInstructions: "",
  })

  // Enhanced sale items
  const [saleItems, setSaleItems] = useState<EnhancedSaleItem[]>([])
  
  // Payment details
  const [paymentDetails, setPaymentDetails] = useState<PaymentDetails>({
    method: "cash",
    cashAmount: 0,
    cardAmount: 0,
    upiAmount: 0,
    bankTransferAmount: 0,
    chequeAmount: 0,
    chequeNumber: "",
    bankName: "",
    transactionId: "",
    advanceAmount: 0,
    balanceAmount: 0,
    dueDate: "",
  })

  // Exchange details
  const [exchangeDetails, setExchangeDetails] = useState<ExchangeDetails>({
    hasExchange: false,
    oldGoldWeight: 0,
    oldGoldPurity: "22K",
    oldGoldRate: 0,
    oldGoldValue: 0,
    exchangeDiscount: 0,
    netExchangeValue: 0,
    exchangeItems: [],
  })

  // Discount and offers
  const [globalDiscount, setGlobalDiscount] = useState({
    type: "none" as "none" | "percentage" | "fixed",
    value: 0,
    amount: 0,
  })

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [activeTab, setActiveTab] = useState("items")

  // Initialize form
  useEffect(() => {
    if (sale) {
      // Load existing sale data
      setFormData({
        customerId: sale.customer.id,
        date: sale.date,
        invoiceNumber: sale.invoiceNumber || "",
        status: sale.status,
        salesPerson: sale.salesPerson || "",
        notes: sale.notes || "",
        specialInstructions: sale.specialInstructions || "",
      })
      // Convert basic sale items to enhanced format
      const enhancedItems: EnhancedSaleItem[] = sale.items.map(item => ({
        ...item,
        discountType: "none",
        discountValue: 0,
        discountAmount: 0,
        finalAmount: item.amount,
        hsnCode: item.item.hsnCode || "",
        gstRate: 3, // Default GST rate for jewelry
        cgstAmount: 0,
        sgstAmount: 0,
        igstAmount: 0,
      }))
      setSaleItems(enhancedItems)
    } else {
      // Set default date and generate invoice number
      const today = new Date().toISOString().split("T")[0]
      const invoiceNumber = `INV-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`
      setFormData(prev => ({ ...prev, date: today, invoiceNumber }))
    }
  }, [sale])

  // Auto-calculate exchange value when exchange details change
  useEffect(() => {
    if (exchangeDetails.hasExchange) {
      const totalOldGoldValue = exchangeDetails.exchangeItems.reduce((sum, item) => sum + item.value, 0)
      const discountAmount = (totalOldGoldValue * exchangeDetails.exchangeDiscount) / 100
      const netValue = totalOldGoldValue - discountAmount
      
      setExchangeDetails(prev => ({
        ...prev,
        oldGoldValue: totalOldGoldValue,
        netExchangeValue: netValue
      }))
    }
  }, [exchangeDetails.exchangeItems, exchangeDetails.exchangeDiscount, exchangeDetails.hasExchange])

  // Helper functions
  const handleInputChange = (field: string, value: any) => {
    setFormData({ ...formData, [field]: value })
    if (errors[field]) {
      setErrors({ ...errors, [field]: "" })
    }
  }

  const addSaleItem = () => {
    const newItem: EnhancedSaleItem = {
      id: `item_${Date.now()}`,
      item: {} as InventoryItem,
      grossWeight: 0,
      stoneWeight: 0,
      netWeight: 0,
      rate: 0,
      makingCharges: 0,
      stoneAmount: 0,
      amount: 0,
      discountType: "none",
      discountValue: 0,
      discountAmount: 0,
      finalAmount: 0,
      hsnCode: "",
      gstRate: 3,
      cgstAmount: 0,
      sgstAmount: 0,
      igstAmount: 0,
    }
    setSaleItems([...saleItems, newItem])
  }

  const removeSaleItem = (index: number) => {
    setSaleItems(saleItems.filter((_, i) => i !== index))
  }

  const updateSaleItem = (index: number, field: string, value: any) => {
    const updatedItems = [...saleItems]
    const item = updatedItems[index]

    if (field === "itemId") {
      const inventoryItem = inventory.find((inv) => inv.id === value)
      if (inventoryItem) {
        item.item = inventoryItem
        item.grossWeight = Number(inventoryItem.grossWeight) || 0
        item.stoneWeight = Number(inventoryItem.stoneWeight) || 0
        item.netWeight = Number(inventoryItem.netWeight) || 0
        item.rate = Number(getMetalRate(inventoryItem.metalType, inventoryItem.purity)) || 0
        item.makingCharges = Number(inventoryItem.makingCharges) || 0
        item.stoneAmount = Number(inventoryItem.stoneAmount) || 0
        item.hsnCode = inventoryItem.hsnCode || ""
      }
    } else {
      // Handle numeric fields
      if (['grossWeight', 'stoneWeight', 'netWeight', 'rate', 'makingCharges', 'stoneAmount', 'discountValue', 'gstRate'].includes(field)) {
        (item as any)[field] = Number(value) || 0
      } else {
        (item as any)[field] = value
      }
    }

    // Recalculate weights
    if (field === "grossWeight" || field === "stoneWeight") {
      item.netWeight = Math.max(0, item.grossWeight - item.stoneWeight)
    }

    // Recalculate amounts
    const metalValue = item.netWeight * item.rate
    const totalBeforeDiscount = metalValue + item.makingCharges + item.stoneAmount
    
    // Calculate discount
    let discountAmount = 0
    if (item.discountType === "percentage") {
      discountAmount = (totalBeforeDiscount * item.discountValue) / 100
    } else if (item.discountType === "fixed") {
      discountAmount = item.discountValue
    }
    
    item.discountAmount = discountAmount
    item.amount = totalBeforeDiscount - discountAmount
    
    // Calculate GST
    const gstAmount = (item.amount * item.gstRate) / 100
    item.cgstAmount = gstAmount / 2
    item.sgstAmount = gstAmount / 2
    item.igstAmount = 0 // For inter-state transactions
    
    item.finalAmount = item.amount + gstAmount

    updatedItems[index] = item
    setSaleItems(updatedItems)
  }

  const addExchangeItem = () => {
    const newExchangeItem = {
      description: "",
      weight: 0,
      purity: "22K",
      rate: getMetalRate("gold", "22K"),
      value: 0,
    }
    
    setExchangeDetails(prev => ({
      ...prev,
      exchangeItems: [...prev.exchangeItems, newExchangeItem]
    }))
  }

  const updateExchangeItem = (index: number, field: string, value: any) => {
    const updatedItems = [...exchangeDetails.exchangeItems]
    const item = updatedItems[index]
    
    if (field === "weight" || field === "rate") {
      item[field] = Number(value) || 0
      item.value = item.weight * item.rate
    } else {
      (item as any)[field] = value
    }
    
    setExchangeDetails(prev => ({
      ...prev,
      exchangeItems: updatedItems
    }))
  }

  const removeExchangeItem = (index: number) => {
    setExchangeDetails(prev => ({
      ...prev,
      exchangeItems: prev.exchangeItems.filter((_, i) => i !== index)
    }))
  }

  // Calculate totals
  const calculations = useMemo(() => {
    const itemsSubtotal = saleItems.reduce((sum, item) => sum + item.amount, 0)
    const totalDiscount = saleItems.reduce((sum, item) => sum + item.discountAmount, 0)
    
    // Apply global discount
    let globalDiscountAmount = 0
    if (globalDiscount.type === "percentage") {
      globalDiscountAmount = (itemsSubtotal * globalDiscount.value) / 100
    } else if (globalDiscount.type === "fixed") {
      globalDiscountAmount = globalDiscount.value
    }
    
    const subtotalAfterDiscount = itemsSubtotal - globalDiscountAmount
    const totalCGST = saleItems.reduce((sum, item) => sum + item.cgstAmount, 0)
    const totalSGST = saleItems.reduce((sum, item) => sum + item.sgstAmount, 0)
    const totalIGST = saleItems.reduce((sum, item) => sum + item.igstAmount, 0)
    const totalGST = totalCGST + totalSGST + totalIGST
    
    const grandTotal = subtotalAfterDiscount + totalGST
    const netPayable = grandTotal - exchangeDetails.netExchangeValue
    const balanceAmount = netPayable - paymentDetails.advanceAmount
    
    return {
      itemsSubtotal,
      totalDiscount: totalDiscount + globalDiscountAmount,
      subtotalAfterDiscount,
      totalCGST,
      totalSGST,
      totalIGST,
      totalGST,
      grandTotal,
      exchangeValue: exchangeDetails.netExchangeValue,
      netPayable,
      advanceAmount: paymentDetails.advanceAmount,
      balanceAmount,
    }
  }, [saleItems, globalDiscount, exchangeDetails.netExchangeValue, paymentDetails.advanceAmount])

  return (
    <form className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="items" className="flex items-center gap-2">
            <ShoppingCart className="h-4 w-4" />
            Items
          </TabsTrigger>
          <TabsTrigger value="exchange" className="flex items-center gap-2">
            <Scale className="h-4 w-4" />
            Exchange
          </TabsTrigger>
          <TabsTrigger value="discounts" className="flex items-center gap-2">
            <Percent className="h-4 w-4" />
            Discounts
          </TabsTrigger>
          <TabsTrigger value="payment" className="flex items-center gap-2">
            <CreditCard className="h-4 w-4" />
            Payment
          </TabsTrigger>
          <TabsTrigger value="summary" className="flex items-center gap-2">
            <Receipt className="h-4 w-4" />
            Summary
          </TabsTrigger>
        </TabsList>

        {/* Basic Information Header */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Sale Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label htmlFor="customerId">Customer *</Label>
                <Select value={formData.customerId || undefined} onValueChange={(value) => handleInputChange("customerId", value)}>
                  <SelectTrigger className={errors.customerId ? "border-red-500" : ""}>
                    <SelectValue placeholder="Select customer" />
                  </SelectTrigger>
                  <SelectContent>
                    {customers.filter(customer => customer.id && customer.id.trim() !== "").map((customer) => (
                      <SelectItem key={customer.id} value={customer.id}>
                        <div className="flex flex-col">
                          <span>{customer.name}</span>
                          <span className="text-xs text-muted-foreground">{customer.phone}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.customerId && <p className="text-sm text-red-500">{errors.customerId}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="date">Sale Date *</Label>
                <Input
                  id="date"
                  type="date"
                  value={formData.date}
                  onChange={(e) => handleInputChange("date", e.target.value)}
                  required
                  className={errors.date ? "border-red-500" : ""}
                />
                {errors.date && <p className="text-sm text-red-500">{errors.date}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="invoiceNumber">Invoice Number</Label>
                <Input
                  id="invoiceNumber"
                  value={formData.invoiceNumber}
                  onChange={(e) => handleInputChange("invoiceNumber", e.target.value)}
                  placeholder="Auto-generated"
                  className="bg-gray-50"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select value={formData.status} onValueChange={(value) => handleInputChange("status", value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="paid">Paid</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="partial">Partial Payment</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Items Tab */}
        <TabsContent value="items" className="space-y-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <ShoppingCart className="h-5 w-5" />
                Sale Items
              </CardTitle>
              <Button type="button" onClick={addSaleItem} size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Item
              </Button>
            </CardHeader>
            <CardContent className="space-y-4">
              {errors.items && <p className="text-sm text-red-500">{errors.items}</p>}

              {saleItems.map((saleItem, index) => (
                <Card key={saleItem.id} className="p-4 border-l-4 border-l-blue-500">
                  <div className="flex justify-between items-start mb-4">
                    <h4 className="font-medium">Item {index + 1}</h4>
                    <Button type="button" variant="ghost" size="sm" onClick={() => removeSaleItem(index)}>
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div className="space-y-2">
                      <Label>Select Item *</Label>
                      <Select
                        value={saleItem.item.id || undefined}
                        onValueChange={(value) => updateSaleItem(index, "itemId", value)}
                      >
                        <SelectTrigger className={errors[`item_${index}`] ? "border-red-500" : ""}>
                          <SelectValue placeholder="Select inventory item" />
                        </SelectTrigger>
                        <SelectContent>
                          {inventory.filter(item => item.id && item.id.trim() !== "" && (item.stock || 0) > 0).map((item) => (
                            <SelectItem key={item.id} value={item.id}>
                              <div className="flex flex-col">
                                <span className="font-medium">{item.name}</span>
                                <span className="text-xs text-muted-foreground">
                                  {item.metalType} {item.purity} - {item.netWeight}g - Stock: {item.stock}
                                </span>
                                <span className="text-xs text-green-600">
                                  ₹{(item.currentValue || 0).toLocaleString()}
                                </span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {errors[`item_${index}`] && <p className="text-sm text-red-500">{errors[`item_${index}`]}</p>}
                    </div>

                    <div className="space-y-2">
                      <Label>Rate (₹/g)</Label>
                      <Input
                        type="number"
                        step="0.01"
                        value={saleItem.rate}
                        onChange={(e) => updateSaleItem(index, "rate", e.target.value)}
                        placeholder="Rate per gram"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                    <div className="space-y-2">
                      <Label>Gross Weight (g) *</Label>
                      <Input
                        type="number"
                        step="0.001"
                        min="0"
                        value={saleItem.grossWeight}
                        onChange={(e) => updateSaleItem(index, "grossWeight", e.target.value)}
                        placeholder="25.500"
                        className={errors[`grossWeight_${index}`] ? "border-red-500" : ""}
                      />
                      {errors[`grossWeight_${index}`] && (
                        <p className="text-sm text-red-500">{errors[`grossWeight_${index}`]}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label>Stone Weight (g)</Label>
                      <Input
                        type="number"
                        step="0.001"
                        min="0"
                        value={saleItem.stoneWeight}
                        onChange={(e) => updateSaleItem(index, "stoneWeight", e.target.value)}
                        placeholder="2.500"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Net Weight (g)</Label>
                      <Input
                        type="number"
                        step="0.001"
                        value={saleItem.netWeight}
                        readOnly
                        className="bg-gray-50"
                        placeholder="Auto-calculated"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>HSN Code</Label>
                      <Input
                        value={saleItem.hsnCode}
                        onChange={(e) => updateSaleItem(index, "hsnCode", e.target.value)}
                        placeholder="71131900"
                        className="bg-gray-50"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                    <div className="space-y-2">
                      <Label>Making Charges (₹)</Label>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        value={saleItem.makingCharges}
                        onChange={(e) => updateSaleItem(index, "makingCharges", e.target.value)}
                        placeholder="8500"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Stone Amount (₹)</Label>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        value={saleItem.stoneAmount}
                        onChange={(e) => updateSaleItem(index, "stoneAmount", e.target.value)}
                        placeholder="15000"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>GST Rate (%)</Label>
                      <Select
                        value={saleItem.gstRate.toString()}
                        onValueChange={(value) => updateSaleItem(index, "gstRate", value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="0">0% (Exempt)</SelectItem>
                          <SelectItem value="3">3% (Standard)</SelectItem>
                          <SelectItem value="18">18% (Other)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Final Amount</Label>
                      <div className="p-2 bg-green-50 border border-green-200 rounded-md">
                        <p className="font-semibold text-green-700">
                          ₹{(saleItem.finalAmount || 0).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Item Discount Section */}
                  <div className="border-t pt-4">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div className="space-y-2">
                        <Label>Discount Type</Label>
                        <Select
                          value={saleItem.discountType}
                          onValueChange={(value) => updateSaleItem(index, "discountType", value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="none">No Discount</SelectItem>
                            <SelectItem value="percentage">Percentage (%)</SelectItem>
                            <SelectItem value="fixed">Fixed Amount (₹)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      {saleItem.discountType !== "none" && (
                        <>
                          <div className="space-y-2">
                            <Label>
                              Discount Value {saleItem.discountType === "percentage" ? "(%)" : "(₹)"}
                            </Label>
                            <Input
                              type="number"
                              step="0.01"
                              min="0"
                              value={saleItem.discountValue}
                              onChange={(e) => updateSaleItem(index, "discountValue", e.target.value)}
                              placeholder={saleItem.discountType === "percentage" ? "10" : "1000"}
                            />
                          </div>

                          <div className="space-y-2">
                            <Label>Discount Amount</Label>
                            <div className="p-2 bg-red-50 border border-red-200 rounded-md">
                              <p className="font-semibold text-red-700">
                                -₹{(saleItem.discountAmount || 0).toLocaleString()}
                              </p>
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  </div>

                  {saleItem.item.id && (
                    <div className="flex gap-2 text-xs text-muted-foreground mt-4">
                      <Badge variant="outline">{saleItem.item.category}</Badge>
                      <Badge variant="outline">
                        {saleItem.item.metalType} {saleItem.item.purity}
                      </Badge>
                      <Badge variant="outline">Stock: {saleItem.item.stock}</Badge>
                      {saleItem.item.bisHallmark && (
                        <Badge variant="outline" className="bg-green-50">BIS</Badge>
                      )}
                    </div>
                  )}
                </Card>
              ))}

              {saleItems.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  <ShoppingCart className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No items added yet. Click "Add Item" to start.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
